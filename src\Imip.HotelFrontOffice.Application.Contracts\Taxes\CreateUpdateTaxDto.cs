using System;
using System.ComponentModel.DataAnnotations;

namespace Imip.HotelFrontOffice.Taxes;

public class CreateUpdateTaxDto
{
    [Required]
    public string Name { get; set; } = default!;
    [Required]
    public string Code { get; set; } = default!;
    [Required]
    public decimal? Rate { get; set; }
    [Required]
    public DateOnly? StartDate { get; set; } = default!;
    [Required]
    public DateOnly? EndDate { get; set; } = default!;
    public bool IsActive { get; set; } = true;
}
