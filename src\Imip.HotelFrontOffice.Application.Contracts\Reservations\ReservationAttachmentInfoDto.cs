using System;

namespace Imip.HotelFrontOffice.Reservations;

/// <summary>
/// DTO for reservation attachment information in responses
/// </summary>
public class ReservationAttachmentInfoDto
{
    /// <summary>
    /// The unique identifier for the attachment
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// The name of the file
    /// </summary>
    public string? FileName { get; set; }

    /// <summary>
    /// The content type of the file
    /// </summary>
    public string? ContentType { get; set; }

    /// <summary>
    /// The size of the file in bytes
    /// </summary>
    public long Size { get; set; }

    /// <summary>
    /// The URL to download the file
    /// </summary>
    public string? Url { get; set; }

    /// <summary>
    /// The URL to stream/view the file in browser
    /// </summary>
    public string? StreamUrl { get; set; }

    /// <summary>
    /// Optional description of the file
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// The date and time when the attachment was created
    /// </summary>
    public DateTime CreationTime { get; set; }
}
