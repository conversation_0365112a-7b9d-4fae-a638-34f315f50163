using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Volo.Abp;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.HotelFrontOffice.Reports;

[RemoteService(false)]
public class ReportExecutionService : ApplicationService, IReportExecutionService
{
    private readonly IRepository<Report, Guid> _reportRepository;
    private readonly IDbContextProvider<HotelFrontOfficeDbContext> _dbContextProvider;

    public ReportExecutionService(
        IRepository<Report, Guid> reportRepository,
        IDbContextProvider<HotelFrontOfficeDbContext> dbContextProvider)
    {
        _reportRepository = reportRepository;
        _dbContextProvider = dbContextProvider;
    }

    public async Task<ReportPreviewDto> ExecuteReportAsync(Guid reportId, Dictionary<string, object> parameters)
    {
        var report = await _reportRepository.GetAsync(reportId);

        if (!report.IsActive)
            throw new BusinessException("Report is not active");

        var dbContext = await _dbContextProvider.GetDbContextAsync();
        var connection = dbContext.Database.GetDbConnection();

        var processedQuery = ProcessQueryParameters(report.Query, parameters);

        try
        {
            await connection.OpenAsync();

            using var command = connection.CreateCommand();
            command.CommandText = processedQuery;
            command.CommandTimeout = 300; // 5 minutes timeout

            // Set command type based on report type
            switch (report.QueryType)
            {
                case ReportQueryType.StoredProcedure:
                    command.CommandType = CommandType.StoredProcedure;
                    AddParametersToCommand(command, parameters);
                    break;
                case ReportQueryType.RawQuery:
                case ReportQueryType.View:
                    command.CommandType = CommandType.Text;
                    break;
            }

            using var reader = await command.ExecuteReaderAsync();

            var result = new ReportPreviewDto
            {
                ReportName = report.Name,
                ExecutedAt = DateTime.UtcNow
            };

            // Get column names
            for (int i = 0; i < reader.FieldCount; i++)
            {
                result.Columns.Add(reader.GetName(i));
            }

            // Read data
            while (await reader.ReadAsync())
            {
                var row = new Dictionary<string, object>();
                for (int i = 0; i < reader.FieldCount; i++)
                {
                    var value = reader.IsDBNull(i) ? null : reader.GetValue(i);
                    row[reader.GetName(i)] = value;
                }
                result.Data.Add(row);
            }

            result.TotalRows = result.Data.Count;
            return result;
        }
        finally
        {
            await connection.CloseAsync();
        }
    }

    public async Task<byte[]> ExportReportToCsvAsync(Guid reportId, Dictionary<string, object> parameters)
    {
        var reportData = await ExecuteReportAsync(reportId, parameters);

        var csv = new StringBuilder();

        // Add headers
        csv.AppendLine(string.Join(",", reportData.Columns.Select(EscapeCsvValue)));

        // Add data rows
        foreach (var row in reportData.Data)
        {
            var values = reportData.Columns.Select(col =>
                EscapeCsvValue(row.ContainsKey(col) ? row[col]?.ToString() ?? "" : ""));
            csv.AppendLine(string.Join(",", values));
        }

        return Encoding.UTF8.GetBytes(csv.ToString());
    }

    public async Task<byte[]> ExportReportToExcelAsync(Guid reportId, Dictionary<string, object> parameters)
    {
        var reportData = await ExecuteReportAsync(reportId, parameters);

        // Basic Excel export using CSV format with Excel headers
        // For more advanced Excel features, consider using EPPlus or similar library
        var csv = new StringBuilder();
        csv.AppendLine("sep=,"); // Excel separator hint

        // Add headers
        csv.AppendLine(string.Join(",", reportData.Columns.Select(EscapeCsvValue)));

        // Add data rows
        foreach (var row in reportData.Data)
        {
            var values = reportData.Columns.Select(col =>
                EscapeCsvValue(row.ContainsKey(col) ? row[col]?.ToString() ?? "" : ""));
            csv.AppendLine(string.Join(",", values));
        }

        return Encoding.UTF8.GetBytes(csv.ToString());
    }

    public async Task<List<ReportParameterDto>> GetReportParametersAsync(Guid reportId)
    {
        var report = await _reportRepository.GetAsync(reportId);

        if (string.IsNullOrEmpty(report.Parameters))
            return new List<ReportParameterDto>();

        try
        {
            return JsonSerializer.Deserialize<List<ReportParameterDto>>(report.Parameters) ?? new List<ReportParameterDto>();
        }
        catch
        {
            return new List<ReportParameterDto>();
        }
    }

    private string ProcessQueryParameters(string query, Dictionary<string, object> parameters)
    {
        if (parameters == null || !parameters.Any())
            return query;

        var processedQuery = query;

        foreach (var param in parameters)
        {
            var placeholder = $"{{{{{param.Key}}}}}";
            var value = param.Value?.ToString() ?? "";

            // Basic SQL injection protection - escape single quotes
            value = value.Replace("'", "''");

            processedQuery = processedQuery.Replace(placeholder, value);
        }

        return processedQuery;
    }

    private void AddParametersToCommand(IDbCommand command, Dictionary<string, object> parameters)
    {
        if (parameters == null) return;

        foreach (var param in parameters)
        {
            var parameter = command.CreateParameter();
            parameter.ParameterName = $"@{param.Key}";
            parameter.Value = param.Value ?? DBNull.Value;
            command.Parameters.Add(parameter);
        }
    }

    private static string EscapeCsvValue(string value)
    {
        if (string.IsNullOrEmpty(value))
            return "\"\"";

        if (value.Contains(",") || value.Contains("\"") || value.Contains("\n") || value.Contains("\r"))
        {
            return "\"" + value.Replace("\"", "\"\"") + "\"";
        }

        return value;
    }
}