﻿using System;
using System.Collections.Generic;
using Imip.HotelFrontOffice.MasterStatuses;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.HotelFrontOffice.Guests;

public class Guest : FullAuditedAggregateRoot<Guid>
{
    public string? Fullname { get; set; } // Changed from FirstName
    public string? IdentityNumber { get; set; } // Changed from IdentifyNumber
    public string? PhoneNumber { get; set; }
    public string? Email { get; set; }
    public string? Nationality { get; set; }
    public string? CompanyName { get; set; }
    public string? Attachment { get; set; }
    public Guid? StatusId { get; set; }
    public virtual ICollection<Payments.Payment> Payments { get; set; }
    public virtual MasterStatus? Status { get; set; }
    public virtual ICollection<ReservationDetails.ReservationDetail> ReservationDetails { get; set; }
    public virtual ICollection<PaymentGuests.PaymentGuest> PaymentGuests { get; set; }


    protected Guest()
    {
        Payments = new HashSet<Payments.Payment>();
        ReservationDetails = new HashSet<ReservationDetails.ReservationDetail>();
        PaymentGuests = new HashSet<PaymentGuests.PaymentGuest>();
    }

    public Guest(Guid id, string fullName, string lastName, string identityNumber, string phoneNumber, string email,
        string nationality, string companyName, string attachment, Guid? statusId) : this()
    {
        Id = id;
        Fullname = fullName;
        IdentityNumber = identityNumber;
        PhoneNumber = phoneNumber;
        Email = email;
        Nationality = nationality;
        CompanyName = companyName;
        Attachment = attachment;
        StatusId = statusId;
    }
}