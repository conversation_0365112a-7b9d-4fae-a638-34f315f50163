﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Attachments;
using Imip.HotelFrontOffice.Guests;
using Imip.HotelFrontOffice.Permissions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Data;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Uow;

namespace Imip.HotelFrontOffice.Reservations;

[Authorize(WismaAppPermissions.PolicyReservation.Default)]
public class ReservationsAppService : PermissionCheckedCrudAppService<
    Reservation,
    ReservationsDto,
    Guid,
    PagedAndSortedResultRequestDto,
    CreateUpdateReservationsDto,
    CreateUpdateReservationsDto
>, IReservationsAppService
{
    private readonly IRepository<Reservation, Guid> _repository;
    private readonly ReservationDetailService _reservationDetailService;
    private readonly IReservationCodeGeneratorService _codeGeneratorService;
    private readonly IAttachmentAppService _attachmentAppService;
    private readonly ILogger<ReservationsAppService> _logger;
    private readonly IUnitOfWorkManager _unitOfWorkManager;

    public ReservationsAppService(
        IRepository<Reservation, Guid> repository,
        ReservationDetailService reservationDetailService,
        IReservationCodeGeneratorService codeGeneratorService,
        ILogger<ReservationsAppService> logger,
        IAttachmentAppService attachmentAppService,
        IPermissionChecker permissionChecker,
        IUnitOfWorkManager unitOfWorkManager)
        : base(repository, permissionChecker)
    {
        _repository = repository;
        _reservationDetailService = reservationDetailService;
        _codeGeneratorService = codeGeneratorService;
        _attachmentAppService = attachmentAppService;
        _logger = logger;
        _unitOfWorkManager = unitOfWorkManager;

        GetPolicyName = WismaAppPermissions.PolicyReservation.View;
        GetListPolicyName = WismaAppPermissions.PolicyReservation.View;
        CreatePolicyName = WismaAppPermissions.PolicyReservation.Create;
        UpdatePolicyName = WismaAppPermissions.PolicyReservation.Edit;
        DeletePolicyName = WismaAppPermissions.PolicyReservation.Delete;
    }

    protected override async Task<IQueryable<Reservation>> CreateFilteredQueryAsync(
        PagedAndSortedResultRequestDto input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .Include(x => x.ReservationType);
    }

    public override async Task<PagedResultDto<ReservationsDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        try
        {
            var query = await Repository.GetQueryableAsync();

            // First, get the total count
            var totalCount = await query.CountAsync();

            // Then get the items with includes
            var items = await query
                .AsNoTracking()
                .Include(x => x.PaymentMethod)
                .Include(x => x.Status)
                .Include(x => x.DiningOptions)
                .Include(x => x.Company)
                .Include(x => x.ReservationType)
                // .Include(x => x.ReservationDetails!)
                // .ThenInclude(rd => rd.Room)
                // .Include(x => x.ReservationDetails!)
                // .ThenInclude(rd => rd.Guest)
                // .Include(x => x.ReservationDetails!)
                // .ThenInclude(rd => rd.Status)
                // .Include(x => x.ReservationDetails!)
                // .ThenInclude(rd => rd.ReservationRooms!)
                // .Include(x => x.ReservationDetails!)
                // .ThenInclude(rd => rd.ReservationFoodAndBeverages!)
                // .ThenInclude(rf => rf.FoodAndBeverage!)
                .OrderByDescending(x => x.CreationTime)
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount)
                .ToListAsync();

            if (items == null)
            {
                return new PagedResultDto<ReservationsDto>
                {
                    TotalCount = 0,
                    Items = new List<ReservationsDto>()
                };
            }

            var dtos = ObjectMapper.Map<List<Reservation>, List<ReservationsDto>>(items);

            // Add the additional properties like in GetAsync
            foreach (var dto in dtos)
            {
                if (dto.ReservationDetails != null)
                {
                    foreach (var detail in dto.ReservationDetails)
                    {
                        if (detail.Room != null)
                        {
                            detail.RoomNumber = detail.Room.RoomNumber;
                        }

                        if (detail.Guest != null)
                        {
                            detail.GuestName = detail.Guest.Fullname;
                        }
                    }
                }
            }

            return new PagedResultDto<ReservationsDto>
            {
                TotalCount = totalCount,
                Items = dtos
            };
        }
        catch (Exception ex)
        {
            throw new UserFriendlyException(
                "Could not retrieve reservations list",
                "Error.ReservationsList.Failed",
                ex.Message);
        }
    }

    /* CREATE RESERVATION WITH GUEST INSERTED MANUALLY */
    [Authorize(WismaAppPermissions.PolicyReservation.Create)]
    public virtual async Task<object> CreateReservationWithDetailsAsync(CreateReservationWithDetailsDto input)
    {
        try
        {
            // Use a unit of work to ensure transaction consistency
            using var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: true);

            try
            {
                // Use ObjectMapper to map from CreateReservationWithDetailsDto to Entities.Reservations
                var entity = ObjectMapper.Map<CreateReservationWithDetailsDto, Reservation>(input);

                // Generate a new reservation code
                entity.ReservationCode = await _codeGeneratorService.GenerateReservationCode();

                await _repository.InsertAsync(entity, autoSave: true);

                // Check if ReservationDetails or ReservationDetail is provided
                var details = input.ReservationDetails;

                foreach (var detail in details)
                {
                    await _reservationDetailService.CreateReservationDetailAsync(detail, entity.Id);
                }

                // Complete the transaction
                await uow.CompleteAsync();

                return new
                {
                    success = true,
                    message = "Reservation created successfully",
                    data = new
                    {
                        reservationId = entity.Id,
                        reservationCode = entity.ReservationCode
                    }
                };
            }
            catch (Exception)
            {
                // Rollback the transaction
                await uow.RollbackAsync();
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating reservation with details: {Message}", ex.Message);
            return new
            {
                success = false,
                message = "Failed to create reservation",
                error = ex.Message
            };
        }
    }


    [Authorize(WismaAppPermissions.PolicyReservation.Edit)]
    public async Task<ReservationsDto> UpdateWithDetailsAsync(Guid id, UpdateReservationWithDetailsDto input)
    {
        try
        {
            // Use a unit of work to ensure transaction consistency
            using var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: true);

            try
            {
                // Validate that the reservation exists
                var reservation = await _repository.GetAsync(id);
                if (reservation == null)
                {
                    throw new EntityNotFoundException(typeof(Reservation), id);
                }

                // Set concurrency stamp for optimistic concurrency control
                if (!string.IsNullOrEmpty(input.ConcurrencyStamp))
                {
                    reservation.ConcurrencyStamp = input.ConcurrencyStamp;
                }

                // Update the main reservation entity
                reservation.ReservationCode = input.ReservationCode;
                reservation.GroupCode = input.GroupCode;
                reservation.BookerName = input.BookerName;
                reservation.BookerIdentityNumber = input.BookerIdentityNumber;
                reservation.BookerPhoneNumber = input.BookerPhoneNumber;
                reservation.BookerEmail = input.BookerEmail;
                reservation.ArrivalDate = input.ArrivalDate;
                reservation.Days = input.Days;
                reservation.Attachment = input.Attachment;
                reservation.CompanyId = input.CompanyId;
                reservation.StatusId = input.StatusId;
                reservation.PaymentMethodId = input.PaymentMethodId;
                reservation.DiningOptionsId = input.DiningOptionsId;
                reservation.ReservationTypeId = input.ReservationTypeId;

                // First update the main reservation to get a new concurrency stamp
                await _repository.UpdateAsync(reservation, autoSave: true);

                // Update reservation details with the latest concurrency stamps
                // Check if ReservationDetails or ReservationDetail is provided
                var details = input.ReservationDetail ?? input.ReservationDetails ?? new List<UpdateReservationDetailDto>();

                // If the list is empty, it might be a new reservation, or the details were removed.
                // We'll handle this case by not updating any details
                foreach (var detailDto in details)
                {
                    await _reservationDetailService.UpdateReservationDetailAsync(detailDto);
                }

                // Complete the transaction
                await uow.CompleteAsync();

                // Return updated reservation data with fresh data
                return await GetWithDetailsAsync(id);
            }
            catch (AbpDbConcurrencyException ex)
            {
                // Rollback the transaction
                await uow.RollbackAsync();

                throw new UserFriendlyException(
                    "The data you have submitted has already been changed by another user. Please refresh and try again.",
                    "Error.Reservation.ConcurrencyConflict",
                    null);
            }
            catch (Exception)
            {
                // Rollback the transaction
                await uow.RollbackAsync();
                throw;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating reservation with details: {Message}", ex.Message);
            throw new UserFriendlyException(
                "An error occurred while updating the reservation. Please try again.",
                "Error.Reservation.UpdateFailed",
                ex.Message);
        }
    }


    [Authorize(WismaAppPermissions.PolicyReservation.Create)]
    public override async Task<ReservationsDto> CreateAsync(CreateUpdateReservationsDto input)
    {
        try
        {
            // Use a unit of work to ensure transaction consistency
            using var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: true);

            try
            {
                // 1. Generate a new reservation code
                input.ReservationCode = await _codeGeneratorService.GenerateReservationCode();

                // 2. Create the reservation entity
                // Ignore the ID from the input DTO to avoid tracking conflicts
                input.Id = Guid.Empty; // Clear any ID that might be in the input
                var entity = ObjectMapper.Map<CreateUpdateReservationsDto, Reservation>(input);

                // 3. Insert the reservation entity first to get its ID
                await _repository.InsertAsync(entity, autoSave: true);

                // 4. Process reservation-level attachments if provided
                if (input.Attachments != null && input.Attachments.Count > 0)
                {
                    await ProcessReservationAttachmentsAsync(input.Attachments, entity.Id, input.BookerName);
                }

                // 5. Process reservation details if provided
                if (input.ReservationDetails != null && input.ReservationDetails.Count > 0)
                {
                    foreach (var detail in input.ReservationDetails)
                    {
                        await _reservationDetailService.CreateReservationDetailAsync(detail, entity.Id);
                    }
                }

                // Complete the transaction
                await uow.CompleteAsync();

                // 6. Return the created reservation with details
                return await GetWithDetailsAsync(entity.Id);
            }
            catch (Exception)
            {
                // Rollback the transaction
                await uow.RollbackAsync();
                throw;
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error creating reservation: {Message}", ex.Message);
            throw new UserFriendlyException(
                "An error occurred while creating the reservation. Please try again.",
                "Error.Reservation.CreateFailed",
                ex.Message);
        }
    }

    /// <summary>
    /// Process reservation attachments and upload them to the SFTP server
    /// </summary>
    private async Task ProcessReservationAttachmentsAsync(List<ReservationAttachmentDto> attachments, Guid reservationId, string bookerName)
    {
        foreach (var attachment in attachments)
        {
            try
            {
                // Validate the base64 string
                if (string.IsNullOrEmpty(attachment.Base64Content))
                {
                    Logger.LogWarning("Empty base64 content for file {FileName}", attachment.FileName);
                    continue;
                }

                // Validate file type
                if (!IsAllowedFileType(attachment.ContentType))
                {
                    Logger.LogWarning("Invalid file type {ContentType} for file {FileName}",
                        attachment.ContentType, attachment.FileName);
                    continue;
                }

                // Decode the base64 string to a byte array
                byte[] fileBytes;
                try
                {
                    fileBytes = Convert.FromBase64String(attachment.Base64Content);
                }
                catch (FormatException ex)
                {
                    Logger.LogWarning(ex, "Invalid base64 string for file {FileName}", attachment.FileName);
                    continue;
                }

                // Create file upload DTO
                var fileUploadDto = new FileUploadDto
                {
                    Description = attachment.Description ?? $"Reservation attachment for {bookerName}",
                    ReferenceId = reservationId,
                    ReferenceType = "Reservation"
                };

                // Upload file
                await _attachmentAppService.UploadFileAsync(
                    fileUploadDto,
                    attachment.FileName,
                    attachment.ContentType,
                    fileBytes);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error processing reservation attachment {FileName}: {Message}",
                    attachment.FileName, ex.Message);
                // Continue with other attachments even if one fails
            }
        }
    }

    /// <summary>
    /// Checks if the file type is allowed (PDF or image)
    /// </summary>
    private static bool IsAllowedFileType(string contentType)
    {
        if (string.IsNullOrEmpty(contentType))
            return false;

        contentType = contentType.ToLower();

        // Allow PDF files
        if (contentType == "application/pdf")
            return true;

        // Allow image files
        if (contentType.StartsWith("image/"))
            return true;

        return false;
    }

    public override async Task<ReservationsDto> GetAsync(Guid id)
    {
        return await GetWithDetailsAsync(id);
    }

    public async Task<ReservationsDto> GetWithDetailsAsync(Guid id)
    {
        try
        {
            var query = await Repository.GetQueryableAsync();
            var reservation = await query
                .AsNoTracking()
                .Include(x => x.PaymentMethod)
                .Include(x => x.Status)
                .Include(x => x.DiningOptions)
                .Include(x => x.Company)
                .Include(x => x.ReservationType)
                // For ReservationDetail, I added ! to tell the compiler that even though the property is defined
                // as nullable (ICollection<ReservationDetails>?), we're asserting that it won't be null in this context.
                .Include(x => x.ReservationDetails!)
                    .ThenInclude(rd => rd.Room!)
                        .ThenInclude(r => r.RoomType)
                .Include(x => x.ReservationDetails!)
                    .ThenInclude(rd => rd.Room!)
                        .ThenInclude(r => r.RoomStatus)
                .Include(x => x.ReservationDetails!)
                    .ThenInclude(rd => rd.Guest)
                .Include(x => x.ReservationDetails!)
                    .ThenInclude(rd => rd.Status)
                .Include(x => x.ReservationDetails!)
                    .ThenInclude(rd => rd.ReservationRooms!)
                        .ThenInclude(rr => rr.Services!)
                            .ThenInclude(s => s.ServiceType)
                .Include(x => x.ReservationDetails!)
                    .ThenInclude(rd => rd.ReservationFoodAndBeverages!)
                        .ThenInclude(rf => rf.FoodAndBeverage!)
                .ThenInclude(f => f.TypeFoodAndBeverage)
                .Include(x => x.ReservationDetails!)
                    .ThenInclude(rd => rd.ReservationRooms!)
                        .ThenInclude(rr => rr.Services)
                .Include(x => x.ReservationDetails!)
                    .ThenInclude(rd => rd.ReservationFoodAndBeverages!)
                        .ThenInclude(rf => rf.FoodAndBeverage)
                .FirstOrDefaultAsync(x => x.Id == id);

            if (reservation == null)
            {
                throw new EntityNotFoundException(typeof(Reservation), id);
            }

            var dto = ObjectMapper.Map<Reservation, ReservationsDto>(reservation);

            // Process reservation details
            if (dto.ReservationDetails != null)
            {
                foreach (var detail in dto.ReservationDetails)
                {
                    if (detail.Room != null)
                    {
                        detail.RoomNumber = detail.Room.RoomNumber;
                    }

                    if (detail.Guest != null)
                    {
                        detail.GuestName = detail.Guest.Fullname;
                    }
                }
            }

            // Get reservation-level attachments
            try
            {
                var reservationAttachments = await _attachmentAppService.GetByReferenceAsync(id, "Reservation");
                if (reservationAttachments != null && reservationAttachments.Count > 0)
                {
                    dto.Attachments = reservationAttachments.Select(a => new ReservationAttachmentInfoDto
                    {
                        Id = a.Id,
                        FileName = a.FileName,
                        ContentType = a.ContentType,
                        Size = a.Size,
                        Url = a.Url,
                        StreamUrl = a.StreamUrl,
                        // Description is not available in FileUploadResultDto
                        Description = null,
                        CreationTime = a.UploadTime
                    }).ToList();
                }
            }
            catch (Exception ex)
            {
                // Log the error but don't fail the entire request
                Logger.LogError(ex, "Error retrieving attachments for reservation {ReservationId}: {Message}",
                    id, ex.Message);
            }

            // Process guest attachments if there are any reservation details with guests
            if (dto.ReservationDetails != null)
            {
                foreach (var detail in dto.ReservationDetails)
                {
                    if (detail.GuestId != Guid.Empty)
                    {
                        try
                        {
                            var guestAttachments = await _attachmentAppService.GetByReferenceAsync(detail.GuestId, "Guest");
                            if (guestAttachments != null && guestAttachments.Count > 0 && detail.Guest != null)
                            {
                                // Store the guest attachments in a temporary variable
                                // We can't directly set Guest.Attachments because it might not be defined in GuestDto
                                var guestAttachmentsList = guestAttachments.Select(a => new GuestAttachmentInfoDto
                                {
                                    Id = a.Id,
                                    FileName = a.FileName,
                                    ContentType = a.ContentType,
                                    Size = a.Size,
                                    Url = a.Url,
                                    StreamUrl = a.StreamUrl,
                                    // Description is not available in FileUploadResultDto
                                    Description = null,
                                    CreationTime = a.UploadTime
                                }).ToList();

                                // Use reflection to set the Attachments property if it exists
                                var attachmentsProperty = detail.Guest.GetType().GetProperty("Attachments");
                                if (attachmentsProperty != null)
                                {
                                    attachmentsProperty.SetValue(detail.Guest, guestAttachmentsList);
                                }
                                else
                                {
                                    Logger.LogWarning("GuestDto does not have an Attachments property. Guest ID: {GuestId}", detail.GuestId);
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            // Log the error but don't fail the entire request
                            Logger.LogError(ex, "Error retrieving attachments for guest {GuestId}: {Message}",
                                detail.GuestId, ex.Message);
                        }
                    }
                }
            }

            return dto;
        }
        catch (Exception ex)
        {
            throw new UserFriendlyException(
                "An error occurred while retrieving the reservation. Please try again.",
                "Error.Reservation.GetFailed",
                ex.Message);
        }
    }
}