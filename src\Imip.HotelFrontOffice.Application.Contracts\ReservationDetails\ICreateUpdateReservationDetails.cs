﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Services;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.HotelFrontOffice.ReservationDetails
{
    public interface ICreateUpdateReservationDetails : ICrudAppService<
        ReservationDetailsDto,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdateReservationDetailsDto,
        CreateUpdateReservationDetailsDto
    >
    {
    }
}