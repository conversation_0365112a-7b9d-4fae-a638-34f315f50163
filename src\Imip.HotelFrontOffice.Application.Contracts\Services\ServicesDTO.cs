﻿using System;
using System.Collections.Generic;
using Volo.Abp.Application.Dtos;

namespace Imip.HotelFrontOffice.Services;

public class ServicesDto : AuditedEntityDto<Guid>
{
    public string Name { get; set; } = default!;
    public decimal Price { get; set; } = 0!;
    public int UsegeTime { get; set; } = default!;
    public string Information { get; set; } = default!;
    public Guid ServiceTypeId { get; set; } = default!;
    public string ServiceTypeName { get; set; } = default!;

    // Dictionary for extra properties
    public Dictionary<string, object> ExtraProperties { get; set; } = new Dictionary<string, object>();
}