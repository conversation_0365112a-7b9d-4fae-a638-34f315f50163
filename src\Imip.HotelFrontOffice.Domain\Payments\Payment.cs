﻿using System;
using System.Collections.Generic;
using Imip.HotelFrontOffice.MasterStatuses;
using Imip.HotelFrontOffice.PaymentDetails;
using Imip.HotelFrontOffice.PaymentGuests;
using Imip.HotelFrontOffice.PaymentMethods;
using Imip.HotelFrontOffice.ReservationDetails;
using Imip.HotelFrontOffice.Reservations;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.HotelFrontOffice.Payments;

public class Payment : FullAuditedAggregateRoot<Guid>
{
    public decimal? TotalAmount { get; set; }
    public decimal? VatRate { get; set; }
    public decimal? VatAmount { get; set; }
    public decimal? PaidAmount { get; set; }
    public decimal? GrantTotal { get; set; }
    public required string PaymentCode { get; set; }
    public DateTime? TransactionDate { get; set; }
    public Guid ReservationsId { get; set; }
    public Guid? TaxId { get; set; }

    public Guid? PaymentMethodId { get; set; }

    public Guid? StatusId { get; set; }

    public virtual Reservation? Reservations { get; set; }
    public virtual MasterStatus? Status { get; set; }
    public virtual PaymentMethod? PaymentMethod { get; set; }
    public virtual ICollection<PaymentDetail> PaymentDetails { get; set; }
    public IEnumerable<PaymentGuest>? PaymentGuests { get; set; }

    protected Payment()
    {
        PaymentDetails = new HashSet<PaymentDetail>();
    }

    public Payment(
        Guid id,
        decimal? totalAmount,
        decimal? paidAmount,
        decimal? vatRate,
        decimal? vatAmount,
        decimal? grantTotal,
        Guid? paymentMethodId,
        Guid? statusId,
        Guid? taxId,
        string paymentCode,
        DateTime? transactionDate,
        Guid reservationsId
    ) : base(id)
    {
        TotalAmount = totalAmount;
        PaidAmount = paidAmount;
        VatRate = vatRate;
        VatAmount = vatAmount;
        GrantTotal = grantTotal;
        PaymentMethodId = paymentMethodId;
        StatusId = statusId;
        TaxId = taxId;
        PaymentCode = paymentCode;
        TransactionDate = transactionDate;
        ReservationsId = reservationsId;
        PaymentDetails = new HashSet<PaymentDetail>();
    }
}