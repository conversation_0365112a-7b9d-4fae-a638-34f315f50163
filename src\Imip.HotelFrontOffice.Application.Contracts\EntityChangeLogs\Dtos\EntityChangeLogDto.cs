using System;
using System.Collections.Generic;
using Volo.Abp.Application.Dtos;

namespace Imip.HotelFrontOffice.EntityChangeLogs.Dtos
{
    /// <summary>
    /// DTO for entity change logs
    /// </summary>
    public class EntityChangeLogDto : EntityDto<Guid>
    {
        /// <summary>
        /// The time when the entity was changed
        /// </summary>
        public DateTime ChangeTime { get; set; }

        /// <summary>
        /// Type of change: Created, Updated, or Deleted
        /// </summary>
        public EntityChangeTypeDto ChangeType { get; set; }

        /// <summary>
        /// The ID of the entity that was changed
        /// </summary>
        public string? EntityId { get; set; }

        /// <summary>
        /// The tenant ID of the entity that was changed (if multi-tenant)
        /// </summary>
        public Guid? EntityTenantId { get; set; }

        /// <summary>
        /// Full type name of the entity
        /// </summary>
        public string? EntityTypeFullName { get; set; }

        /// <summary>
        /// User-friendly display name of the entity type
        /// </summary>
        public string? EntityDisplayName { get; set; }

        /// <summary>
        /// User-friendly display value for the entity (usually the name or title)
        /// </summary>
        public string? EntityDisplayValue { get; set; }

        /// <summary>
        /// Reference to the original ABP audit log ID
        /// </summary>
        public Guid AuditLogId { get; set; }

        /// <summary>
        /// Username of the user who made the change
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// Collection of property changes for this entity change
        /// </summary>
        public List<EntityPropertyChangeLogDto>? PropertyChanges { get; set; }

        /// <summary>
        /// Audit log information
        /// </summary>
        public AuditLogInfoDto? AuditLog { get; set; }

        /// <summary>
        /// Audit log actions information
        /// </summary>
        public List<AuditLogActionInfoDto>? AuditLogActions { get; set; }
    }

    /// <summary>
    /// Type of entity change
    /// </summary>
    public enum EntityChangeTypeDto : byte
    {
        Created = 0,
        Updated = 1,
        Deleted = 2
    }

    /// <summary>
    /// DTO for audit log information (subset of AuditLogWithDetailsDto)
    /// </summary>
    public class AuditLogInfoDto : EntityDto<Guid>
    {
        /// <summary>
        /// Application name
        /// </summary>
        public string? ApplicationName { get; set; }

        /// <summary>
        /// User ID
        /// </summary>
        public Guid? UserId { get; set; }

        /// <summary>
        /// Username
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// Tenant ID
        /// </summary>
        public Guid? TenantId { get; set; }

        /// <summary>
        /// Execution time
        /// </summary>
        public DateTime ExecutionTime { get; set; }

        /// <summary>
        /// Execution duration in milliseconds
        /// </summary>
        public int ExecutionDuration { get; set; }

        /// <summary>
        /// Client IP address
        /// </summary>
        public string? ClientIpAddress { get; set; }

        /// <summary>
        /// Browser info
        /// </summary>
        public string? BrowserInfo { get; set; }

        /// <summary>
        /// HTTP method
        /// </summary>
        public string? HttpMethod { get; set; }

        /// <summary>
        /// URL
        /// </summary>
        public string? Url { get; set; }

        /// <summary>
        /// HTTP status code
        /// </summary>
        public int? HttpStatusCode { get; set; }
    }

    /// <summary>
    /// DTO for audit log action information (subset of AuditLogActionDto)
    /// </summary>
    public class AuditLogActionInfoDto : EntityDto<Guid>
    {
        /// <summary>
        /// Audit log ID
        /// </summary>
        public Guid AuditLogId { get; set; }

        /// <summary>
        /// Service name
        /// </summary>
        public string? ServiceName { get; set; }

        /// <summary>
        /// Method name
        /// </summary>
        public string? MethodName { get; set; }

        /// <summary>
        /// Parameters
        /// </summary>
        public string? Parameters { get; set; }

        /// <summary>
        /// Execution time
        /// </summary>
        public DateTime ExecutionTime { get; set; }

        /// <summary>
        /// Execution duration in milliseconds
        /// </summary>
        public int ExecutionDuration { get; set; }
    }
}
