using System;
using Imip.HotelFrontOffice.Attachments;
using Imip.HotelFrontOffice.Companies;
using Imip.HotelFrontOffice.DiningOptions;
using Imip.HotelFrontOffice.Documents;
using Imip.HotelFrontOffice.EntityChangeLogs;
using Imip.HotelFrontOffice.FoodAndBeverages;
using Imip.HotelFrontOffice.Guests;
using Imip.HotelFrontOffice.MasterStatuses;
using Imip.HotelFrontOffice.PaymentDetails;
using Imip.HotelFrontOffice.PaymentGuests;
using Imip.HotelFrontOffice.PaymentMethods;
using Imip.HotelFrontOffice.Payments;
using Imip.HotelFrontOffice.Reports;
using Imip.HotelFrontOffice.ReservationDetails;
using Imip.HotelFrontOffice.ReservationFoodAndBeverages;
using Imip.HotelFrontOffice.ReservationRooms;
using Imip.HotelFrontOffice.Reservations;
using Imip.HotelFrontOffice.ReservationTypes;
using Imip.HotelFrontOffice.Rooms;
using Imip.HotelFrontOffice.RoomStatuses;
using Imip.HotelFrontOffice.RoomStatusLogs;
using Imip.HotelFrontOffice.RoomTypes;
using Imip.HotelFrontOffice.Services;
using Imip.HotelFrontOffice.ServiceTypes;
using Imip.HotelFrontOffice.Taxes;
using Imip.HotelFrontOffice.TypeFoodAndBeverages;
using Microsoft.EntityFrameworkCore;
using TickerQ.EntityFrameworkCore.Entities;
using Volo.Abp.AuditLogging.EntityFrameworkCore;
using Volo.Abp.BackgroundJobs.EntityFrameworkCore;
using Volo.Abp.BlobStoring.Database.EntityFrameworkCore;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore.Modeling;
using Volo.Abp.FeatureManagement.EntityFrameworkCore;
using Volo.Abp.Identity;
using Volo.Abp.Identity.EntityFrameworkCore;
using Volo.Abp.OpenIddict.EntityFrameworkCore;
using Volo.Abp.PermissionManagement.EntityFrameworkCore;
using Volo.Abp.SettingManagement.EntityFrameworkCore;
using Volo.Abp.TenantManagement;
using Volo.Abp.TenantManagement.EntityFrameworkCore;


namespace Imip.HotelFrontOffice.EntityFrameworkCore;

[ReplaceDbContext(typeof(IIdentityDbContext))]
[ReplaceDbContext(typeof(ITenantManagementDbContext))]
[ConnectionStringName("Default")]
public class HotelFrontOfficeDbContext :
    AbpDbContext<HotelFrontOfficeDbContext>,
    ITenantManagementDbContext,
    IIdentityDbContext
{
    /* Add DbSet properties for your Aggregate Roots / Entities here. */
    public DbSet<Guest> Guests { get; set; }
    public DbSet<TypeFoodAndBeverage> TypeFoodAndBeverages { get; set; }
    public DbSet<FoodAndBeverage> FoodAndBeverages { get; set; }
    public DbSet<RoomStatus> RoomStatuses { get; set; }
    public DbSet<RoomType> RoomTypes { get; set; }
    public DbSet<Room> Rooms { get; set; }
    public DbSet<Service> Services { get; set; }
    public DbSet<ServiceType> ServiceTypes { get; set; }

    public DbSet<Company> Companies { get; set; }
    public DbSet<MasterStatus> MasterStatuses { get; set; }
    public DbSet<DiningOption> MasterDiningOptions { get; set; }
    public DbSet<PaymentMethod> MasterPaymentMethods { get; set; }

    public DbSet<ReservationRoom> ReservationRooms { get; set; }
    public DbSet<ReservationFoodAndBeverage> ReservationFoodAndBeverages { get; set; }

    public DbSet<Payment> Payments { get; set; }
    public DbSet<Tax> Taxes { get; set; }
    public DbSet<Report> Reports { get; set; }
    public DbSet<PaymentGuest> PaymentGuests { get; set; }
    public DbSet<RoomStatusLog> RoomStatusLogs { get; set; }
    public DbSet<PaymentDetail> PaymentDetails { get; set; }

    public DbSet<Attachment> Attachments { get; set; }

    public DbSet<TemporaryZipFile> TemporaryZipFiles { get; set; }

    public DbSet<DocumentTemplate> DocumentTemplates { get; set; }

    // Entity Change Logs
    public DbSet<EntityChangeLog> EntityChangeLogs { get; set; }
    public DbSet<EntityPropertyChangeLog> EntityPropertyChangeLogs { get; set; }

    // TickerQ tables
    public DbSet<CronTicker> CronTickers { get; set; }
    public DbSet<TimeTicker> TimeTickers { get; set; }
    public DbSet<CronTickerOccurrence<CronTicker>> CronTickerOccurrences { get; set; }

    #region Entities from the modules

    /* Notice: We only implemented IIdentityProDbContext and ISaasDbContext
     * and replaced them for this DbContext. This allows you to perform JOIN
     * queries for the entities of these modules over the repositories easily. You
     * typically don't need that for other modules. But, if you need, you can
     * implement the DbContext interface of the needed module and use ReplaceDbContext
     * attribute just like IIdentityProDbContext and ISaasDbContext.
     *
     * More info: Replacing a DbContext of a module ensures that the related module
     * uses this DbContext on runtime. Otherwise, it will use its own DbContext class.
     */

    // Identity
    public DbSet<IdentityUser> Users { get; set; }
    public DbSet<IdentityRole> Roles { get; set; }
    public DbSet<IdentityClaimType> ClaimTypes { get; set; }
    public DbSet<OrganizationUnit> OrganizationUnits { get; set; }
    public DbSet<IdentitySecurityLog> SecurityLogs { get; set; }
    public DbSet<IdentityLinkUser> LinkUsers { get; set; }
    public DbSet<IdentityUserDelegation> UserDelegations { get; set; }
    public DbSet<IdentitySession> Sessions { get; set; }
    public DbSet<ReservationType> ReservationTypes { get; set; }
    public DbSet<Reservation> Reservations { get; set; }
    public DbSet<ReservationDetail> ReservationDetails { get; set; }

    // Tenant Management
    public DbSet<Tenant> Tenants { get; set; }
    public DbSet<TenantConnectionString> TenantConnectionStrings { get; set; }

    #endregion

    public HotelFrontOfficeDbContext(DbContextOptions<HotelFrontOfficeDbContext> options)
        : base(options)
    {
    }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        /* Include modules to your migration db context */

        builder.ConfigurePermissionManagement();
        builder.ConfigureSettingManagement();
        builder.ConfigureBackgroundJobs();
        builder.ConfigureAuditLogging();
        builder.ConfigureFeatureManagement();
        builder.ConfigureIdentity();
        builder.ConfigureOpenIddict();
        builder.ConfigureTenantManagement();
        builder.ConfigureBlobStoring();

        builder.Entity<Payment>(b =>
        {
            b.ToTable(HotelFrontOfficeConsts.DbTablePrefix + "Payments", HotelFrontOfficeConsts.DbSchema);
            b.ConfigureByConvention();

            b.Property(x => x.TotalAmount)
                .HasColumnType("decimal(18,4)");
            b.Property(x => x.VatRate)
                .HasColumnType("decimal(18,4)");
            b.Property(x => x.VatAmount)
                .HasColumnType("decimal(18,4)");
            b.Property(x => x.PaymentCode)
                .HasMaxLength(200);

            b.Property(x => x.PaidAmount)
                .HasColumnType("decimal(18,4)");
            b.Property(x => x.GrantTotal)
                .HasColumnType("decimal(18,4)");

            b.Property(x => x.PaymentMethodId);

            b.Property(x => x.StatusId);

            b.Property(x => x.TransactionDate)
                .IsRequired();

            b.HasOne(x => x.Reservations)
                .WithMany()
                .HasForeignKey(x => x.ReservationsId)
                .IsRequired()
                .OnDelete(DeleteBehavior.Restrict);

            b.HasOne(x => x.Status)
                .WithMany(x => x.Payments)
                .HasForeignKey(x => x.StatusId)
                .IsRequired(false)
                .OnDelete(DeleteBehavior.Restrict);

            b.HasOne(x => x.PaymentMethod)
                .WithMany(x => x.Payments)
                .HasForeignKey(x => x.PaymentMethodId)
                .IsRequired(false)
                .OnDelete(DeleteBehavior.Restrict);

            b.HasIndex(x => new { x.ReservationsId, x.StatusId, x.PaymentMethodId });

            // Additional performance indexes for Payment entity
            b.HasIndex(x => x.PaymentCode); // For payment code searches
            b.HasIndex(x => x.TransactionDate); // For transaction date filtering
            b.HasIndex(x => x.ReservationsId); // For reservation-based queries
            b.HasIndex(x => x.StatusId); // For status filtering
            b.HasIndex(x => x.PaymentMethodId); // For payment method filtering
            b.HasIndex(x => x.TaxId); // For tax filtering
            b.HasIndex(x => x.TotalAmount); // For amount range queries
            b.HasIndex(x => x.PaidAmount); // For paid amount queries
            b.HasIndex(x => x.GrantTotal); // For grant total queries
            b.HasIndex(x => x.CreationTime); // For sorting by creation time

            // Composite indexes for complex filtering scenarios
            b.HasIndex(x => new { x.StatusId, x.TransactionDate }); // Status with transaction date
            b.HasIndex(x => new { x.ReservationsId, x.TransactionDate }); // Reservation with transaction date
            b.HasIndex(x => new { x.PaymentMethodId, x.StatusId }); // Payment method with status
            b.HasIndex(x => new { x.TransactionDate, x.StatusId }); // Transaction date with status
            b.HasIndex(x => new { x.ReservationsId, x.PaymentMethodId }); // Reservation with payment method
            b.HasIndex(x => new { x.StatusId, x.CreationTime }); // Status with creation time sorting
        });

        builder.Entity<PaymentGuest>(b =>
        {
            b.ToTable(HotelFrontOfficeConsts.DbTablePrefix + "PaymentGuests", HotelFrontOfficeConsts.DbSchema);
            b.ConfigureByConvention();

            b.HasOne(x => x.Guest)
                .WithMany(g => g.PaymentGuests)
                .HasForeignKey(x => x.GuestId)
                .IsRequired()
                .OnDelete(DeleteBehavior.Restrict);

            b.Property(x => x.AmountPaid)
                .IsRequired()
                .HasColumnType("decimal(18,4)");

            b.HasOne(x => x.Payments)
                .WithMany(x => x.PaymentGuests)
                .HasForeignKey(x => x.PaymentId)
                .IsRequired()
                .OnDelete(DeleteBehavior.Restrict);

            b.HasIndex(x => new { x.PaymentId, x.GuestId });

            // Additional performance indexes for PaymentGuest entity
            b.HasIndex(x => x.PaymentId); // For payment-based queries
            b.HasIndex(x => x.GuestId); // For guest-based queries
            b.HasIndex(x => x.AmountPaid); // For amount paid queries
            b.HasIndex(x => x.CreationTime); // For sorting by creation time
        });

        builder.Entity<RoomStatusLog>(b =>
        {
            b.ToTable(HotelFrontOfficeConsts.DbTablePrefix + "RoomStatusLogs", HotelFrontOfficeConsts.DbSchema);
            b.ConfigureByConvention();
            b.Property(x => x.RoomId)
                .IsRequired();
            b.Property(x => x.RoomStatusId)
                .IsRequired();


            b.Property(x => x.StatusSource)
                .IsRequired()
                .HasMaxLength(200);

            b.HasOne(x => x.Room)
                .WithMany(x => x.RoomStatusLogs)
                .HasForeignKey(x => x.RoomId)
                .IsRequired()
                .OnDelete(DeleteBehavior.Restrict);

            b.HasOne(x => x.RoomStatus)
                .WithMany(x => x.RoomStatusLogs)
                .HasForeignKey(x => x.RoomStatusId)
                .IsRequired()
                .OnDelete(DeleteBehavior.Restrict);
        });

        builder.Entity<PaymentDetail>(b =>
        {
            b.ToTable(HotelFrontOfficeConsts.DbTablePrefix + "PaymentDetails", HotelFrontOfficeConsts.DbSchema);
            b.ConfigureByConvention();

            b.Property(x => x.Qty)
                .IsRequired()
                .HasColumnType("decimal(18,4)");
            b.Property(x => x.UnitPrice)
                .IsRequired(false)
                .HasColumnType("decimal(18,4)");
            b.Property(x => x.VatRate)
                .IsRequired(false)
                .HasColumnType("decimal(18,4)");
            b.Property(x => x.VatAmount)
                .IsRequired(false)
                .HasColumnType("decimal(18,4)");
            b.Property(x => x.Amount)
                .IsRequired()
                .HasColumnType("decimal(18,4)");
            b.Property(x => x.SourceId)
                .IsRequired(false);
            b.Property(x => x.SourceType)
                .IsRequired()
                .HasConversion(
                    v => v.ToString(),                                  // Convert to string when saving to database
                    v => v != null ? (PaymentSourceType)Enum.Parse(typeof(PaymentSourceType), v) : default) // Convert from string when reading from database
                .HasMaxLength(200);
            b.Property(x => x.PaymentId)
                .IsRequired();

            b.HasOne(x => x.ReservationDetails)
                .WithMany(x => x.PaymentDetails)
                .HasForeignKey(x => x.ReservationDetailsId)
                .IsRequired(false)
                .OnDelete(DeleteBehavior.Restrict);

            // b.HasOne(x => x.ReservationFoodAndBeverages)
            //     .WithOne(x => x.PaymentDetails)
            //     .HasForeignKey<PaymentDetail>(x => x.SourceId)
            //     .IsRequired(false)
            //     .OnDelete(DeleteBehavior.Restrict);

            // b.HasOne(x => x.ReservationRoom)
            //     .WithOne(x => x.PaymentDetails)
            //     .HasForeignKey<PaymentDetail>(x => x.SourceId)
            //     .IsRequired(false)
            //     .OnDelete(DeleteBehavior.Restrict);

            b.HasOne(x => x.Payments)
                .WithMany(x => x.PaymentDetails)
                .HasForeignKey(x => x.PaymentId)
                .IsRequired()
                .OnDelete(DeleteBehavior.Restrict);

            b.HasIndex(x => new { x.SourceId, x.SourceType });

            // Additional performance indexes for PaymentDetail entity
            b.HasIndex(x => x.PaymentId); // For payment-based queries
            b.HasIndex(x => x.ReservationDetailsId); // For reservation detail queries
            b.HasIndex(x => x.SourceType); // For source type filtering
            b.HasIndex(x => x.SourceId); // For source ID filtering
            b.HasIndex(x => x.Amount); // For amount range queries
            b.HasIndex(x => x.UnitPrice); // For unit price queries
            b.HasIndex(x => x.Qty); // For quantity queries
            b.HasIndex(x => x.VatAmount); // For VAT amount queries
            b.HasIndex(x => x.TaxId); // For tax filtering
            b.HasIndex(x => x.CreationTime); // For sorting by creation time

            // Composite indexes for complex filtering scenarios
            b.HasIndex(x => new { x.PaymentId, x.SourceType }); // Payment with source type
            b.HasIndex(x => new { x.ReservationDetailsId, x.SourceType }); // Reservation detail with source type
            b.HasIndex(x => new { x.SourceType, x.Amount }); // Source type with amount
            b.HasIndex(x => new { x.PaymentId, x.ReservationDetailsId }); // Payment with reservation detail
            b.HasIndex(x => new { x.TaxId, x.SourceType }); // Tax with source type
        });

        builder.Entity<ReservationRoom>(b =>
        {
            b.ToTable(HotelFrontOfficeConsts.DbTablePrefix + "ReservationRooms", HotelFrontOfficeConsts.DbSchema);
            b.ConfigureByConvention();

            b.Property(x => x.TotalPrice)
                .IsRequired()
                .HasColumnType("decimal(18,4)");

            b.Property(x => x.Quantity)
                .IsRequired();

            b.HasOne(x => x.ReservationDetails)
                .WithMany(x => x.ReservationRooms)
                .HasForeignKey(x => x.ReservationDetailsId)
                .IsRequired()
                .OnDelete(DeleteBehavior.Restrict);


            b.HasOne(x => x.Services)
                .WithMany()
                .HasForeignKey(x => x.ServiceId)
                .IsRequired()
                .OnDelete(DeleteBehavior.Restrict);

            b.HasOne(x => x.PaymentStatus)
                .WithMany(x => x.ReservationRoomPaymentStatus)
                .HasForeignKey(x => x.PaymentStatusId)
                .IsRequired(false)
                .OnDelete(DeleteBehavior.ClientSetNull);

            // Performance indexes for ReservationRoom entity
            b.HasIndex(x => x.ReservationDetailsId); // For reservation detail filtering
            b.HasIndex(x => x.ServiceId); // For service filtering
            b.HasIndex(x => x.PaymentStatusId); // For payment status filtering
            b.HasIndex(x => x.TotalPrice); // For price range queries
            b.HasIndex(x => new { x.ReservationDetailsId, x.PaymentStatusId }); // Combined filtering
            b.HasIndex(x => new { x.ServiceId, x.PaymentStatusId }); // Service with payment status
        });

        builder.Entity<ReservationFoodAndBeverage>(b =>
        {
            b.ToTable(HotelFrontOfficeConsts.DbTablePrefix + "ReservationFoodAndBeverages",
                HotelFrontOfficeConsts.DbSchema);
            b.ConfigureByConvention();

            b.Property(x => x.TotalPrice)
                .IsRequired()
                .HasColumnType("decimal(18,4)");

            b.Property(x => x.Quantity)
                .IsRequired();


            b.HasOne(x => x.ReservationDetails)
                .WithMany(x => x.ReservationFoodAndBeverages)
                .HasForeignKey(x => x.ReservationDetailsId)
                .IsRequired()
                .OnDelete(DeleteBehavior.Restrict);


            b.HasOne(x => x.FoodAndBeverage)
                .WithMany(x => x.ReservationFoodAndBeverages)
                .HasForeignKey(x => x.FoodAndBeverageId)
                .IsRequired()
                .OnDelete(DeleteBehavior.Restrict);

            b.HasOne(x => x.PaymentStatus)
                .WithMany(x => x.ReservationFoodAndBeveragePaymentStatus)
                .HasForeignKey(x => x.PaymentStatusId)
                .IsRequired(false)
                .OnDelete(DeleteBehavior.ClientSetNull);

            // Performance indexes for ReservationFoodAndBeverage entity
            b.HasIndex(x => x.ReservationDetailsId); // For reservation detail filtering
            b.HasIndex(x => x.FoodAndBeverageId); // For food and beverage filtering
            b.HasIndex(x => x.PaymentStatusId); // For payment status filtering
            b.HasIndex(x => x.TotalPrice); // For price range queries
            b.HasIndex(x => new { x.ReservationDetailsId, x.PaymentStatusId }); // Combined filtering
            b.HasIndex(x => new { x.FoodAndBeverageId, x.PaymentStatusId }); // Food with payment status

            b.HasIndex(x => x.Id);
        });

        builder.Entity<ReservationType>(b =>
        {
            b.ToTable(HotelFrontOfficeConsts.DbTablePrefix + "ReservationTypes", HotelFrontOfficeConsts.DbSchema);
            b.ConfigureByConvention();

            b.Property(x => x.Name)
                .IsRequired()
                .HasMaxLength(200);

            b.HasOne(x => x.Status)
                .WithMany(x => x.ReservationTypes)
                .HasForeignKey(x => x.StatusId)
                .IsRequired(false)
                .OnDelete(DeleteBehavior.Restrict);

            b.HasIndex(x => x.Name);

            // Additional performance indexes for ReservationType entity
            b.HasIndex(x => x.StatusId); // For status filtering
            b.HasIndex(x => x.CreationTime); // For sorting by creation time
            b.HasIndex(x => new { x.StatusId, x.Name }); // Status with name filtering
        });

        builder.Entity<Company>(b =>
        {
            b.ToTable(HotelFrontOfficeConsts.DbTableMasterPrefix + "Company", HotelFrontOfficeConsts.DbSchema);
            b.ConfigureByConvention();

            b.Property(x => x.Name)
                .IsRequired()
                .HasMaxLength(200);

            b.HasIndex(x => x.Name);
        });

        builder.Entity<DiningOption>(b =>
        {
            b.ToTable(HotelFrontOfficeConsts.DbTableMasterPrefix + "DiningOptions", HotelFrontOfficeConsts.DbSchema);
            b.ConfigureByConvention();

            b.Property(x => x.Name)
                .IsRequired()
                .HasMaxLength(200);

            b.HasIndex(x => x.Name);
        });

        builder.Entity<PaymentMethod>(b =>
        {
            b.ToTable(HotelFrontOfficeConsts.DbTableMasterPrefix + "PaymentMethod", HotelFrontOfficeConsts.DbSchema);
            b.ConfigureByConvention();

            b.Property(x => x.Name)
                .IsRequired()
                .HasMaxLength(200);

            b.Property(x => x.Information)
                .IsRequired()
                .HasMaxLength(200);

            b.HasIndex(x => x.Name);
        });

        builder.Entity<MasterStatus>(b =>
        {
            b.ToTable(HotelFrontOfficeConsts.DbTableMasterPrefix + "Status", HotelFrontOfficeConsts.DbSchema);
            b.ConfigureByConvention();

            b.Property(x => x.Name)
                .IsRequired()
                .HasMaxLength(200);
            b.Property(x => x.DocType)
                .IsRequired()
                .HasMaxLength(200);

            b.HasIndex(x => x.Name);
            b.HasIndex(x => x.DocType);

            // Additional performance indexes for MasterStatus entity
            b.HasIndex(x => x.Code); // For code-based searches
            b.HasIndex(x => new { x.DocType, x.Code }); // Combined DocType and Code filtering
            b.HasIndex(x => new { x.DocType, x.Name }); // Combined DocType and Name filtering
            b.HasIndex(x => x.CreationTime); // For sorting by creation time
        });

        builder.Entity<Reservation>(b =>
        {
            b.ToTable(HotelFrontOfficeConsts.DbTablePrefix + "Reservations", HotelFrontOfficeConsts.DbSchema);
            b.ConfigureByConvention();

            // b.Property(x => x.StatusId);
            // b.Property(x => x.DiningOptionsId);
            // b.Property(x => x.PaymentMethodId);
            // b.Property(x => x.CompanyId);

            b.Property(x => x.ReservationCode)
                .IsRequired()
                .HasMaxLength(200);
            b.Property(x => x.GroupCode)
                .IsRequired()
                .HasMaxLength(200);
            b.Property(x => x.BookerName)
                .IsRequired()
                .HasMaxLength(200);
            b.Property(x => x.BookerIdentityNumber)
                .HasMaxLength(200);
            b.Property(x => x.BookerPhoneNumber)
                .HasMaxLength(200);
            b.Property(x => x.BookerEmail)
                .HasMaxLength(200);
            b.Property(x => x.ArrivalDate)
                .IsRequired();
            b.Property(x => x.Days)
                .IsRequired();
            b.Property(x => x.Attachment)
                .HasMaxLength(200);

            b.HasOne(x => x.ReservationType)
                .WithMany(x => x.Reservations)
                .HasForeignKey(x => x.ReservationTypeId)
                .IsRequired()
                .OnDelete(DeleteBehavior.Restrict);

            b.HasOne(x => x.Status)
                .WithMany(x => x.Reservations)
                .HasForeignKey(x => x.StatusId)
                .IsRequired(false)
                .OnDelete(DeleteBehavior.Restrict);

            b.HasOne(x => x.PaymentMethod)
                .WithMany(x => x.Reservations)
                .HasForeignKey(x => x.PaymentMethodId)
                .IsRequired(false)
                .OnDelete(DeleteBehavior.Restrict);

            // Performance indexes for Reservation entity
            b.HasIndex(x => x.ReservationCode); // For reservation code searches
            b.HasIndex(x => x.GroupCode); // For group code searches
            b.HasIndex(x => x.BookerName); // For booker name searches
            b.HasIndex(x => x.BookerEmail); // For booker email searches
            b.HasIndex(x => x.BookerPhoneNumber); // For booker phone searches
            b.HasIndex(x => x.ArrivalDate); // For arrival date filtering
            b.HasIndex(x => x.StatusId); // For status filtering
            b.HasIndex(x => x.CompanyId); // For company filtering
            b.HasIndex(x => x.ReservationTypeId); // For type filtering
            b.HasIndex(x => x.PaymentMethodId); // For payment method filtering
            b.HasIndex(x => x.CreationTime); // For sorting by creation time

            // Composite indexes for common query patterns
            b.HasIndex(x => new { x.StatusId, x.ArrivalDate }); // Status with arrival date
            b.HasIndex(x => new { x.CompanyId, x.StatusId }); // Company with status
            b.HasIndex(x => new { x.ReservationTypeId, x.StatusId }); // Type with status
            b.HasIndex(x => new { x.ArrivalDate, x.StatusId }); // Arrival date with status

            b.HasOne(x => x.DiningOptions)
                .WithMany(x => x.Reservations)
                .HasForeignKey(x => x.DiningOptionsId)
                .IsRequired(false)
                .OnDelete(DeleteBehavior.Restrict);

            b.HasOne(x => x.Company)
                .WithMany(x => x.Reservations)
                .HasForeignKey(x => x.CompanyId)
                .IsRequired(false)
                .OnDelete(DeleteBehavior.Restrict);


            b.HasIndex(x => x.ReservationCode);
            b.HasIndex(x => new { x.GroupCode, x.BookerName });
            b.HasIndex(x => new { x.StatusId, x.PaymentMethodId, x.DiningOptionsId, x.CompanyId });
        });

        builder.Entity<ReservationDetail>(b =>
        {
            b.ToTable(HotelFrontOfficeConsts.DbTablePrefix + "ReservationDetails", HotelFrontOfficeConsts.DbSchema);
            b.ConfigureByConvention();

            b.Property(x => x.CheckInDate)
                .IsRequired(false); // Make it nullable
            b.Property(x => x.CheckOutDate)
                .IsRequired(false); // Make it nullable
            b.Property(x => x.Rfid)
                .HasMaxLength(200);
            b.Property(x => x.Price)
                .IsRequired()
                .HasColumnType("decimal(18,4)");

            b.HasOne(x => x.Reservation)
                .WithMany(x => x.ReservationDetails)
                .HasForeignKey(x => x.ReservationId)
                .IsRequired()
                .OnDelete(DeleteBehavior.Restrict);

            b.HasOne(x => x.Status)
                .WithMany(x => x.ReservationDetails)
                .HasForeignKey(x => x.StatusId)
                .IsRequired(false)
                .OnDelete(DeleteBehavior.ClientSetNull);

            b.HasOne(x => x.Criteria)
                .WithMany(x => x.ReservationCriteria)
                .HasForeignKey(x => x.CriteriaId)
                .IsRequired(false)
                .OnDelete(DeleteBehavior.ClientSetNull);

            b.HasOne(x => x.PaymentStatus)
                .WithMany(x => x.ReservationDetailPaymentStatus)
                .HasForeignKey(x => x.PaymentStatusId)
                .IsRequired(false)
                .OnDelete(DeleteBehavior.ClientSetNull);


            b.HasOne(x => x.Room)
                .WithMany(x => x.ReservationDetails)
                .HasForeignKey(x => x.RoomId)
                .IsRequired(false)
                .OnDelete(DeleteBehavior.Restrict);

            b.HasOne(x => x.Guest)
                .WithMany(x => x.ReservationDetails)
                .HasForeignKey(x => x.GuestId)
                .IsRequired(false)
                .OnDelete(DeleteBehavior.Restrict);

            b.HasIndex(x => x.Id);
            b.HasIndex(x => new { x.StatusId, x.RoomId, x.GuestId, x.ReservationId });

            // Performance optimization indexes
            b.HasIndex(x => x.CheckInDate);
            b.HasIndex(x => x.CheckOutDate);
            b.HasIndex(x => new { x.CheckInDate, x.CheckOutDate });
            b.HasIndex(x => x.PaymentStatusId);
            b.HasIndex(x => new { x.StatusId, x.PaymentStatusId });
            b.HasIndex(x => new { x.ReservationId, x.StatusId });
            b.HasIndex(x => new { x.RoomId, x.CheckInDate, x.CheckOutDate });
            b.HasIndex(x => x.CreationTime); // For default sorting

            // Additional performance indexes for common query patterns
            b.HasIndex(x => x.GuestId); // For guest-specific queries
            b.HasIndex(x => x.CriteriaId); // For criteria-based filtering
            b.HasIndex(x => x.Price); // For price range queries
            b.HasIndex(x => x.Rfid); // For RFID lookups

            // Composite indexes for complex filtering scenarios
            b.HasIndex(x => new { x.GuestId, x.StatusId }); // Guest status filtering
            b.HasIndex(x => new { x.RoomId, x.StatusId }); // Room status filtering
            b.HasIndex(x => new { x.PaymentStatusId, x.CheckInDate }); // Payment status with date
            b.HasIndex(x => new { x.StatusId, x.CheckInDate, x.CheckOutDate }); // Status with date range
            b.HasIndex(x => new { x.ReservationId, x.GuestId }); // Reservation guest combination
            b.HasIndex(x => new { x.CriteriaId, x.StatusId }); // Criteria status combination

            // Indexes for sorting with filtering
            b.HasIndex(x => new { x.StatusId, x.CreationTime }); // Status with creation time sorting
            b.HasIndex(x => new { x.PaymentStatusId, x.CreationTime }); // Payment status with creation time sorting
            b.HasIndex(x => new { x.RoomId, x.CreationTime }); // Room with creation time sorting
        });

        builder.Entity<Guest>(b =>
        {
            b.ToTable(HotelFrontOfficeConsts.DbTablePrefix + "Guests", HotelFrontOfficeConsts.DbSchema);
            b.ConfigureByConvention();

            b.Property(x => x.Fullname)
                .HasMaxLength(200)
                .IsRequired();
            b.Property(x => x.IdentityNumber) // Updated property name
                .HasMaxLength(50);
            b.Property(x => x.PhoneNumber)
                .HasMaxLength(100);
            b.Property(x => x.Email)
                .HasMaxLength(100);
            b.Property(x => x.Nationality)
                .HasMaxLength(100);
            b.Property(x => x.CompanyName)
                .HasMaxLength(200);
            b.Property(x => x.Attachment)
                .HasMaxLength(1000);


            b.HasOne(x => x.Status)
                .WithMany(x => x.Guests)
                .HasForeignKey(x => x.StatusId)
                .IsRequired(false)
                .OnDelete(DeleteBehavior.Restrict);

            b.HasIndex(x => x.Email);
            b.HasIndex(x => x.IdentityNumber); // Updated index name

            // Additional performance indexes for Guest entity
            b.HasIndex(x => x.Fullname); // For name-based searches
            b.HasIndex(x => x.PhoneNumber); // For phone number searches
            b.HasIndex(x => x.StatusId); // For status filtering
            b.HasIndex(x => x.CompanyName); // For company-based filtering
            b.HasIndex(x => x.Nationality); // For nationality filtering
            b.HasIndex(x => x.CreationTime); // For sorting by creation time
        });

        builder.Entity<ServiceType>(b =>
        {
            b.ToTable(HotelFrontOfficeConsts.DbTablePrefix + "ServiceTypes", HotelFrontOfficeConsts.DbSchema);
            b.ConfigureByConvention();

            b.Property(x => x.Name)
                .IsRequired()
                .HasMaxLength(200);

            b.HasOne(x => x.Status)
                .WithMany(x => x.ServiceTypes)
                .HasForeignKey(x => x.StatusId)
                .IsRequired(false)
                .OnDelete(DeleteBehavior.Restrict);

            b.HasMany(x => x.Services)
                .WithOne(x => x.ServiceType)
                .HasForeignKey(x => x.ServiceTypeId)
                .IsRequired()
                .OnDelete(DeleteBehavior.Restrict);

            b.HasIndex(x => x.Name);

            // Additional performance indexes for ServiceType entity
            b.HasIndex(x => x.StatusId); // For status filtering
            b.HasIndex(x => x.CreationTime); // For sorting by creation time
            b.HasIndex(x => new { x.StatusId, x.Name }); // Status with name filtering
        });

        builder.Entity<Service>(b =>
        {
            b.ToTable(HotelFrontOfficeConsts.DbTablePrefix + "Services", HotelFrontOfficeConsts.DbSchema);
            b.ConfigureByConvention();

            b.Property(x => x.Name)
                .IsRequired()
                .HasMaxLength(200);

            b.Property(x => x.Price)
                .IsRequired()
                .HasColumnType("decimal(18,4)");

            b.Property(x => x.UsageTime)
                .IsRequired();

            b.Property(x => x.Information)
                .IsRequired()
                .HasMaxLength(200);

            b.HasOne(x => x.ServiceType)
                .WithMany(x => x.Services)
                .HasForeignKey(x => x.ServiceTypeId)
                .IsRequired()
                .OnDelete(DeleteBehavior.Restrict);

            b.HasIndex(x => x.Name);

            // Additional performance indexes for Service entity
            b.HasIndex(x => x.ServiceTypeId); // For service type filtering
            b.HasIndex(x => x.Price); // For price range queries
            b.HasIndex(x => x.UsageTime); // For usage time filtering
            b.HasIndex(x => x.CreationTime); // For sorting by creation time
            b.HasIndex(x => new { x.ServiceTypeId, x.Price }); // Service type with price filtering
            b.HasIndex(x => new { x.Name, x.ServiceTypeId }); // Name with service type filtering
        });

        builder.Entity<RoomStatus>(b =>
        {
            b.ToTable(HotelFrontOfficeConsts.DbTablePrefix + "RoomStatuses", HotelFrontOfficeConsts.DbSchema);
            b.ConfigureByConvention();

            b.Property(x => x.Name)
                .IsRequired()
                .HasMaxLength(200);

            b.Property(x => x.Color)
                .IsRequired()
                .HasMaxLength(200);

            b.Property(x => x.Code)
                .IsRequired()
                .HasDefaultValue(1);

            b.HasMany(x => x.Rooms)
                .WithOne(x => x.RoomStatus)
                .HasForeignKey(x => x.RoomStatusId)
                .IsRequired()
                .OnDelete(DeleteBehavior.Restrict);

            b.HasIndex(x => x.Name);

            // Additional performance indexes for RoomStatus entity
            b.HasIndex(x => x.Code); // For code-based searches
            b.HasIndex(x => x.Color); // For color-based filtering
            b.HasIndex(x => x.CreationTime); // For sorting by creation time
            b.HasIndex(x => new { x.Code, x.Name }); // Code with name filtering
        });

        builder.Entity<Tax>(b =>
        {
            b.ToTable(HotelFrontOfficeConsts.DbTableMasterPrefix + "Taxes", HotelFrontOfficeConsts.DbSchema);
            b.ConfigureByConvention();

            b.Property(x => x.Name)
                .IsRequired()
                .HasMaxLength(200);

            b.Property(x => x.Code)
                .IsRequired()
                .HasMaxLength(200);

            b.Property(x => x.Rate)
                .IsRequired()
                .HasColumnType("decimal(18,2)");

            b.Property(x => x.StartDate)
                .IsRequired();

            b.Property(x => x.EndDate)
                .IsRequired();

            b.Property(x => x.IsActive)
                .IsRequired();

            b.HasIndex(x => x.Name);
            b.HasIndex(x => x.Code).IsUnique();
        });

        builder.Entity<Report>(b =>
        {
            b.ToTable(HotelFrontOfficeConsts.DbTableMasterPrefix + "Reports", HotelFrontOfficeConsts.DbSchema);
            b.ConfigureByConvention();

            b.Property(x => x.Name)
                .IsRequired()
                .HasMaxLength(200);

            b.Property(x => x.Description)
                .HasMaxLength(200);

            b.HasIndex(x => x.Name);
        });

        builder.Entity<RoomType>(b =>
        {
            b.ToTable(HotelFrontOfficeConsts.DbTablePrefix + "RoomTypes", HotelFrontOfficeConsts.DbSchema);
            b.ConfigureByConvention();

            b.Property(x => x.Name)
                .IsRequired()
                .HasMaxLength(200);

            b.HasOne(x => x.Status)
                .WithMany(x => x.RoomTypes)
                .HasForeignKey(x => x.StatusId)
                .IsRequired(false)
                .OnDelete(DeleteBehavior.Restrict);

            b.Property(x => x.Alias)
                .IsRequired()
                .HasMaxLength(200);

            b.HasMany(x => x.Rooms)
                .WithOne(x => x.RoomType)
                .HasForeignKey(x => x.RoomTypeId)
                .IsRequired()
                .OnDelete(DeleteBehavior.Restrict);

            b.HasIndex(x => x.Name);

            // Additional performance indexes for RoomType entity
            b.HasIndex(x => x.StatusId); // For status filtering
            b.HasIndex(x => x.Alias); // For alias-based searches
            b.HasIndex(x => x.CreationTime); // For sorting by creation time
            b.HasIndex(x => new { x.StatusId, x.Name }); // Status with name filtering
            b.HasIndex(x => new { x.Alias, x.StatusId }); // Alias with status filtering
        });

        builder.Entity<Room>(b =>
        {
            b.ToTable(HotelFrontOfficeConsts.DbTablePrefix + "Room", HotelFrontOfficeConsts.DbSchema);
            b.ConfigureByConvention();

            b.Property(x => x.RoomNumber)
                .IsRequired()
                .HasMaxLength(200);
            b.Property(x => x.RoomCode)
                .IsRequired()
                .HasMaxLength(200);
            b.Property(x => x.Size)
                .IsRequired()
                .HasMaxLength(200);
            b.Property(x => x.Information)
                .IsRequired()
                .HasMaxLength(200);
            b.Property(x => x.Price)
                .HasColumnType("decimal(18,4)");
            b.HasOne(x => x.RoomStatus)
                .WithMany(x => x.Rooms)
                .HasForeignKey(x => x.RoomStatusId)
                .IsRequired()
                .OnDelete(DeleteBehavior.Restrict);

            // Performance indexes for Room entity
            b.HasIndex(x => x.RoomNumber); // For room number searches
            b.HasIndex(x => x.RoomCode); // For room code searches
            b.HasIndex(x => x.RoomStatusId); // For status filtering
            b.HasIndex(x => x.RoomTypeId); // For type filtering
            b.HasIndex(x => x.Price); // For price range queries
            b.HasIndex(x => new { x.RoomStatusId, x.RoomTypeId }); // Combined status and type filtering
            b.HasIndex(x => new { x.RoomTypeId, x.Price }); // Type with price filtering
        });

        builder.Entity<TypeFoodAndBeverage>(b =>
        {
            b.ToTable(HotelFrontOfficeConsts.DbTablePrefix + "TypeFoodAndBeverage", HotelFrontOfficeConsts.DbSchema);
            b.ConfigureByConvention();

            b.Property(x => x.Name)
                .IsRequired()
                .HasMaxLength(200);

            b.HasOne(x => x.Status)
                .WithMany(x => x.TypeFoodAndBeverages)
                .HasForeignKey(x => x.StatusId)
                .IsRequired(false)
                .OnDelete(DeleteBehavior.Restrict);

            b.HasIndex(x => x.Name);

            // Additional performance indexes for TypeFoodAndBeverage entity
            b.HasIndex(x => x.StatusId); // For status filtering
            b.HasIndex(x => x.CreationTime); // For sorting by creation time
            b.HasIndex(x => new { x.StatusId, x.Name }); // Status with name filtering
        });

        builder.Entity<FoodAndBeverage>(b =>
        {
            b.ToTable(HotelFrontOfficeConsts.DbTablePrefix + "FoodAndBeverage", HotelFrontOfficeConsts.DbSchema);
            b.ConfigureByConvention();

            b.Property(x => x.Name)
                .IsRequired()
                .HasMaxLength(200);

            b.Property(x => x.Price)
                .IsRequired()
                .HasColumnType("decimal(18,4)");

            b.Property(x => x.Information)
                .IsRequired()
                .HasMaxLength(200);


            b.HasOne(x => x.TypeFoodAndBeverage)
                .WithMany(x => x.FoodAndBeverages)
                .HasForeignKey(x => x.TypeFoodAndBeverageId)
                .IsRequired()
                .OnDelete(DeleteBehavior.Restrict);

            b.HasIndex(x => x.Name);

            // Additional performance indexes for FoodAndBeverage entity
            b.HasIndex(x => x.TypeFoodAndBeverageId); // For type filtering
            b.HasIndex(x => x.Price); // For price range queries
            b.HasIndex(x => x.CreationTime); // For sorting by creation time
            b.HasIndex(x => new { x.TypeFoodAndBeverageId, x.Price }); // Type with price filtering
            b.HasIndex(x => new { x.Name, x.TypeFoodAndBeverageId }); // Name with type filtering
        });

        builder.Entity<Attachment>(b =>
        {
            b.ToTable(HotelFrontOfficeConsts.DbTablePrefix + "Attachments", HotelFrontOfficeConsts.DbSchema);
            b.ConfigureByConvention();

            b.Property(x => x.FileName)
                .IsRequired()
                .HasMaxLength(256);

            b.Property(x => x.ContentType)
                .IsRequired()
                .HasMaxLength(128);

            b.Property(x => x.Size)
                .IsRequired();

            b.Property(x => x.BlobName)
                .IsRequired()
                .HasMaxLength(512);

            b.Property(x => x.Description)
                .HasMaxLength(1000);

            b.Property(x => x.ReferenceType)
                .HasMaxLength(50);

            b.HasIndex(x => x.ReferenceId);
            b.HasIndex(x => x.ReferenceType);
            b.HasIndex(x => new { x.ReferenceId, x.ReferenceType });
        });

        builder.Entity<TemporaryZipFile>(b =>
        {
            b.ToTable(HotelFrontOfficeConsts.DbTablePrefix + "TemporaryZipFiles", HotelFrontOfficeConsts.DbSchema);
            b.ConfigureByConvention();

            b.Property(x => x.BlobName)
                .IsRequired()
                .HasMaxLength(512);

            b.Property(x => x.DeleteAfter)
                .IsRequired();

            b.Property(x => x.IsProcessed)
                .IsRequired()
                .HasDefaultValue(false);

            b.HasIndex(x => x.DeleteAfter);
            b.HasIndex(x => x.IsProcessed);
        });

        builder.Entity<DocumentTemplate>(b =>
        {
            b.ToTable(HotelFrontOfficeConsts.DbTablePrefix + "DocumentTemplates", HotelFrontOfficeConsts.DbSchema);
            b.ConfigureByConvention();

            b.Property(x => x.Name)
                .IsRequired()
                .HasMaxLength(100);

            b.Property(x => x.DocumentType)
                .IsRequired();

            b.Property(x => x.AttachmentId)
                .IsRequired();

            b.Property(x => x.Description)
                .HasMaxLength(500);

            b.Property(x => x.IsDefault)
                .IsRequired()
                .HasDefaultValue(false);

            b.HasIndex(x => x.DocumentType);
            b.HasIndex(x => x.AttachmentId);
            b.HasIndex(x => new { x.DocumentType, x.IsDefault });
        });

        // Configure TickerQ tables with the correct schema
        builder.Entity<CronTicker>(b => { b.ToTable("CronTickers", "ticker"); });

        builder.Entity<TimeTicker>(b => { b.ToTable("TimeTickers", "ticker"); });

        builder.Entity<CronTickerOccurrence<CronTicker>>(b => { b.ToTable("CronTickerOccurrences", "ticker"); });

        // Configure Entity Change Log entities
        builder.Entity<EntityChangeLog>(b =>
        {
            b.ToTable(HotelFrontOfficeConsts.DbTableLogPrefix + "EntityChangeLogs", HotelFrontOfficeConsts.DbSchema);
            b.ConfigureByConvention();

            b.Property(x => x.EntityId)
                .IsRequired()
                .HasMaxLength(128);

            b.Property(x => x.EntityTypeFullName)
                .IsRequired()
                .HasMaxLength(128);

            b.Property(x => x.EntityDisplayName)
                .HasMaxLength(128);

            b.Property(x => x.EntityDisplayValue)
                .HasMaxLength(256);

            b.HasIndex(x => x.EntityTypeFullName);
            b.HasIndex(x => x.EntityId);
            b.HasIndex(x => x.AuditLogId);
            b.HasIndex(x => x.ChangeTime);
            b.HasIndex(x => x.ChangeType);
        });

        builder.Entity<EntityPropertyChangeLog>(b =>
        {
            b.ToTable(HotelFrontOfficeConsts.DbTableLogPrefix + "EntityPropertyChangeLogs", HotelFrontOfficeConsts.DbSchema);
            b.ConfigureByConvention();

            b.Property(x => x.PropertyName)
                .IsRequired()
                .HasMaxLength(128);

            b.Property(x => x.PropertyDisplayName)
                .HasMaxLength(128);

            b.Property(x => x.PropertyTypeFullName)
                .IsRequired()
                .HasMaxLength(256);

            b.Property(x => x.OriginalValue)
                .HasMaxLength(512);

            b.Property(x => x.NewValue)
                .HasMaxLength(512);

            b.HasOne(x => x.EntityChangeLog)
                .WithMany(x => x.PropertyChanges)
                .HasForeignKey(x => x.EntityChangeLogId)
                .IsRequired()
                .OnDelete(DeleteBehavior.Cascade);

            b.HasIndex(x => x.EntityChangeLogId);
            b.HasIndex(x => x.PropertyName);
        });
    }
}