﻿using System;
using System.Collections.Generic;
using Imip.HotelFrontOffice.RoomStatuses;
using Imip.HotelFrontOffice.RoomTypes;
using Volo.Abp.Application.Dtos;

namespace Imip.HotelFrontOffice.Rooms;

public class RoomDto : AuditedEntityDto<Guid>
{
    public decimal? Price { get; set; }
    public string? Size { get; set; }
    public string RoomNumber { get; set; } = default!;
    public string RoomCode { get; set; } = default!;
    public string Information { get; set; } = default!;

    public Guid RoomStatusId { get; set; }
    public RoomStatusDto RoomStatus { get; set; } = default!;

    public Guid RoomTypeId { get; set; }
    public RoomTypeDto RoomType { get; set; } = default!;

    // Dictionary for extra properties
    public Dictionary<string, object> ExtraProperties { get; set; } = new Dictionary<string, object>();
}