﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.MasterStatuses;
using Imip.HotelFrontOffice.Permissions;
using Imip.HotelFrontOffice.ReservationFoodAndBeverages;
using Imip.HotelFrontOffice.ReservationRooms;
using Imip.HotelFrontOffice.Reservations;
using Imip.HotelFrontOffice.Rooms;
using Imip.HotelFrontOffice.RoomStatuses;
using Imip.HotelFrontOffice.RoomStatusLogs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Settings;

namespace Imip.HotelFrontOffice.ReservationDetails;

[Authorize(WismaAppPermissions.PolicyPaymentDetails.Default)]
public class ReservationDetailsAppService : PermissionCheckedCrudAppService<
    ReservationDetail,
    ReservationDetailsDto,
    Guid,
    PagedAndSortedResultRequestDto,
    CreateUpdateReservationDetailsDto,
    CreateUpdateReservationDetailsDto
>, IReservationDetailsAppService
{
    private readonly IRepository<Room, Guid> _roomRepository;
    private readonly IRepository<RoomStatus, Guid> _roomStatusRepository;
    private readonly IRepository<MasterStatus, Guid> _masterStatusRepository;
    private readonly IRepository<RoomStatusLog, Guid> _roomStatusLogRepository;
    private readonly IRepository<Reservation, Guid> _reservationRepository;
    private readonly IRepository<ReservationRoom, Guid> _reservationRoomRepository;
    private readonly IRepository<ReservationFoodAndBeverage, Guid> _reservationFoodAndBeverageRepository;
    private readonly ISettingProvider _settingProvider;
    private readonly ILogger<ReservationDetailsAppService> _logger;

    public ReservationDetailsAppService(
        IRepository<ReservationDetail, Guid> repository,
        IPermissionChecker permissionChecker,
        IRepository<Room, Guid> roomRepository,
        IRepository<RoomStatus, Guid> roomStatusRepository,
        IRepository<MasterStatus, Guid> masterStatusRepository,
        IRepository<RoomStatusLog, Guid> roomStatusLogRepository,
        IRepository<Reservation, Guid> reservationRepository,
        IRepository<ReservationRoom, Guid> reservationRoomRepository,
        IRepository<ReservationFoodAndBeverage, Guid> reservationFoodAndBeverageRepository,
        ISettingProvider settingProvider,
        ILogger<ReservationDetailsAppService> logger)
        : base(repository, permissionChecker)
    {
        _roomRepository = roomRepository;
        _roomStatusRepository = roomStatusRepository;
        _masterStatusRepository = masterStatusRepository;
        _roomStatusLogRepository = roomStatusLogRepository;
        _reservationRepository = reservationRepository;
        _reservationRoomRepository = reservationRoomRepository;
        _reservationFoodAndBeverageRepository = reservationFoodAndBeverageRepository;
        _settingProvider = settingProvider;
        _logger = logger;

        GetPolicyName = WismaAppPermissions.PolicyPaymentDetails.View;
        GetListPolicyName = WismaAppPermissions.PolicyPaymentDetails.View;
        CreatePolicyName = WismaAppPermissions.PolicyPaymentDetails.Create;
        UpdatePolicyName = WismaAppPermissions.PolicyPaymentDetails.Edit;
        DeletePolicyName = WismaAppPermissions.PolicyPaymentDetails.Delete;
    }
    protected override async Task<IQueryable<ReservationDetail>> CreateFilteredQueryAsync(
        PagedAndSortedResultRequestDto input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .Include(x => x.Reservation)
            .Include(x => x.Room)
            .Include(x => x.Guest);
    }

    public override async Task<ReservationDetailsDto> GetAsync(Guid id)
    {
        var query = await Repository.GetQueryableAsync();

        // Use explicit null-forgiving operator (!) to handle nullability warnings
        // This tells the compiler that we're aware of the nullability and are handling it appropriately
        var reservationDetails = await query
            .AsNoTracking()
            .Include(x => x.Reservation)
            .Include(x => x.Criteria)
            .Include(x => x.Room!)
                .ThenInclude(r => r.RoomType!)
            .Include(x => x.Room!)
               .ThenInclude(r => r.RoomStatus!)
            .Include(x => x.Guest)
            .Include(x => x.Status)
            .Include(x => x.PaymentStatus)
            .Include(x => x.ReservationRooms!)
                .ThenInclude(s => s.PaymentStatus)
            .Include(x => x.ReservationFoodAndBeverages!)
                .ThenInclude(rf => rf.PaymentStatus)
            // Use null-forgiving operator (!) to handle nullability warnings
            .Include(x => x.ReservationRooms!)
                .ThenInclude(rr => rr.Services!)
                    .ThenInclude(s => s.ServiceType)
            .Include(x => x.ReservationFoodAndBeverages!)
                .ThenInclude(rf => rf.FoodAndBeverage!)
                    .ThenInclude(f => f.TypeFoodAndBeverage)
            .FirstOrDefaultAsync(x => x.Id == id);

        // Use pattern matching for null check (modern C# style)
        if (reservationDetails is null)
        {
            throw new EntityNotFoundException(typeof(ReservationDetail), id);
        }

        return ObjectMapper.Map<ReservationDetail, ReservationDetailsDto>(reservationDetails);
    }

    public override async Task<PagedResultDto<ReservationDetailsDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        var query = await Repository.GetQueryableAsync();
        var totalCount = await query.CountAsync();

        // Use explicit null-forgiving operator (!) to handle nullability warnings
        var items = await query
            .AsNoTracking()
            .Include(x => x.Reservation)
            .Include(x => x.Criteria)
            .Include(x => x.Room)
                .ThenInclude(r => r != null ? r.RoomStatus : null)
            .Include(x => x.Room)
                .ThenInclude(r => r != null ? r.RoomType : null)
            .Include(x => x.Guest)
            .Include(x => x.Status)
            .Include(x => x.PaymentStatus)
            .Include(x => x.ReservationRooms!)
                .ThenInclude(s => s.PaymentStatus)
            .Include(x => x.ReservationFoodAndBeverages!)
                .ThenInclude(rf => rf.PaymentStatus)
            .Include(x => x.ReservationRooms!)
                .ThenInclude(rr => rr.Services!)
                    .ThenInclude(s => s.ServiceType)
            .Include(x => x.ReservationFoodAndBeverages!)
                .ThenInclude(rf => rf.FoodAndBeverage!)
                    .ThenInclude(f => f.TypeFoodAndBeverage)
            .OrderByDescending(x => x.CreationTime)
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        var reservationDetailsDtos = ObjectMapper.Map<List<ReservationDetail>, List<ReservationDetailsDto>>(items);

        return new PagedResultDto<ReservationDetailsDto>
        {
            TotalCount = totalCount,
            Items = reservationDetailsDtos
        };
    }

    public override async Task<ReservationDetailsDto> UpdateAsync(Guid id, CreateUpdateReservationDetailsDto input)
    {
        await CheckUpdatePolicyAsync();

        // Get the existing reservation detail with related entities
        var reservationDetail = await Repository.GetAsync(id);
        var previousStatusId = reservationDetail.StatusId;

        // Get the room associated with this reservation detail
        var room = await _roomRepository.GetAsync(reservationDetail.RoomId);
        if (room == null)
        {
            throw new EntityNotFoundException(typeof(Room), reservationDetail.RoomId);
        }

        // Update the reservation detail properties
        reservationDetail.CheckInDate = input.CheckInDate;
        reservationDetail.CheckOutDate = input.CheckOutDate;
        reservationDetail.Rfid = input.Rfid;
        reservationDetail.Price = input.Price;
        reservationDetail.RoomId = input.RoomId;
        reservationDetail.CriteriaId = input.CriteriaId;
        reservationDetail.GuestId = input.GuestId ?? reservationDetail.GuestId;
        reservationDetail.StatusId = input.StatusId;
        reservationDetail.PaymentStatusId = input.PaymentStatusId ?? reservationDetail.PaymentStatusId;

        // Save the updated reservation detail
        await Repository.UpdateAsync(reservationDetail);

        // Check if the status has changed
        if (previousStatusId != input.StatusId && input.StatusId != Guid.Empty)
        {
            // Get the new status
            var newStatus = await _masterStatusRepository.GetAsync(input.StatusId);
            if (newStatus != null)
            {
                // Handle status change based on the status name
                await HandleRoomStatusChangeAsync(room, newStatus, reservationDetail);
            }
        }

        // Return the updated entity
        return await GetAsync(id);
    }

    private async Task HandleRoomStatusChangeAsync(Room room, MasterStatus newReservationStatus, ReservationDetail reservationDetail)
    {
        try
        {
            // Get status names from settings
            // var checkInStatusName = await _settingProvider.GetOrNullAsync(HotelFrontOfficeSettings.ReservationStatus.CheckInStatusName);
            // var checkOutStatusName = await _settingProvider.GetOrNullAsync(HotelFrontOfficeSettings.ReservationStatus.CheckOutStatusName);
            // var occupiedInStayStatusName = await _settingProvider.GetOrNullAsync(HotelFrontOfficeSettings.RoomStatus.OccupiedInStayStatusName);
            // var dirtyStatusName = await _settingProvider.GetOrNullAsync(HotelFrontOfficeSettings.RoomStatus.DirtyStatusName);

            var checkInStatus = await _masterStatusRepository.FirstOrDefaultAsync(
                ms => ms.Code != null && ms.Code.ToLower() == "checkin");
            var checkOutStatus = await _masterStatusRepository.FirstOrDefaultAsync(
                ms => ms.Code != null && ms.Code.ToLower() == "checkout");

            // Check if the statuses were found
            if (checkInStatus is null || checkOutStatus is null || string.IsNullOrEmpty(newReservationStatus.Code))
            {
                _logger.LogWarning("Required statuses not found or have null Code. CheckIn: {CheckInFound}, CheckOut: {CheckOutFound}, NewStatus.Code: {NewStatusCodeFound}",
                    checkInStatus != null, checkOutStatus != null, !string.IsNullOrEmpty(newReservationStatus.Code));
                return; // Exit early if statuses are missing
            }

            // Check if the status code matches the Check IN status
            if (newReservationStatus.Code != null && checkInStatus.Code != null &&
                newReservationStatus.Code.ToLower() == checkInStatus.Code.ToLower())
            {
                // Find the "OVC IN Stay" room status
                var occupiedInStayStatus = await _roomStatusRepository.FirstOrDefaultAsync(
                    rs => rs.Code != null && rs.Code.ToLower() == "ovc");

                if (occupiedInStayStatus != null)
                {
                    // Update the room status
                    await UpdateRoomStatusAsync(room, occupiedInStayStatus.Id, $"Reservation {checkInStatus.Name}");
                }
                else
                {
                    _logger.LogWarning("Could not find 'OVC' room status for Check IN");
                }
            }
            // Check if the status code matches the Check OUT status
            else if (newReservationStatus.Code != null && checkOutStatus.Code != null &&
                     newReservationStatus.Code.ToLower() == checkOutStatus.Code.ToLower())
            {
                // Find the "Dirty" room status
                var dirtyStatus = await _roomStatusRepository.FirstOrDefaultAsync(
                    rs => rs.Code != null && rs.Code.ToLower() == "vd");

                if (dirtyStatus != null)
                {
                    // Update the room status
                    await UpdateRoomStatusAsync(room, dirtyStatus.Id, $"Reservation {checkOutStatus.Name}");
                }
                else
                {
                    _logger.LogWarning("Could not find 'VD' room status for Check OUT");
                }

                // Get the reservation detail to find related entities
                if (reservationDetail != null)
                {
                    // Update all related entities to "Closed" status
                    await UpdateRelatedEntitiesToClosedStatusAsync(reservationDetail);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating room status for room {RoomId}: {Message}", room.Id, ex.Message);
            // Don't throw the exception to avoid disrupting the main update operation
        }
    }

    private async Task UpdateRoomStatusAsync(Room room, Guid newStatusId, string statusSource)
    {
        // Update the room status
        room.RoomStatusId = newStatusId;
        await _roomRepository.UpdateAsync(room);

        // Create a room status log entry
        var roomStatusLog = new RoomStatusLog(
            GuidGenerator.Create(),
            room.Id,
            newStatusId,
            statusSource
        );

        await _roomStatusLogRepository.InsertAsync(roomStatusLog);
    }

    /// <summary>
    /// Updates all related entities to "Closed" status when a reservation is checked out,
    /// but only if all payment conditions are met
    /// </summary>
    private async Task UpdateRelatedEntitiesToClosedStatusAsync(ReservationDetail reservationDetail)
    {
        try
        {
            // Find the "Closed" status for each entity type
            var reservationClosedStatus = await _masterStatusRepository.FirstOrDefaultAsync(
                ms => ms.DocType == "reservations" && ms.Code != null && ms.Code.ToLower() == "close");

            // Find the "Paid" payment status
            var paidStatus = await _masterStatusRepository.FirstOrDefaultAsync(
                ms => ms.DocType == "paymentStatus" && ms.Code != null && ms.Code.ToLower() == "paid");

            if (reservationClosedStatus == null || paidStatus == null)
            {
                _logger.LogWarning("Could not find all required statuses. Reservation Closed: {ReservationClosedFound}, " +
                    "Paid: {PaidFound}",
                    reservationClosedStatus != null, paidStatus != null);
                return; // Exit early if statuses are missing
            }

            // Check if all payment conditions are met
            bool allPaymentConditionsMet = await AreAllPaymentConditionsMetAsync(reservationDetail, paidStatus.Id);

            if (!allPaymentConditionsMet)
            {
                _logger.LogWarning("Not all payment conditions are met for reservation detail {ReservationDetailId}. " +
                    "Skipping update to Closed status.", reservationDetail.Id);
                return; // Exit early if payment conditions are not met
            }

            // 1. Update the main Reservation entity
            if (reservationDetail.ReservationId != Guid.Empty)
            {
                var reservation = await _reservationRepository.GetAsync(reservationDetail.ReservationId);

                // Check if all reservation details for this reservation have paid status
                bool allReservationDetailsPaid = await AreAllReservationDetailsPaidAsync(reservation.Id, paidStatus.Id);

                if (allReservationDetailsPaid)
                {
                    reservation.StatusId = reservationClosedStatus.Id;
                    await _reservationRepository.UpdateAsync(reservation);
                }
            }

            // 2. Update the ReservationDetail entity
            reservationDetail.StatusId = reservationClosedStatus.Id;
            await Repository.UpdateAsync(reservationDetail);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating related entities to Closed status for reservation detail {ReservationDetailId}: {Message}",
                reservationDetail.Id, ex.Message);
            // Don't throw the exception to avoid disrupting the main update operation
        }
    }

    /// <summary>
    /// Checks if all payment conditions are met for a reservation detail
    /// </summary>
    /// <param name="reservationDetail">The reservation detail to check</param>
    /// <param name="paidStatusId">The ID of the "Paid" status</param>
    /// <returns>True if all payment conditions are met, false otherwise</returns>
    private async Task<bool> AreAllPaymentConditionsMetAsync(ReservationDetail reservationDetail, Guid paidStatusId)
    {
        try
        {
            // Check if the reservation detail itself has a paid payment status
            if (reservationDetail.PaymentStatusId != paidStatusId)
            {
                return false;
            }

            // Check if all reservation food and beverage items have paid status
            bool allFoodAndBeveragesPaid = await AreAllFoodAndBeveragesPaidAsync(reservationDetail.Id, paidStatusId);
            if (!allFoodAndBeveragesPaid)
            {
                return false;
            }

            // Check if all reservation room items have paid status
            bool allRoomsPaid = await AreAllRoomsPaidAsync(reservationDetail.Id, paidStatusId);
            if (!allRoomsPaid)
            {
                return false;
            }

            // All conditions are met
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking payment conditions for reservation detail {ReservationDetailId}: {Message}",
                reservationDetail.Id, ex.Message);
            return false; // In case of error, assume conditions are not met
        }
    }

    /// <summary>
    /// Checks if all reservation details for a reservation have paid payment status
    /// </summary>
    /// <param name="reservationId">The ID of the reservation to check</param>
    /// <param name="paidStatusId">The ID of the "Paid" status</param>
    /// <returns>True if all reservation details have paid status, false otherwise</returns>
    private async Task<bool> AreAllReservationDetailsPaidAsync(Guid reservationId, Guid paidStatusId)
    {
        try
        {
            // Get all reservation details for this reservation
            var reservationDetails = await Repository.GetListAsync(
                rd => rd.ReservationId == reservationId && !rd.IsDeleted);

            // If there are no reservation details, return true (nothing to check)
            if (reservationDetails.Count == 0)
            {
                return true;
            }

            // Check if all reservation details have paid payment status
            foreach (var rd in reservationDetails)
            {
                if (rd.PaymentStatusId != paidStatusId)
                {
                    return false;
                }
            }

            // All reservation details have paid status
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if all reservation details are paid for reservation {ReservationId}: {Message}",
                reservationId, ex.Message);
            return false; // In case of error, assume not all are paid
        }
    }

    /// <summary>
    /// Checks if all food and beverage items for a reservation detail have paid payment status
    /// </summary>
    /// <param name="reservationDetailId">The ID of the reservation detail to check</param>
    /// <param name="paidStatusId">The ID of the "Paid" status</param>
    /// <returns>True if all food and beverage items have paid status, false otherwise</returns>
    private async Task<bool> AreAllFoodAndBeveragesPaidAsync(Guid reservationDetailId, Guid paidStatusId)
    {
        try
        {
            // Get all food and beverage items for this reservation detail
            var foodAndBeverages = await _reservationFoodAndBeverageRepository.GetListAsync(
                fb => fb.ReservationDetailsId == reservationDetailId && !fb.IsDeleted);

            // If there are no food and beverage items, return true (nothing to check)
            if (foodAndBeverages.Count == 0)
            {
                return true;
            }

            // Check if all food and beverage items have paid payment status
            foreach (var fb in foodAndBeverages)
            {
                if (fb.PaymentStatusId != paidStatusId)
                {
                    return false;
                }
            }

            // All food and beverage items have paid status
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if all food and beverage items are paid for reservation detail {ReservationDetailId}: {Message}",
                reservationDetailId, ex.Message);
            return false; // In case of error, assume not all are paid
        }
    }

    /// <summary>
    /// Checks if all room items for a reservation detail have paid payment status
    /// </summary>
    /// <param name="reservationDetailId">The ID of the reservation detail to check</param>
    /// <param name="paidStatusId">The ID of the "Paid" status</param>
    /// <returns>True if all room items have paid status, false otherwise</returns>
    private async Task<bool> AreAllRoomsPaidAsync(Guid reservationDetailId, Guid paidStatusId)
    {
        try
        {
            // Get all room items for this reservation detail
            var rooms = await _reservationRoomRepository.GetListAsync(
                r => r.ReservationDetailsId == reservationDetailId && !r.IsDeleted);

            // If there are no room items, return true (nothing to check)
            if (rooms.Count == 0)
            {
                return true;
            }

            // Check if all room items have paid payment status
            foreach (var r in rooms)
            {
                if (r.PaymentStatusId != paidStatusId)
                {
                    return false;
                }
            }

            // All room items have paid status
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if all room items are paid for reservation detail {ReservationDetailId}: {Message}",
                reservationDetailId, ex.Message);
            return false; // In case of error, assume not all are paid
        }
    }


}
