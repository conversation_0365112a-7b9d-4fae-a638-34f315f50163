using System.Threading.Tasks;
using Imip.HotelFrontOffice.Users;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc;

namespace Imip.HotelFrontOffice.Web.Controllers;

/// <summary>
/// Controller for user synchronization health checks and monitoring
/// </summary>
[ApiController]
[Route("api/app/user-synchronization")]
public class UserSynchronizationController : AbpControllerBase
{
    private readonly IUserSynchronizationService _userSynchronizationService;

    public UserSynchronizationController(IUserSynchronizationService userSynchronizationService)
    {
        _userSynchronizationService = userSynchronizationService;
    }

    /// <summary>
    /// Gets health check information for user synchronization
    /// </summary>
    /// <returns>Health check information</returns>
    [HttpGet("health")]
    [AllowAnonymous] // Allow anonymous access for health checks
    public async Task<UserSynchronizationHealthDto> GetHealthAsync()
    {
        return await _userSynchronizationService.GetHealthAsync();
    }

    /// <summary>
    /// Gets detailed health check information for user synchronization (requires authentication)
    /// </summary>
    /// <returns>Detailed health check information</returns>
    [HttpGet("health/detailed")]
    [Authorize] // Require authentication for detailed information
    public async Task<UserSynchronizationHealthDto> GetDetailedHealthAsync()
    {
        return await _userSynchronizationService.GetHealthAsync();
    }
}
