﻿using System;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.HotelFrontOffice.Repositories
{
    public class ReservationRoomsRepository : EfCoreRepository<HotelFrontOfficeDbContext, ReservationRooms.ReservationRoom, Guid>, IReservationRoomsRepository
    {
        public ReservationRoomsRepository(IDbContextProvider<HotelFrontOfficeDbContext> dbContextProvider)
            : base(dbContextProvider)
        {
        }

        public async Task<ReservationRooms.ReservationRoom?> FindByNameAsync(string name)
        {
            var dbContext = await GetDbContextAsync();
            return await dbContext.ReservationRooms
                .Include(x => x.ReservationDetails)

                .FirstOrDefaultAsync(); 
        }
    }
}