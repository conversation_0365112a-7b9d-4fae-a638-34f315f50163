using System.IO;
using System.Threading.Tasks;
using Volo.Abp.BlobStoring;
using Volo.Abp.DependencyInjection;

namespace Imip.HotelFrontOffice.BlobStorage;

/// <summary>
/// Example service that demonstrates how to use the SFTP blob storage provider.
/// </summary>
public class SftpBlobStorageExampleService : ITransientDependency
{
    private readonly IBlobContainer _blobContainer;

    public SftpBlobStorageExampleService(IBlobContainerFactory blobContainerFactory)
    {
        // Get a reference to the default container or a named container
        // For named containers, use a specific name
        _blobContainer = blobContainerFactory.Create("default");
    }

    /// <summary>
    /// Saves a file to the SFTP server.
    /// </summary>
    /// <param name="fileName">The name of the file.</param>
    /// <param name="content">The content of the file.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    public async Task SaveFileAsync(string fileName, byte[] content)
    {
        using var stream = new MemoryStream(content);
        await _blobContainer.SaveAsync(fileName, stream);
    }

    /// <summary>
    /// Gets a file from the SFTP server.
    /// </summary>
    /// <param name="fileName">The name of the file.</param>
    /// <returns>The content of the file, or null if the file doesn't exist.</returns>
    public async Task<byte[]> GetFileAsync(string fileName)
    {
        var stream = await _blobContainer.GetAsync(fileName);
        using var memoryStream = new MemoryStream();
        await stream.CopyToAsync(memoryStream);
        return memoryStream.ToArray();
    }

    /// <summary>
    /// Checks if a file exists on the SFTP server.
    /// </summary>
    /// <param name="fileName">The name of the file.</param>
    /// <returns>True if the file exists, false otherwise.</returns>
    public async Task<bool> FileExistsAsync(string fileName)
    {
        return await _blobContainer.ExistsAsync(fileName);
    }

    /// <summary>
    /// Deletes a file from the SFTP server.
    /// </summary>
    /// <param name="fileName">The name of the file.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    public async Task DeleteFileAsync(string fileName)
    {
        await _blobContainer.DeleteAsync(fileName);
    }
}
