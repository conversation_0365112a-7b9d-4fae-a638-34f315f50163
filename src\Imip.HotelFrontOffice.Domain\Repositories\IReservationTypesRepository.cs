﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.Repositories
{
    public interface IReservationTypesRepository : IRepository<ReservationTypes.ReservationType, Guid>
    {
        Task<ReservationTypes.ReservationType?> FindByNameAsync(string name);

        Task<List<ReservationTypes.ReservationType>> GetActiveAsync();
    }
}