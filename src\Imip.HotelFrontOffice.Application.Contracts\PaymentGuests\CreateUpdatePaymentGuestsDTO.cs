﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Imip.HotelFrontOffice.PaymentGuests;

public class CreateUpdatePaymentGuestsDto
{
    /// <summary>
    /// The ID of the payment guest (only used for updates)
    /// </summary>
    public Guid Id { get; set; }

    public Guid PaymentId { get; set; } = default!;

    [Required]
    public Guid GuestId { get; set; } = default!;

    [Required]
    public decimal AmountPaid { get; set; } = default!;
}