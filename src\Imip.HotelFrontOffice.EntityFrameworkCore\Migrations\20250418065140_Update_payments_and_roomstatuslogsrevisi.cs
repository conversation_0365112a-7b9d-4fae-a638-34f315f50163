﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.HotelFrontOffice.Migrations
{
    /// <inheritdoc />
    public partial class Update_payments_and_roomstatuslogsrevisi : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AppRoomStatusLogs_AppRoomStatuses_RoomStatusesId",
                table: "AppRoomStatusLogs");

            migrationBuilder.DropIndex(
                name: "IX_AppRoomStatusLogs_RoomStatusesId",
                table: "AppRoomStatusLogs");

            migrationBuilder.DropColumn(
                name: "RoomStatusesId",
                table: "AppRoomStatusLogs");

            migrationBuilder.CreateIndex(
                name: "IX_AppRoomStatusLogs_RoomStatusId",
                table: "AppRoomStatusLogs",
                column: "RoomStatusId");

            migrationBuilder.AddForeignKey(
                name: "FK_AppRoomStatusLogs_AppRoomStatuses_RoomStatusId",
                table: "AppRoomStatusLogs",
                column: "RoomStatusId",
                principalTable: "AppRoomStatuses",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AppRoomStatusLogs_AppRoomStatuses_RoomStatusId",
                table: "AppRoomStatusLogs");

            migrationBuilder.DropIndex(
                name: "IX_AppRoomStatusLogs_RoomStatusId",
                table: "AppRoomStatusLogs");

            migrationBuilder.AddColumn<Guid>(
                name: "RoomStatusesId",
                table: "AppRoomStatusLogs",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.CreateIndex(
                name: "IX_AppRoomStatusLogs_RoomStatusesId",
                table: "AppRoomStatusLogs",
                column: "RoomStatusId");

            migrationBuilder.AddForeignKey(
                name: "FK_AppRoomStatusLogs_AppRoomStatuses_RoomStatusesId",
                table: "AppRoomStatusLogs",
                column: "RoomStatusId",
                principalTable: "AppRoomStatuses",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
