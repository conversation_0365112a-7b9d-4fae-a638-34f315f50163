﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.FoodAndBeverages;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.Repositories
{
    public interface IFoodAndBeverageRepository : IRepository<FoodAndBeverage, Guid>
    {
        Task<FoodAndBeverage?> FindByNameAsync(string name);

    }
}