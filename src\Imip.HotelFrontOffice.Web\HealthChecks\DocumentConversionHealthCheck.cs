using Microsoft.Extensions.Diagnostics.HealthChecks;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Imip.HotelFrontOffice.Web.HealthChecks;

/// <summary>
/// Health check for document conversion services and memory usage
/// </summary>
public class DocumentConversionHealthCheck : IHealthCheck
{
    private const long MemoryWarningThresholdMB = 1024; // 1GB
    private const long MemoryCriticalThresholdMB = 1536; // 1.5GB

    public Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            // Check memory usage
            var memoryUsage = GC.GetTotalMemory(false);
            var memoryUsageMB = memoryUsage / 1024 / 1024;

            var data = new Dictionary<string, object>
            {
                ["memoryUsageMB"] = memoryUsageMB,
                ["memoryWarningThresholdMB"] = MemoryWarningThresholdMB,
                ["memoryCriticalThresholdMB"] = MemoryCriticalThresholdMB,
                ["generation0Collections"] = GC.CollectionCount(0),
                ["generation1Collections"] = GC.CollectionCount(1),
                ["generation2Collections"] = GC.CollectionCount(2)
            };

            // Determine health status based on memory usage
            if (memoryUsageMB >= MemoryCriticalThresholdMB)
            {
                return Task.FromResult(HealthCheckResult.Unhealthy(
                    $"Memory usage is critical: {memoryUsageMB} MB (threshold: {MemoryCriticalThresholdMB} MB)",
                    data: data));
            }

            if (memoryUsageMB >= MemoryWarningThresholdMB)
            {
                return Task.FromResult(HealthCheckResult.Degraded(
                    $"Memory usage is high: {memoryUsageMB} MB (warning threshold: {MemoryWarningThresholdMB} MB)",
                    data: data));
            }

            return Task.FromResult(HealthCheckResult.Healthy(
                $"Memory usage is normal: {memoryUsageMB} MB",
                data: data));
        }
        catch (Exception ex)
        {
            return Task.FromResult(HealthCheckResult.Unhealthy(
                "Failed to check document conversion health",
                ex));
        }
    }
}
