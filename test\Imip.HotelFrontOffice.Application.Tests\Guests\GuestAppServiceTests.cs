using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Attachments;
using Imip.HotelFrontOffice.Guests;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Shouldly;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Guids;
using Xunit;

namespace Imip.HotelFrontOffice.Application.Tests.Guests;

public class GuestAppServiceTests : HotelFrontOfficeApplicationTestBase<HotelFrontOfficeApplicationTestModule>
{
    private readonly IRepository<Guest, Guid> _mockGuestRepository;
    private readonly IPermissionChecker _mockPermissionChecker;
    private readonly IAttachmentAppService _mockAttachmentAppService;
    private readonly ILogger<GuestAppService> _mockLogger;
    private readonly GuestAppService _guestAppService;
    private readonly IGuidGenerator _guidGenerator;

    // Test data
    private readonly Guid _testGuestId = Guid.NewGuid();
    private readonly Guid _testStatusId = Guid.NewGuid();

    public GuestAppServiceTests()
    {
        // Setup mock repositories and services
        _mockGuestRepository = Substitute.For<IRepository<Guest, Guid>>();
        _mockPermissionChecker = Substitute.For<IPermissionChecker>();
        _mockAttachmentAppService = Substitute.For<IAttachmentAppService>();
        _mockLogger = Substitute.For<ILogger<GuestAppService>>();
        _guidGenerator = GetRequiredService<IGuidGenerator>();

        // Setup permission checker to always return true for simplicity
        _mockPermissionChecker.IsGrantedAsync(Arg.Any<string>()).Returns(true);

        // Create the service under test
        _guestAppService = new GuestAppService(
            _mockGuestRepository,
            _mockPermissionChecker,
            _mockAttachmentAppService,
            _mockLogger);
    }

    [Fact]
    public async Task UploadAttachmentsAsync_WithValidAttachments_ShouldProcessSuccessfully()
    {
        // Arrange
        var guest = new Guest(
            _testGuestId,
            "John Doe",
            "Doe",
            "ID123456",
            "1234567890",
            "<EMAIL>",
            "US",
            "Test Company",
            "",
            _testStatusId);

        var attachments = new List<GuestAttachmentDto>
        {
            new GuestAttachmentDto
            {
                FileName = "passport.pdf",
                ContentType = "application/pdf",
                Base64Content = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes("fake pdf content")),
                Description = "Passport document"
            },
            new GuestAttachmentDto
            {
                FileName = "photo.jpg",
                ContentType = "image/jpeg",
                Base64Content = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes("fake image content")),
                Description = "Guest photo"
            }
        };

        // Setup mock repository to return the guest
        _mockGuestRepository.GetAsync(_testGuestId).Returns(guest);

        // Setup mock attachment service to return success
        _mockAttachmentAppService.UploadFileAsync(
            Arg.Any<FileUploadDto>(),
            Arg.Any<string>(),
            Arg.Any<string>(),
            Arg.Any<byte[]>())
            .Returns(new FileUploadResultDto
            {
                Id = Guid.NewGuid(),
                FileName = "test.pdf",
                ContentType = "application/pdf",
                Size = 1024,
                UploadTime = DateTime.UtcNow,
                Url = "http://example.com/file",
                StreamUrl = "http://example.com/stream"
            });

        // Act
        await _guestAppService.UploadAttachmentsAsync(_testGuestId, attachments);

        // Assert
        await _mockGuestRepository.Received(1).GetAsync(_testGuestId);
        await _mockAttachmentAppService.Received(2).UploadFileAsync(
            Arg.Is<FileUploadDto>(dto => 
                dto.ReferenceId == _testGuestId && 
                dto.ReferenceType == "Guest"),
            Arg.Any<string>(),
            Arg.Any<string>(),
            Arg.Any<byte[]>());
    }

    [Fact]
    public async Task UploadAttachmentsAsync_WithInvalidFileType_ShouldSkipInvalidFiles()
    {
        // Arrange
        var guest = new Guest(
            _testGuestId,
            "John Doe",
            "Doe",
            "ID123456",
            "1234567890",
            "<EMAIL>",
            "US",
            "Test Company",
            "",
            _testStatusId);

        var attachments = new List<GuestAttachmentDto>
        {
            new GuestAttachmentDto
            {
                FileName = "document.txt",
                ContentType = "text/plain", // Invalid file type
                Base64Content = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes("text content")),
                Description = "Text document"
            },
            new GuestAttachmentDto
            {
                FileName = "photo.jpg",
                ContentType = "image/jpeg", // Valid file type
                Base64Content = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes("fake image content")),
                Description = "Guest photo"
            }
        };

        // Setup mock repository to return the guest
        _mockGuestRepository.GetAsync(_testGuestId).Returns(guest);

        // Setup mock attachment service
        _mockAttachmentAppService.UploadFileAsync(
            Arg.Any<FileUploadDto>(),
            Arg.Any<string>(),
            Arg.Any<string>(),
            Arg.Any<byte[]>())
            .Returns(new FileUploadResultDto
            {
                Id = Guid.NewGuid(),
                FileName = "photo.jpg",
                ContentType = "image/jpeg",
                Size = 1024,
                UploadTime = DateTime.UtcNow,
                Url = "http://example.com/file",
                StreamUrl = "http://example.com/stream"
            });

        // Act
        await _guestAppService.UploadAttachmentsAsync(_testGuestId, attachments);

        // Assert
        await _mockGuestRepository.Received(1).GetAsync(_testGuestId);
        // Should only call upload once for the valid image file
        await _mockAttachmentAppService.Received(1).UploadFileAsync(
            Arg.Any<FileUploadDto>(),
            "photo.jpg",
            "image/jpeg",
            Arg.Any<byte[]>());
    }

    [Fact]
    public async Task UploadAttachmentsAsync_WithEmptyBase64Content_ShouldSkipEmptyFiles()
    {
        // Arrange
        var guest = new Guest(
            _testGuestId,
            "John Doe",
            "Doe",
            "ID123456",
            "1234567890",
            "<EMAIL>",
            "US",
            "Test Company",
            "",
            _testStatusId);

        var attachments = new List<GuestAttachmentDto>
        {
            new GuestAttachmentDto
            {
                FileName = "empty.pdf",
                ContentType = "application/pdf",
                Base64Content = "", // Empty content
                Description = "Empty document"
            }
        };

        // Setup mock repository to return the guest
        _mockGuestRepository.GetAsync(_testGuestId).Returns(guest);

        // Act
        await _guestAppService.UploadAttachmentsAsync(_testGuestId, attachments);

        // Assert
        await _mockGuestRepository.Received(1).GetAsync(_testGuestId);
        // Should not call upload for empty files
        await _mockAttachmentAppService.DidNotReceive().UploadFileAsync(
            Arg.Any<FileUploadDto>(),
            Arg.Any<string>(),
            Arg.Any<string>(),
            Arg.Any<byte[]>());
    }
}
