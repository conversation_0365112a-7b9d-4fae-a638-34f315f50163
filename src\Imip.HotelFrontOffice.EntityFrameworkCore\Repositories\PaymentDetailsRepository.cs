﻿using System;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.HotelFrontOffice.Repositories
{
    public class PaymentDetailsRepository : EfCoreRepository<HotelFrontOfficeDbContext, PaymentDetails.PaymentDetail, Guid>, IPaymentDetailsRepository
    {
        public PaymentDetailsRepository(IDbContextProvider<HotelFrontOfficeDbContext> dbContextProvider)
            : base(dbContextProvider)
        {
        }

        public async Task<PaymentDetails.PaymentDetail?> FindByNameAsync(string name)
        {
            var dbContext = await GetDbContextAsync();
            return await dbContext.PaymentDetails
                .Include(x => x.Payments)
                .FirstOrDefaultAsync(); 
        }
    }
}