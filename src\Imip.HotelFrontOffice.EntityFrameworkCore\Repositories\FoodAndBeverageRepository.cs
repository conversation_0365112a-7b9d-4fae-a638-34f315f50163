﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.EntityFrameworkCore;
using Imip.HotelFrontOffice.FoodAndBeverages;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.HotelFrontOffice.Repositories
{
    public class FoodAndBeverageRepository : EfCoreRepository<HotelFrontOfficeDbContext, FoodAndBeverage, Guid>, IFoodAndBeverageRepository
    {
        public FoodAndBeverageRepository(IDbContextProvider<HotelFrontOfficeDbContext> dbContextProvider)
            : base(dbContextProvider)
        {
        }

        public async Task<FoodAndBeverage?> FindByNameAsync(string name)
        {
            var dbContext = await GetDbContextAsync();
            return await dbContext.FoodAndBeverages
                .Include(x => x.TypeFoodAndBeverage)
                .Where(x => x.Name == name)
                .FirstOrDefaultAsync(); 
        }
    }
}