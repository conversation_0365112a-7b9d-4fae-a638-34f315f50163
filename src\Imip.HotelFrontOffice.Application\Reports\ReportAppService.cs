using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Models;
using Imip.HotelFrontOffice.Permissions;
using Imip.HotelFrontOffice.Reports;
using Imip.HotelFrontOffice.Repositories;
using Imip.HotelFrontOffice.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.ObjectMapping;

namespace Imip.HotelFrontOffice.PolicyReport;

[Route("api/app/report")]
[Authorize(WismaAppPermissions.PolicyReport.Default)]
public class ReportAppService : PermissionCheckedCrudAppService<
    Report,
    ReportDto,
    Guid,
    PagedAndSortedResultRequestDto,
    CreateUpdateReportDto,
    CreateUpdateReportDto
>, IReportAppService
{
    private readonly ILogger<ReportAppService> _logger;
    private readonly IReportExecutionService _reportExecutionService;
    private readonly IReportRepository _reportRepository;

    public ReportAppService(
        IRepository<Report, Guid> repository,
        IReportExecutionService reportExecutionService,
        IReportRepository reportRepository,
        ILogger<ReportAppService> logger,
        IPermissionChecker permissionChecker)
        : base(repository, permissionChecker)
    {
        _logger = logger;
        _reportExecutionService = reportExecutionService;
        _reportRepository = reportRepository;

        GetPolicyName = WismaAppPermissions.PolicyReport.View;
        GetListPolicyName = WismaAppPermissions.PolicyReport.View;
        CreatePolicyName = WismaAppPermissions.PolicyReport.Create;
        UpdatePolicyName = WismaAppPermissions.PolicyReport.Edit;
        DeletePolicyName = WismaAppPermissions.PolicyReport.Delete;
    }

    [HttpGet]
    [Authorize(WismaAppPermissions.PolicyReport.View)]
    public override Task<PagedResultDto<ReportDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        return base.GetListAsync(input);
    }

    [HttpGet("{id}")]
    [Authorize(WismaAppPermissions.PolicyReport.View)]
    public override Task<ReportDto> GetAsync(Guid id)
    {
        return base.GetAsync(id);
    }

    [HttpPost]
    [Authorize(WismaAppPermissions.PolicyReport.Create)]
    public override Task<ReportDto> CreateAsync(CreateUpdateReportDto input)
    {
        return base.CreateAsync(input);
    }

    [HttpPut("{id}")]
    [Authorize(WismaAppPermissions.PolicyReport.Edit)]
    public override Task<ReportDto> UpdateAsync(Guid id, CreateUpdateReportDto input)
    {
        return base.UpdateAsync(id, input);
    }

    [HttpDelete("{id}")]
    [Authorize(WismaAppPermissions.PolicyReport.Delete)]
    public override Task DeleteAsync(Guid id)
    {
        return base.DeleteAsync(id);
    }

    [HttpPost("preview")]
    [Authorize(WismaAppPermissions.PolicyReport.Execute)]
    public async Task<ReportPreviewDto> PreviewReportAsync(ReportExecutionDto input)
    {
        await CheckPermissionAsync(WismaAppPermissions.PolicyReport.Execute);
        return await _reportExecutionService.ExecuteReportAsync(input.ReportId, input.Parameters);
    }

    [HttpPost("export/csv")]
    [Authorize(WismaAppPermissions.PolicyReport.Export)]
    public async Task<byte[]> ExportReportToCsvAsync(ReportExecutionDto input)
    {
        await CheckPermissionAsync(WismaAppPermissions.PolicyReport.Export);
        return await _reportExecutionService.ExportReportToCsvAsync(input.ReportId, input.Parameters);
    }

    [HttpPost("export/excel")]
    [Authorize(WismaAppPermissions.PolicyReport.Export)]
    public async Task<byte[]> ExportReportToExcelAsync(ReportExecutionDto input)
    {
        await CheckPermissionAsync(WismaAppPermissions.PolicyReport.Export);
        return await _reportExecutionService.ExportReportToExcelAsync(input.ReportId, input.Parameters);
    }

    [HttpGet("{reportId}/parameters")]
    [Authorize(WismaAppPermissions.PolicyReport.Default)]
    public async Task<List<ReportParameterDto>> GetReportParametersAsync(Guid reportId)
    {
        await CheckPermissionAsync(WismaAppPermissions.PolicyReport.Default);
        return await _reportExecutionService.GetReportParametersAsync(reportId);
    }

    [HttpGet("active")]
    [Authorize(WismaAppPermissions.PolicyReport.Default)]
    public async Task<List<ReportDto>> GetActiveReportsAsync()
    {
        await CheckPermissionAsync(WismaAppPermissions.PolicyReport.Default);
        var reports = await _reportRepository.GetActiveReportsAsync();
        return ObjectMapper.Map<List<Report>, List<ReportDto>>(reports);
    }

    private async Task CheckPermissionAsync(string permissionName)
    {
        await AuthorizationService.CheckAsync(permissionName);
    }
}
