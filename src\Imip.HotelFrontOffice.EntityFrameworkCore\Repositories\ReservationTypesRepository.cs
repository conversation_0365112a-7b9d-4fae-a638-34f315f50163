﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.HotelFrontOffice.Repositories
{
    public class ReservationTypesRepository : EfCoreRepository<HotelFrontOfficeDbContext, ReservationTypes.ReservationType, Guid>, IReservationTypesRepository
    {
        public ReservationTypesRepository(IDbContextProvider<HotelFrontOfficeDbContext> dbContextProvider)
            : base(dbContextProvider)
        {
        }

        public async Task<List<ReservationTypes.ReservationType>> GetActiveAsync() 
        {
            var dbContext = await GetDbContextAsync();
            return await dbContext.ReservationTypes
                .ToListAsync(); 
        }

        public async Task<ReservationTypes.ReservationType?> FindByNameAsync(string name)
        {
            var dbContext = await GetDbContextAsync();
            return await dbContext.ReservationTypes
                .Where(x => x.Name == name)
                .FirstOrDefaultAsync(); 
        }
    }
}