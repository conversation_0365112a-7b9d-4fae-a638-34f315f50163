﻿using System;
using Imip.HotelFrontOffice.ServiceTypes;
using Volo.Abp.Data;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.HotelFrontOffice.Services;

public class Service : FullAuditedAggregateRoot<Guid>
{
    public string Name { get; set; } = default!;
    public decimal? Price { get; set; }
    public int? UsageTime { get; set; } = default!;
    public string? Information { get; set; } = default!;

    public Guid ServiceTypeId { get; set; }

    public virtual ServiceType ServiceType { get; set; } = default!;

    protected Service()
    {
    }

    public Service(
        Guid id,
        string name,
        decimal price,
        int usageTime,
        string information,
        Guid serviceTypeId
    )
    {
        Id = id;
        Name = name;
        Price = price;
        UsageTime = usageTime;
        Information = information;
        ServiceTypeId = serviceTypeId;
    }

    // Helper methods for ExtraProperties

    /// <summary>
    /// Sets a value in the ExtraProperties dictionary
    /// </summary>
    public void SetExtraProperty<T>(string key, T value)
    {
        this.SetProperty(key, value);
    }

    /// <summary>
    /// Gets a value from the ExtraProperties dictionary
    /// </summary>
    public T GetExtraProperty<T>(string key)
    {
        return this.GetProperty<T>(key);
    }

    /// <summary>
    /// Removes a property from the ExtraProperties dictionary
    /// </summary>
    public void RemoveExtraProperty(string key)
    {
        this.RemoveProperty(key);
    }
}