using System;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Attachments;
using Volo.Abp.Application.Services;

namespace Imip.HotelFrontOffice.Documents.Invoice;

/// <summary>
/// Interface for invoice document service
/// </summary>
public interface IInvoiceDocumentService : IApplicationService
{
    /// <summary>
    /// Generates an invoice document for a payment
    /// </summary>
    /// <param name="input">The invoice generation input</param>
    /// <returns>The generated file</returns>
    Task<FileDto> GenerateInvoiceAsync(InvoiceGenerationDto input);

    /// <summary>
    /// Gets the invoice template data for a payment
    /// </summary>
    /// <param name="paymentId">The payment ID</param>
    /// <returns>The invoice template data</returns>
    Task<InvoiceTemplateDataDto> GetInvoiceTemplateDataAsync(Guid paymentId);

    /// <summary>
    /// Generates a Wisma invoice document for a payment
    /// </summary>
    /// <param name="input">The invoice generation input</param>
    /// <returns>The generated file</returns>
    Task<FileDto> GenerateWismaInvoiceAsync(InvoiceGenerationDto input);

    /// <summary>
    /// Generates a Wisma invoice document for a payment and saves it as an attachment
    /// </summary>
    /// <param name="input">The invoice generation input</param>
    /// <returns>The file upload result with stream URL</returns>
    Task<FileUploadResultDto> GenerateWismaInvoiceAsAttachmentAsync(InvoiceGenerationDto input);
}
