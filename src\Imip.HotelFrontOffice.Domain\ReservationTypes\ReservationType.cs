﻿using System;
using System.Collections.Generic;
using Imip.HotelFrontOffice.MasterStatuses;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.HotelFrontOffice.ReservationTypes;

public class ReservationType : FullAuditedAggregateRoot<Guid>
{
    public string Name { get; set; } = default!;

    public Guid? StatusId { get; set; }

    public virtual MasterStatus? Status { get; set; }

    public virtual ICollection<Reservations.Reservation> Reservations { get; set; }

    protected ReservationType()
    {
        Reservations = new HashSet<Reservations.Reservation>();
    }

    public ReservationType(Guid id, string name, Guid? statusId) : this()
    {
        Id = id;
        Name = name;
        StatusId = statusId;
    }
}