﻿using System;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.EntityFrameworkCore;
using Imip.HotelFrontOffice.ReservationDetails;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.HotelFrontOffice.Repositories;

public class ReservationDetailsRepository : EfCoreRepository<HotelFrontOfficeDbContext, ReservationDetail, Guid>, IReservationDetailsRepository
{
    public ReservationDetailsRepository(IDbContextProvider<HotelFrontOfficeDbContext> dbContextProvider)
        : base(dbContextProvider)
    {
    }

    public async Task<ReservationDetail?> FindByNameAsync(string name)
    {
        var dbContext = await GetDbContextAsync();
        return await dbContext.ReservationDetails
            .Include(x => x.Reservation)

            .FirstOrDefaultAsync();
    }
}