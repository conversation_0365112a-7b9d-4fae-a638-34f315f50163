﻿using System;
using System.ComponentModel.DataAnnotations;
using Imip.HotelFrontOffice.ReservationDetails;
using Imip.HotelFrontOffice.ReservationFoodAndBeverages;
using Imip.HotelFrontOffice.ReservationRooms;
using Volo.Abp.Application.Dtos;

namespace Imip.HotelFrontOffice.PaymentDetails;

public class PaymentDetailsDto : AuditedEntityDto<Guid>
{
    public Guid PaymentId { get; set; }

    [EnumDataType(typeof(PaymentSourceType))]
    public PaymentSourceType SourceType { get; set; } = default!;

    public Guid SourceId { get; set; } = default!;

    public decimal Amount { get; set; } = default!;

    public decimal Qty { get; set; } = default!;
    public decimal UnitPrice { get; set; } = default!;

    public Guid ReservationDetailsId { get; set; } = default!;

    /// <summary>
    /// Using the basic DTO to avoid circular references
    /// </summary>
    public ReservationDetailBasicDto? ReservationDetails { get; set; } = default!;
    public ReservationFoodAndBeveragesDto? ReservationFoodAndBeverages { get; set; } = default!;
    public ReservationRoomsDto? ReservationRoom { get; set; } = default!;
}