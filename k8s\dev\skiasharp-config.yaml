apiVersion: v1
kind: ConfigMap
metadata:
  name: skiasharp-config
  namespace: imip-wisma-dev-new
data:
  LD_LIBRARY_PATH: "/app/runtimes/linux-x64/native:/usr/lib:/usr/lib/x86_64-linux-gnu:/lib:/lib64:/usr/lib64:/skiasharp-lib:/skiasharp-lib/x86_64-linux-gnu"
  SYNCFUSION_DEBUG: "true"
  SKIASHARP_DEBUG: "true"
  SYNCFUSION_LICENSING_PROVIDER_TRACE: "true"
  DOTNET_SYSTEM_GLOBALIZATION_INVARIANT: "false"
  LC_ALL: "en_US.UTF-8"
  LANG: "en_US.UTF-8"
  FONTCONFIG_PATH: "/etc/fonts"
  SKIASHARP_PRELOAD_NATIVE: "true"
  SKIASHARP_NATIVE_SEARCH_PATHS: "/app/runtimes/linux-x64/native:/usr/lib/x86_64-linux-gnu:/usr/lib"
  DOTNET_EnableDiagnostics: "1"
  DOTNET_gcServer: "1"
  DOTNET_gcConcurrent: "1"
