<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>Imip.HotelFrontOffice</RootNamespace>
    <GenerateEmbeddedFilesManifest>true</GenerateEmbeddedFilesManifest>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.AspNetCore.Serilog" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.BackgroundJobs.Domain.Shared" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.AuditLogging.Domain.Shared" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.Ddd.Application.Contracts" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.FeatureManagement.Domain.Shared" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.PermissionManagement.Domain.Shared" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.SettingManagement.Domain.Shared" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.BlobStoring.Database.Domain.Shared" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.GlobalFeatures" Version="9.0.4" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.OpenIddict.Domain.Shared" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.Identity.Domain.Shared" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.TenantManagement.Domain.Shared" Version="9.0.4" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.FileProviders.Embedded" Version="9.0.0" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Localization\HotelFrontOffice\*.json" />
    <Content Remove="Localization\HotelFrontOffice\*.json" />
  </ItemGroup>

</Project>
