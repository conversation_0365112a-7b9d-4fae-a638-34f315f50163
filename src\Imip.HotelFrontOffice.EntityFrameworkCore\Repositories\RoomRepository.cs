﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.EntityFrameworkCore;
using Imip.HotelFrontOffice.Rooms;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.HotelFrontOffice.Repositories
{
    public class RoomRepository : EfCoreRepository<HotelFrontOfficeDbContext, Room, Guid>, IRoomRepository
    {
        public RoomRepository(IDbContextProvider<HotelFrontOfficeDbContext> dbContextProvider)
            : base(dbContextProvider)
        {
        }

        public async Task<Room?> FindByNameAsync(string number)
        {
            var dbContext = await GetDbContextAsync();
            return await dbContext.Rooms
                .Include(x => x.RoomType)
                .Where(x => x.RoomNumber == number)
                .FirstOrDefaultAsync();
        }
    }
}