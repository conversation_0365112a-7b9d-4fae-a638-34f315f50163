#!/bin/bash
# Script to check the status of a database migration job
# Usage: ./check-migration-job.sh [commit-sha] [namespace]

# Default values
COMMIT_SHA=${1:-"latest"}
NAMESPACE=${2:-"imip-wisma-dev-new"}

# If commit SHA is "latest", try to find the most recent job
if [ "$COMMIT_SHA" = "latest" ]; then
  echo "Looking for the most recent migration job in namespace $NAMESPACE..."
  JOB_LIST=$(kubectl get jobs -n $NAMESPACE -l "job-name=imip-wisma-db-migrator" -o name 2>/dev/null)
  
  if [ -z "$JOB_LIST" ]; then
    # Try a more generic approach
    JOB_LIST=$(kubectl get jobs -n $NAMESPACE | grep "imip-wisma-db-migrator" | awk '{print $1}' 2>/dev/null)
    
    if [ -z "$JOB_LIST" ]; then
      echo "No migration jobs found in namespace $NAMESPACE"
      echo "Listing all jobs in namespace:"
      kubectl get jobs -n $NAMESPACE
      exit 1
    fi
  fi
  
  # Get the most recent job
  JOB_NAME=$(echo "$JOB_LIST" | head -n 1)
  echo "Found job: $JOB_NAME"
else
  # Use the provided commit SHA
  JOB_NAME="imip-wisma-db-migrator-$COMMIT_SHA"
  echo "Using job name: $JOB_NAME"
fi

# Check if the job exists
if ! kubectl get "job/$JOB_NAME" -n $NAMESPACE &>/dev/null; then
  echo "Job $JOB_NAME not found in namespace $NAMESPACE"
  echo "Listing all jobs in namespace:"
  kubectl get jobs -n $NAMESPACE
  exit 1
fi

# Get job details
echo "Job details:"
kubectl describe "job/$JOB_NAME" -n $NAMESPACE

# Find the pod for this job
echo "Looking for pod with label job-name=$JOB_NAME"
POD_LIST=$(kubectl get pods -n $NAMESPACE -l "job-name=$JOB_NAME" -o name 2>/dev/null)

if [ -n "$POD_LIST" ]; then
  # Get the first pod if there are multiple
  POD_NAME=$(echo "$POD_LIST" | head -n 1 | sed 's/^pod\///')
  echo "Found pod: $POD_NAME"
  
  echo "Pod details:"
  kubectl describe "pod/$POD_NAME" -n $NAMESPACE
  
  echo "Pod logs:"
  kubectl logs "pod/$POD_NAME" -n $NAMESPACE
else
  echo "Could not find any pods for job: $JOB_NAME"
  echo "Listing all pods in namespace:"
  kubectl get pods -n $NAMESPACE
fi
