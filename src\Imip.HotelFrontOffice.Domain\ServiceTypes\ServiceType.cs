﻿using System;
using System.Collections.Generic;
using Imip.HotelFrontOffice.MasterStatuses;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.HotelFrontOffice.ServiceTypes;

public class ServiceType : FullAuditedAggregateRoot<Guid>
{
    public string Name { get; set; } = default!;
    public Guid? StatusId { get; set; }

    public virtual ICollection<Services.Service> Services { get; set; }
    public virtual MasterStatus? Status { get; set; }

    protected ServiceType()
    {
        Services = new HashSet<Services.Service>();
    }

    public ServiceType(Guid id, string name, Guid? statusId) : this()
    {
        Id = id;
        Name = name;
        StatusId = statusId;
    }
}