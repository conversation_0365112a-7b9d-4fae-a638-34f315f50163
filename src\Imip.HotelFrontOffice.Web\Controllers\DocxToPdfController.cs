using System;
using System.IO;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Documents;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.AspNetCore.Mvc;

namespace Imip.HotelFrontOffice.Web.Controllers;

/// <summary>
/// Controller for DOCX to PDF conversion using Syncfusion
/// </summary>
[Route("api/docx-to-pdf")]
[Authorize]
public class DocxToPdfController : AbpController
{
    private readonly ISyncfusionDocxToPdfService _syncfusionDocxToPdfService;
    private readonly ILogger<DocxToPdfController> _logger;

    public DocxToPdfController(
        ISyncfusionDocxToPdfService syncfusionDocxToPdfService,
        ILogger<DocxToPdfController> logger)
    {
        _syncfusionDocxToPdfService = syncfusionDocxToPdfService;
        _logger = logger;
    }

    /// <summary>
    /// Converts a DOCX file to PDF using Syncfusion
    /// </summary>
    /// <param name="file">The DOCX file to convert</param>
    /// <returns>The PDF file</returns>
    [HttpPost("convert")]
    public async Task<IActionResult> ConvertDocxToPdfAsync(IFormFile file)
    {
        try
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest("No file uploaded");
            }

            // Check if the file is a DOCX file
            var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
            if (extension != ".docx")
            {
                return BadRequest("Only DOCX files are supported");
            }

            // Read the file into memory
            using var memoryStream = new MemoryStream();
            await file.CopyToAsync(memoryStream);
            memoryStream.Position = 0;

            // Convert to PDF
            var filename = Path.GetFileNameWithoutExtension(file.FileName);
            var result = await _syncfusionDocxToPdfService.ConvertDocxToPdfAsync(memoryStream, filename);

            // Return the PDF file
            return File(result.Content, result.ContentType, result.FileName);
        }
        catch (UserFriendlyException ex)
        {
            _logger.LogWarning(ex, "User-friendly error during DOCX to PDF conversion");
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during DOCX to PDF conversion");
            return StatusCode(500, new { error = "An error occurred during conversion" });
        }
    }

    /// <summary>
    /// Converts a DOCX file to PDF using Syncfusion (from base64 string)
    /// </summary>
    /// <param name="input">The conversion input</param>
    /// <returns>The PDF file</returns>
    [HttpPost("convert-base64")]
    public async Task<IActionResult> ConvertDocxToPdfFromBase64Async([FromBody] Base64ConversionInput input)
    {
        try
        {
            if (string.IsNullOrEmpty(input.Base64Content))
            {
                return BadRequest("No file content provided");
            }

            // Decode the base64 string
            byte[] docxBytes;
            try
            {
                docxBytes = Convert.FromBase64String(input.Base64Content);
            }
            catch (FormatException)
            {
                return BadRequest("Invalid base64 string");
            }

            // Convert to PDF
            var result = await _syncfusionDocxToPdfService.ConvertDocxToPdfAsync(docxBytes, input.Filename);

            // Return the PDF file
            return File(result.Content, result.ContentType, result.FileName);
        }
        catch (UserFriendlyException ex)
        {
            _logger.LogWarning(ex, "User-friendly error during DOCX to PDF conversion");
            return BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during DOCX to PDF conversion");
            return StatusCode(500, new { error = "An error occurred during conversion" });
        }
    }

    /// <summary>
    /// Input model for base64 conversion
    /// </summary>
    public class Base64ConversionInput
    {
        /// <summary>
        /// The DOCX file content as a base64 encoded string
        /// </summary>
        public string Base64Content { get; set; } = string.Empty;

        /// <summary>
        /// The filename without extension
        /// </summary>
        public string Filename { get; set; } = "document";
    }
}
