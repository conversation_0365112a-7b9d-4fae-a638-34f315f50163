using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.HotelFrontOffice.Migrations
{
    /// <inheritdoc />
    public partial class Fix_Reservation_Relationships : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // First, make sure we have the correct foreign key for CompanyId
            migrationBuilder.DropForeignKey(
                name: "FK_AppReservations_MasterDiningOptions_CompanyId",
                table: "AppReservations");

            migrationBuilder.DropForeignKey(
                name: "FK_AppReservations_MasterCompany_CompanyId",
                table: "AppReservations");

            migrationBuilder.DropForeignKey(
                name: "FK_AppReservations_MasterDiningOptions_DiningOptionsId",
                table: "AppReservations");

            // Add the correct foreign key for CompanyId to MasterCompany
            migrationBuilder.AddForeignKey(
                name: "FK_AppReservations_MasterCompany_CompanyId",
                table: "AppReservations",
                column: "CompanyId",
                principalTable: "MasterCompany",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            // Add the correct foreign key for DiningOptionsId
            migrationBuilder.AddForeignKey(
                name: "FK_AppReservations_MasterDiningOptions_DiningOptionsId",
                table: "AppReservations",
                column: "DiningOptionsId",
                principalTable: "MasterDiningOptions",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Remove the foreign keys we added
            migrationBuilder.DropForeignKey(
                name: "FK_AppReservations_MasterCompany_CompanyId",
                table: "AppReservations");

            migrationBuilder.AddForeignKey(
                name: "FK_AppReservations_MasterCompany_CompanyId",
                table: "AppReservations",
                column: "CompanyId",
                principalTable: "MasterCompany",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.DropForeignKey(
                name: "FK_AppReservations_MasterDiningOptions_DiningOptionsId",
                table: "AppReservations");

            // Restore the incorrect foreign key
            migrationBuilder.AddForeignKey(
                name: "FK_AppReservations_MasterDiningOptions_CompanyId",
                table: "AppReservations",
                column: "CompanyId",
                principalTable: "MasterDiningOptions",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            // Add the correct foreign key for DiningOptionsId
            migrationBuilder.AddForeignKey(
                name: "FK_AppReservations_MasterDiningOptions_DiningOptionsId",
                table: "AppReservations",
                column: "DiningOptionsId",
                principalTable: "MasterDiningOptions",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
