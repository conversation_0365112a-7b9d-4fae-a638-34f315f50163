﻿using System;
using System.Collections.Generic;
using Imip.HotelFrontOffice.ReservationDetails;
using Imip.HotelFrontOffice.RoomStatuses;
using Imip.HotelFrontOffice.RoomStatusLogs;
using Imip.HotelFrontOffice.RoomTypes;
using Volo.Abp.Data;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.HotelFrontOffice.Rooms;

public class Room : FullAuditedAggregateRoot<Guid>
{
    public decimal? Price { get; set; }
    public string? Size { get; set; }
    public string? RoomNumber { get; set; }
    public string? RoomCode { get; set; }
    public string? Information { get; set; }

    public Guid RoomStatusId { get; set; }
    public Guid RoomTypeId { get; set; }
    public virtual RoomStatus? RoomStatus { get; set; }
    public virtual RoomType? RoomType { get; set; }
    public virtual ICollection<RoomStatusLog>? RoomStatusLogs { get; set; }
    public virtual ICollection<ReservationDetail>? ReservationDetails { get; set; }

    protected Room()
    {
    }

    public Room(
        Guid id,
        decimal price,
        string information,
        Guid roomTypeId,
        string size,
        string roomNumber,
        string roomCode,
        Guid roomStatusId
    )
    {
        Id = id;
        RoomNumber = roomNumber;
        RoomCode = roomCode;
        Price = price;
        Size = size;
        Information = information;
        RoomTypeId = roomTypeId;
        RoomStatusId = roomStatusId;
    }

    // Helper methods for ExtraProperties

    /// <summary>
    /// Sets a value in the ExtraProperties dictionary
    /// </summary>
    public void SetExtraProperty<T>(string key, T value)
    {
        this.SetProperty(key, value);
    }

    /// <summary>
    /// Gets a value from the ExtraProperties dictionary
    /// </summary>
    public T GetExtraProperty<T>(string key)
    {
        return this.GetProperty<T>(key);
    }

    /// <summary>
    /// Removes a property from the ExtraProperties dictionary
    /// </summary>
    public void RemoveExtraProperty(string key)
    {
        this.RemoveProperty(key);
    }
}