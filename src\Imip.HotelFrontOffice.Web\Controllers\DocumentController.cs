using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Documents;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp;
using Volo.Abp.AspNetCore.Mvc;

namespace Imip.HotelFrontOffice.Web.Controllers;

[RemoteService]
[Route("api/document")]
public class DocumentController : AbpController
{
    private readonly IDocumentService _documentService;

    public DocumentController(IDocumentService documentService)
    {
        _documentService = documentService;
    }

    [HttpGet]
    [Route("templates/all")]
    public async Task<ActionResult<List<DocumentTemplateDto>>> GetAllTemplatesAsync()
    {
        var templates = await _documentService.GetAllTemplatesAsync();
        return Ok(templates);
    }

    [HttpGet]
    [Route("templates/by-type/{documentType}")]
    public async Task<ActionResult<List<DocumentTemplateDto>>> GetTemplatesByTypeAsync(DocumentType documentType)
    {
        var templates = await _documentService.GetTemplatesByTypeAsync(documentType);
        return Ok(templates);
    }

    [HttpGet]
    [Route("templates/{id}")]
    public async Task<ActionResult<DocumentTemplateDto>> GetTemplateByIdAsync(Guid id)
    {
        var template = await _documentService.GetTemplateByIdAsync(id);
        return Ok(template);
    }

    [HttpPost]
    [Route("templates")]
    public async Task<ActionResult<DocumentTemplateDto>> CreateTemplateAsync([FromBody] CreateDocumentTemplateDto input)
    {
        var template = await _documentService.CreateTemplateAsync(input);
        return Ok(template);
    }

    [HttpPost]
    [Route("templates/upload")]
    [Consumes("multipart/form-data")]
    public async Task<ActionResult<DocumentTemplateDto>> UploadTemplateAsync([FromForm] UploadDocumentTemplateDto input)
    {
        if (input.File == null || input.File.Length == 0)
        {
            return BadRequest("No file was uploaded");
        }

        var template = await _documentService.UploadTemplateAsync(input);
        return Ok(template);
    }


    [HttpPut]
    [Route("templates/{id}")]
    public async Task<ActionResult<DocumentTemplateDto>> UpdateTemplateAsync(Guid id,
        [FromBody] UpdateDocumentTemplateDto input)
    {
        var template = await _documentService.UpdateTemplateAsync(id, input);
        return Ok(template);
    }

    [HttpDelete]
    [Route("templates/{id}")]
    public async Task<ActionResult> DeleteTemplateAsync(Guid id)
    {
        await _documentService.DeleteTemplateAsync(id);
        return Ok();
    }

    [HttpPost]
    [Route("convert-docx-to-pdf")]
    public async Task<IActionResult> ConvertDocxToPdfAsync([FromBody] DocxToPdfConversionDto input)
    {
        var fileDto = await _documentService.ConvertDocxToPdfAsync(input);
        return File(fileDto.Content, fileDto.ContentType, fileDto.FileName);
    }
}