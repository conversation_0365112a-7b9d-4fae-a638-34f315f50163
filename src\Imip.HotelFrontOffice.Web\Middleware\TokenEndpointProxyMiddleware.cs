using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Users;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Volo.Abp.DependencyInjection;

namespace Imip.HotelFrontOffice.Web.Middleware;

public class TokenEndpointProxyMiddleware : IMiddleware, ITransientDependency
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IConfiguration _configuration;
    private readonly ILogger<TokenEndpointProxyMiddleware> _logger;

    public TokenEndpointProxyMiddleware(
        IHttpClientFactory httpClientFactory,
        IConfiguration configuration,
        ILogger<TokenEndpointProxyMiddleware> logger)
    {
        _httpClientFactory = httpClientFactory;
        _configuration = configuration;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, RequestDelegate next)
    {
        // Check if this is a token request
        if (context.Request.Path.StartsWithSegments("/connect/token", StringComparison.OrdinalIgnoreCase))
        {
            _logger.LogDebug("Proxying token request to Identity Server");

            // Get the Identity Server URL from configuration
            var identityServerUrl = _configuration["AuthServer:Authority"];
            if (string.IsNullOrEmpty(identityServerUrl))
            {
                _logger.LogError("AuthServer:Authority is not configured");
                await SendErrorResponseAsync(context, 500, "Identity Server URL is not configured");
                return;
            }

            try
            {
                // Create a client to forward the request
                var client = _httpClientFactory.CreateClient("IdentityServer");

                // Build the request to the Identity Server
                var tokenEndpoint = $"{identityServerUrl}/connect/token";
                _logger.LogDebug("Forwarding token request to Identity Server");

                // Read the request body
                string requestBody;
                using (var reader = new StreamReader(context.Request.Body))
                {
                    requestBody = await reader.ReadToEndAsync();
                }

                // Create the request content
                var content = new StringContent(requestBody, System.Text.Encoding.UTF8, "application/x-www-form-urlencoded");

                // Copy headers from the original request
                CopyRequestHeaders(context.Request.Headers, client);

                // Send the request to the Identity Server
                var response = await client.PostAsync(tokenEndpoint, content);

                // Copy the response to the client and handle user synchronization
                await CopyResponseToClientAsync(context, response);
                return;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error proxying token request to Identity Server");
                await SendErrorResponseAsync(context, 500, $"Error proxying token request: {ex.Message}");
                return;
            }
        }

        // Not a token request, continue the pipeline
        await next(context);
    }

    private static void CopyRequestHeaders(IHeaderDictionary headers, HttpClient client)
    {
        // Skip headers that might cause issues
        var skipHeaders = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
            {
                "Host",
                "Content-Length"
            };

        foreach (var header in headers)
        {
            if (!skipHeaders.Contains(header.Key))
            {
                client.DefaultRequestHeaders.TryAddWithoutValidation(header.Key, header.Value.ToString());
            }
        }
    }

    private async Task CopyResponseToClientAsync(HttpContext context, HttpResponseMessage response)
    {
        _logger.LogDebug("Identity Server response status: {StatusCode}", response.StatusCode);

        // Copy the response status code
        context.Response.StatusCode = (int)response.StatusCode;

        // Copy the response headers
        foreach (var header in response.Headers)
        {
            context.Response.Headers[header.Key] = header.Value.ToArray();
        }

        // Copy the response content headers
        foreach (var header in response.Content.Headers)
        {
            context.Response.Headers[header.Key] = header.Value.ToArray();
        }

        // Copy the response body
        var responseBody = await response.Content.ReadAsStringAsync();

        // If the token request was successful, synchronize the user
        if (response.IsSuccessStatusCode && !string.IsNullOrEmpty(responseBody))
        {
            await SynchronizeUserFromTokenResponseAsync(context, responseBody);
        }

        await context.Response.WriteAsync(responseBody);
    }

    /// <summary>
    /// Synchronizes user from successful token response
    /// </summary>
    private async Task SynchronizeUserFromTokenResponseAsync(HttpContext context, string responseBody)
    {
        try
        {
            // Parse the token response
            var tokenResponse = JsonSerializer.Deserialize<JsonElement>(responseBody);

            if (tokenResponse.TryGetProperty("access_token", out var accessTokenElement))
            {
                var accessToken = accessTokenElement.GetString();

                if (!string.IsNullOrEmpty(accessToken))
                {
                    _logger.LogDebug("Synchronizing user from successful token response");

                    // Get the user synchronization service
                    using var scope = context.RequestServices.CreateScope();
                    var userSyncService = scope.ServiceProvider.GetRequiredService<IUserSynchronizationService>();

                    // Synchronize user from the access token
                    var synchronizedUser = await userSyncService.SynchronizeUserFromTokenAsync(accessToken);

                    _logger.LogInformation("Successfully synchronized user {UserId} ({UserName}) after token authentication",
                        synchronizedUser.Id, synchronizedUser.UserName);
                }
            }
        }
        catch (Exception ex)
        {
            // Log the error but don't break the token response
            _logger.LogError(ex, "Error synchronizing user from token response");
        }
    }

    private static async Task SendErrorResponseAsync(HttpContext context, int statusCode, string message)
    {
        context.Response.StatusCode = statusCode;
        context.Response.ContentType = "text/plain";
        await context.Response.WriteAsync(message);
    }
}