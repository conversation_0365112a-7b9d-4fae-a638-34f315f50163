﻿using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.MultiTenancy;

namespace Imip.HotelFrontOffice;

public class HotelFrontOfficeTestDataSeedContributor : IDataSeedContributor, ITransientDependency
{
    private readonly ICurrentTenant _currentTenant;
    private readonly ILogger<HotelFrontOfficeTestDataSeedContributor> _logger;

    public HotelFrontOfficeTestDataSeedContributor(
        ICurrentTenant currentTenant,
        ILogger<HotelFrontOfficeTestDataSeedContributor> logger)
    {
        _currentTenant = currentTenant;
        _logger = logger;
    }

    public Task SeedAsync(DataSeedContext context)
    {
        _logger.LogInformation("Seeding test data...");

        /* Seed additional test data... */
        try
        {
            using (_currentTenant.Change(context?.TenantId))
            {
                // Add your test data seeding logic here
                return Task.CompletedTask;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning("Error while seeding test data: {Message}", ex.Message);
            // Don't throw exceptions during test data seeding to prevent test failures
            return Task.CompletedTask;
        }
    }
}
