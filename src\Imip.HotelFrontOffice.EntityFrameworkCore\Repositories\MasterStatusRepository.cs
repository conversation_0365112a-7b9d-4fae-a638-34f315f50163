using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.EntityFrameworkCore;
using Imip.HotelFrontOffice.MasterStatuses;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.HotelFrontOffice.Repositories
{
    public class MasterStatusRepository : EfCoreRepository<HotelFrontOfficeDbContext, MasterStatus, Guid>, IMasterStatusRepository
    {
        public MasterStatusRepository(IDbContextProvider<HotelFrontOfficeDbContext> dbContextProvider)
            : base(dbContextProvider)
        {
        }

        public async Task<List<MasterStatus>> GetByDocTypeAsync(string docType)
        {
            var dbContext = await GetDbContextAsync();
            return await dbContext.MasterStatuses
                .Where(x => x.DocType == docType)
                .ToListAsync();
        }

        public async Task<MasterStatus?> GetByIdAndDocTypeAsync(Guid id, string docType)
        {
            var dbContext = await GetDbContextAsync();
            return await dbContext.MasterStatuses
                .Where(x => x.Id == id && x.DocType == docType)
                .FirstOrDefaultAsync();
        }

        public async Task<List<MasterStatus>> GetByNameAndDocTypeAsync(string name, string docType)
        {
            var dbContext = await GetDbContextAsync();
            return await dbContext.MasterStatuses
                .Where(x => x.Name.Contains(name) && x.DocType == docType)
                .ToListAsync();
        }
    }
}
