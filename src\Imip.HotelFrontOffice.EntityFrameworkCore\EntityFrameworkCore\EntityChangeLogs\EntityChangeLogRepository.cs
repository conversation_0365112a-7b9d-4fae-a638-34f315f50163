using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.EntityChangeLogs;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.HotelFrontOffice.EntityFrameworkCore.EntityChangeLogs
{
    /// <summary>
    /// Implementation of the IEntityChangeLogRepository interface
    /// </summary>
    public class EntityChangeLogRepository : EfCoreRepository<HotelFrontOfficeDbContext, EntityChangeLog, Guid>, IEntityChangeLogRepository
    {
        public EntityChangeLogRepository(IDbContextProvider<HotelFrontOfficeDbContext> dbContextProvider)
            : base(dbContextProvider)
        {
        }

        public async Task<List<EntityChangeLog>> GetEntityChangeLogsAsync(
            string entityId,
            string entityTypeFullName,
            bool includeDetails = true,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();

            query = query
                .Where(x => x.EntityId == entityId && x.EntityTypeFullName == entityTypeFullName)
                .OrderByDescending(x => x.ChangeTime);

            if (includeDetails)
            {
                query = query.Include(x => x.PropertyChanges);
            }

            return await query.ToListAsync(cancellationToken);
        }

        public async Task<EntityChangeLog?> GetLatestEntityChangeLogAsync(
            string entityId,
            string entityTypeFullName,
            bool includeDetails = true,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();

            query = query
                .Where(x => x.EntityId == entityId && x.EntityTypeFullName == entityTypeFullName)
                .OrderByDescending(x => x.ChangeTime);

            if (includeDetails)
            {
                query = query.Include(x => x.PropertyChanges);
            }

            return await query.FirstOrDefaultAsync(cancellationToken);
        }

        public async Task<List<EntityChangeLog>> GetEntityTypeChangeLogsAsync(
            string entityTypeFullName,
            int maxResultCount = 10,
            int skipCount = 0,
            bool includeDetails = true,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();

            query = query
                .Where(x => x.EntityTypeFullName == entityTypeFullName)
                .OrderByDescending(x => x.ChangeTime)
                .Skip(skipCount)
                .Take(maxResultCount);

            if (includeDetails)
            {
                query = query.Include(x => x.PropertyChanges);
            }

            return await query.ToListAsync(cancellationToken);
        }

        public async Task<List<EntityChangeLog>> GetTimeRangeChangeLogsAsync(
            DateTime startTime,
            DateTime endTime,
            int maxResultCount = 10,
            int skipCount = 0,
            bool includeDetails = true,
            CancellationToken cancellationToken = default)
        {
            var query = await GetQueryableAsync();

            query = query
                .Where(x => x.ChangeTime >= startTime && x.ChangeTime <= endTime)
                .OrderByDescending(x => x.ChangeTime)
                .Skip(skipCount)
                .Take(maxResultCount);

            if (includeDetails)
            {
                query = query.Include(x => x.PropertyChanges);
            }

            return await query.ToListAsync(cancellationToken);
        }
    }
}
