﻿#!/bin/bash
set -e

echo "Setting up SkiaSharp libraries..."

# Find all libSkiaSharp.so files and create symbolic links
find /app -name "libSkiaSharp.so" -type f | while read -r file; do
    echo "Found libSkiaSharp.so at $file"
    cp -v "$file" /usr/lib/ || echo "Failed to copy to /usr/lib/"
    cp -v "$file" /usr/lib/x86_64-linux-gnu/ || echo "Failed to copy to /usr/lib/x86_64-linux-gnu/"
    cp -v "$file" /lib/ || echo "Failed to copy to /lib/"
    
    # Create symbolic links
    ln -sf "$file" /usr/lib/libSkiaSharp.so || echo "Failed to create symlink in /usr/lib/"
    ln -sf "$file" /usr/lib/x86_64-linux-gnu/libSkiaSharp.so || echo "Failed to create symlink in /usr/lib/x86_64-linux-gnu/"
    ln -sf "$file" /lib/libSkiaSharp.so || echo "Failed to create symlink in /lib/"
    
    # If lib64 and usr/lib64 exist, copy there too
    if [ -d "/lib64" ]; then
        cp -v "$file" /lib64/ || echo "Failed to copy to /lib64/"
        ln -sf "$file" /lib64/libSkiaSharp.so || echo "Failed to create symlink in /lib64/"
    fi
    
    if [ -d "/usr/lib64" ]; then
        cp -v "$file" /usr/lib64/ || echo "Failed to copy to /usr/lib64/"
        ln -sf "$file" /usr/lib64/libSkiaSharp.so || echo "Failed to create symlink in /usr/lib64/"
    fi
done

# Copy all native libraries to standard locations
if [ -d "/app/runtimes/linux-x64/native" ]; then
    echo "Copying all native libraries from /app/runtimes/linux-x64/native/"
    cp -vf /app/runtimes/linux-x64/native/* /usr/lib/ || echo "Failed to copy to /usr/lib/"
    cp -vf /app/runtimes/linux-x64/native/* /usr/lib/x86_64-linux-gnu/ || echo "Failed to copy to /usr/lib/x86_64-linux-gnu/"
    cp -vf /app/runtimes/linux-x64/native/* /lib/ || echo "Failed to copy to /lib/"
    
    # If lib64 and usr/lib64 exist, copy there too
    if [ -d "/lib64" ]; then
        cp -vf /app/runtimes/linux-x64/native/* /lib64/ || echo "Failed to copy to /lib64/"
    fi
    
    if [ -d "/usr/lib64" ]; then
        cp -vf /app/runtimes/linux-x64/native/* /usr/lib64/ || echo "Failed to copy to /usr/lib64/"
    fi
else
    echo "WARNING: /app/runtimes/linux-x64/native/ directory not found"
    # Try to find the runtimes directory
    find /app -name "runtimes" -type d | while read -r dir; do
        echo "Found runtimes directory at $dir"
        if [ -d "$dir/linux-x64/native" ]; then
            echo "Copying native libraries from $dir/linux-x64/native/"
            cp -vf "$dir/linux-x64/native/"* /usr/lib/ || echo "Failed to copy to /usr/lib/"
            cp -vf "$dir/linux-x64/native/"* /usr/lib/x86_64-linux-gnu/ || echo "Failed to copy to /usr/lib/x86_64-linux-gnu/"
            cp -vf "$dir/linux-x64/native/"* /lib/ || echo "Failed to copy to /lib/"
            
            # If lib64 and usr/lib64 exist, copy there too
            if [ -d "/lib64" ]; then
                cp -vf "$dir/linux-x64/native/"* /lib64/ || echo "Failed to copy to /lib64/"
            fi
            
            if [ -d "/usr/lib64" ]; then
                cp -vf "$dir/linux-x64/native/"* /usr/lib64/ || echo "Failed to copy to /usr/lib64/"
            fi
        fi
    done
fi

# Update the library cache
echo "Updating library cache with ldconfig..."
ldconfig

# List all library paths to verify
echo "Library paths:"
ldconfig -p | grep SkiaSharp || echo "SkiaSharp not found in ldconfig"
echo "LD_LIBRARY_PATH: $LD_LIBRARY_PATH"

echo "SkiaSharp setup complete"
