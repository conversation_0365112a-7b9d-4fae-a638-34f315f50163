﻿using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.HotelFrontOffice.RoomTypes;

public interface IRoomTypeAppService : ICrudAppService<
        RoomTypeDto,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdateRoomTypeDto,
        CreateUpdateRoomTypeDto
    >
{
    new Task<RoomTypeDto> CreateAsync(CreateUpdateRoomTypeDto input);
    new Task<RoomTypeDto> UpdateAsync(Guid id, CreateUpdateRoomTypeDto input);
}