using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Attachments;
using Imip.HotelFrontOffice.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.HotelFrontOffice.Repositories
{
    /// <summary>
    /// Repository implementation for TemporaryZipFile entity
    /// </summary>
    public class TemporaryZipFileRepository : EfCoreRepository<HotelFrontOfficeDbContext, TemporaryZipFile, Guid>, ITemporaryZipFileRepository
    {
        public TemporaryZipFileRepository(IDbContextProvider<HotelFrontOfficeDbContext> dbContextProvider)
            : base(dbContextProvider)
        {
        }

        /// <summary>
        /// Gets temporary ZIP files that are due for deletion
        /// </summary>
        public async Task<List<TemporaryZipFile>> GetDueForDeletionAsync(DateTime now)
        {
            var dbContext = await GetDbContextAsync();
            
            return await dbContext.Set<TemporaryZipFile>()
                .Where(z => !z.IsProcessed && z.DeleteAfter <= now)
                .ToListAsync();
        }
        
        /// <summary>
        /// Marks a temporary ZIP file as processed
        /// </summary>
        public async Task MarkAsProcessedAsync(Guid id)
        {
            var dbContext = await GetDbContextAsync();
            var zipFile = await dbContext.Set<TemporaryZipFile>().FindAsync(id);
            
            if (zipFile != null)
            {
                zipFile.IsProcessed = true;
                await dbContext.SaveChangesAsync();
            }
        }
    }
}
