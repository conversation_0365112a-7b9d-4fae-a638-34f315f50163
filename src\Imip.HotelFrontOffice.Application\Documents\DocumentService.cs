using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using GdPicture14;
using Imip.HotelFrontOffice.Attachments;
using Imip.HotelFrontOffice.Documents;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Application.Services;
using Volo.Abp.BlobStoring;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Localization;
using Volo.Abp.Uow;

namespace Imip.HotelFrontOffice.Documents;

/// <summary>
/// Service for document operations
/// </summary>
[Authorize]
public class DocumentService : ApplicationService, IDocumentService
{
    private readonly IRepository<DocumentTemplate, Guid> _documentTemplateRepository;
    private readonly IDocumentTemplateRepository _documentTemplateCustomRepository;
    private readonly IRepository<Attachment, Guid> _attachmentRepository;
    private readonly IBlobContainer _blobContainer;
    private readonly IAttachmentAppService _attachmentAppService;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly ISyncfusionDocxToPdfService _syncfusionDocxToPdfService;


    public DocumentService(
        IRepository<DocumentTemplate, Guid> documentTemplateRepository,
        IDocumentTemplateRepository documentTemplateCustomRepository,
        IRepository<Attachment, Guid> attachmentRepository,
        IBlobContainer blobContainer,
        IAttachmentAppService attachmentAppService,
        IUnitOfWorkManager unitOfWorkManager,
        ISyncfusionDocxToPdfService syncfusionDocxToPdfService)
    {
        _documentTemplateRepository = documentTemplateRepository;
        _documentTemplateCustomRepository = documentTemplateCustomRepository;
        _attachmentRepository = attachmentRepository;
        _blobContainer = blobContainer;
        _attachmentAppService = attachmentAppService;
        _unitOfWorkManager = unitOfWorkManager;
        _syncfusionDocxToPdfService = syncfusionDocxToPdfService;
    }

    /// <summary>
    /// Gets all document templates
    /// </summary>
    public async Task<List<DocumentTemplateDto>> GetAllTemplatesAsync()
    {
        var templates = await _documentTemplateRepository.GetListAsync();
        return templates.Select(MapToDto).ToList();
    }

    /// <summary>
    /// Gets document templates by document type
    /// </summary>
    public async Task<List<DocumentTemplateDto>> GetTemplatesByTypeAsync(DocumentType documentType)
    {
        var templates = await _documentTemplateCustomRepository.GetByDocumentTypeAsync(documentType);
        return templates.Select(MapToDto).ToList();
    }

    /// <summary>
    /// Gets a document template by ID
    /// </summary>
    public async Task<DocumentTemplateDto> GetTemplateByIdAsync(Guid id)
    {
        var template = await _documentTemplateRepository.GetAsync(id);
        return MapToDto(template);
    }

    /// <summary>
    /// Creates a new document template
    /// </summary>
    public async Task<DocumentTemplateDto> CreateTemplateAsync(CreateDocumentTemplateDto input)
    {
        // Verify that the attachment exists and is a DOCX file
        var attachment = await _attachmentRepository.GetAsync(input.AttachmentId);
        if (attachment == null)
        {
            throw new UserFriendlyException(L["AttachmentNotFound"]);
        }

        if (attachment.ContentType == null || !attachment.ContentType.Equals(
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                StringComparison.OrdinalIgnoreCase))
        {
            throw new UserFriendlyException(L["AttachmentMustBeDocx"]);
        }

        // If this is set as the default template, unset any existing default templates for this document type
        if (input.IsDefault)
        {
            var existingDefaultTemplates =
                await _documentTemplateCustomRepository.GetByDocumentTypeAsync(input.DocumentType);
            foreach (var existingTemplate in existingDefaultTemplates.Where(t => t.IsDefault))
            {
                existingTemplate.IsDefault = false;
                await _documentTemplateRepository.UpdateAsync(existingTemplate);
            }
        }

        // Create the new template
        var template = new DocumentTemplate(
            GuidGenerator.Create(),
            input.Name,
            input.DocumentType,
            input.AttachmentId,
            input.Description,
            input.IsDefault
        );

        await _documentTemplateRepository.InsertAsync(template);

        return MapToDto(template);
    }

    /// <summary>
    /// Uploads a new document template with file
    /// </summary>
    public async Task<DocumentTemplateDto> UploadTemplateAsync(UploadDocumentTemplateDto input)
    {
        // Validate file
        if (input.File == null || input.File.Length == 0)
        {
            throw new UserFriendlyException(L["FileIsRequired"]);
        }

        if (!input.File.ContentType.Equals("application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                StringComparison.OrdinalIgnoreCase))
        {
            throw new UserFriendlyException(L["FileMustBeDocx"]);
        }

        // Use a single unit of work for all operations to ensure transaction consistency
        using (var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: true))
        {
            try
            {
                // First, upload the file to blob storage and create an attachment record
                using var memoryStream = new MemoryStream();
                await input.File.CopyToAsync(memoryStream);
                byte[] fileBytes = memoryStream.ToArray();

                // Create attachment through the attachment app service
                var fileUploadDto = new FileUploadDto
                {
                    Description = input.Description,
                    ReferenceType = "DocumentTemplate"
                };

                var uploadResult = await _attachmentAppService.UploadFileAsync(
                    fileUploadDto,
                    input.File.FileName,
                    input.File.ContentType,
                    fileBytes);

                // If this is set as the default template, unset any existing default templates for this document type
                if (input.IsDefault)
                {
                    var existingDefaultTemplates =
                        await _documentTemplateCustomRepository.GetByDocumentTypeAsync(input.DocumentType);
                    foreach (var existingTemplate in existingDefaultTemplates.Where(t => t.IsDefault))
                    {
                        existingTemplate.IsDefault = false;
                        await _documentTemplateRepository.UpdateAsync(existingTemplate);
                    }
                }

                // Create the new template with the uploaded attachment
                var template = new DocumentTemplate(
                    GuidGenerator.Create(),
                    input.Name,
                    input.DocumentType,
                    uploadResult.Id,
                    input.Description,
                    input.IsDefault
                );

                await _documentTemplateRepository.InsertAsync(template);

                // Update the attachment's reference ID now that we have the template ID
                // Use direct repository access to avoid entity tracking issues
                var attachment = await _attachmentRepository.FindAsync(uploadResult.Id);
                if (attachment != null)
                {
                    attachment.ReferenceId = template.Id;
                    await _attachmentRepository.UpdateAsync(attachment);
                }

                await uow.CompleteAsync();
                return MapToDto(template);
            }
            catch (UserFriendlyException)
            {
                throw;
            }
            catch (Exception ex)
            {
                await uow.RollbackAsync();
                Logger.LogError(ex, "Error uploading document template: {Message}", ex.Message);
                throw new UserFriendlyException(L["FailedToUploadTemplate"], ex.Message);
            }
        }
    }


    /// <summary>
    /// Updates a document template
    /// </summary>
    public async Task<DocumentTemplateDto> UpdateTemplateAsync(Guid id, UpdateDocumentTemplateDto input)
    {
        var template = await _documentTemplateRepository.GetAsync(id);

        // If this is set as the default template, unset any existing default templates for this document type
        if (input.IsDefault && !template.IsDefault)
        {
            var existingDefaultTemplates =
                await _documentTemplateCustomRepository.GetByDocumentTypeAsync(input.DocumentType);
            foreach (var existingTemplate in existingDefaultTemplates.Where(t => t.IsDefault))
            {
                existingTemplate.IsDefault = false;
                await _documentTemplateRepository.UpdateAsync(existingTemplate);
            }
        }

        // Update the template
        template.Name = input.Name;
        template.DocumentType = input.DocumentType;
        template.Description = input.Description;
        template.IsDefault = input.IsDefault;

        await _documentTemplateRepository.UpdateAsync(template);

        return MapToDto(template);
    }

    /// <summary>
    /// Deletes a document template
    /// </summary>
    public async Task DeleteTemplateAsync(Guid id)
    {
        await _documentTemplateRepository.DeleteAsync(id);
    }

    /// <summary>
    /// Converts a DOCX template to PDF using the provided data model
    /// </summary>
    public async Task<FileDto> ConvertDocxToPdfAsync(DocxToPdfConversionDto input)
    {
        // Get the template
        DocumentTemplate? template;
        if (input.TemplateId.HasValue)
        {
            template = await _documentTemplateRepository.GetAsync(input.TemplateId.Value);
        }
        else
        {
            template = await _documentTemplateCustomRepository.GetDefaultTemplateAsync(input.DocumentType);
            if (template == null)
            {
                throw new UserFriendlyException(L["NoDefaultTemplateFound", input.DocumentType]);
            }
        }

        // Get the attachment (DOCX template)
        var attachment = await _attachmentRepository.GetAsync(template.AttachmentId);
        if (attachment == null)
        {
            throw new UserFriendlyException(L["TemplateAttachmentNotFound"]);
        }

        // Get the DOCX file from blob storage
        if (attachment.BlobName == null)
        {
            throw new UserFriendlyException(L["TemplateBlobNameNotFound"]);
        }

        var docxStream = await _blobContainer.GetAsync(attachment.BlobName);
        if (docxStream == null)
        {
            throw new UserFriendlyException(L["TemplateFileNotFound"]);
        }

        // Read the DOCX file into memory
        using var memoryStream = new MemoryStream();
        await docxStream.CopyToAsync(memoryStream);
        var docxBytes = memoryStream.ToArray();

        // Create a temporary file for the DOCX template
        var tempDocxPath = Path.Combine(Path.GetTempPath(), $"{Guid.NewGuid()}.docx");
        await File.WriteAllBytesAsync(tempDocxPath, docxBytes);

        try
        {
            // Create the JSON model
            var jsonModel = new
            {
                config = input.Config,
                model = input.Model
            };
            var jsonString = JsonSerializer.Serialize(jsonModel);

            // Initialize GdPicture
            // Note: You'll need to replace this with your actual license key from Nutrient.io
            LicenseManager licenseManager = new LicenseManager();
            licenseManager.RegisterKEY("");

            // Process the template
            using var templater = new GdPictureOfficeTemplater();
            var status = templater.SetTemplate(jsonString);
            if (status != GdPictureStatus.OK)
            {
                throw new UserFriendlyException($"Failed to set template: {status}");
            }

            status = templater.LoadFromFile(tempDocxPath);
            if (status != GdPictureStatus.OK)
            {
                throw new UserFriendlyException($"Failed to load template file: {status}");
            }

            status = templater.Process();
            if (status != GdPictureStatus.OK)
            {
                throw new UserFriendlyException($"Failed to process template: {status}");
            }

            // Save the processed DOCX to a temporary file
            var processedDocxPath = Path.Combine(Path.GetTempPath(), $"{Guid.NewGuid()}.docx");
            status = templater.SaveToFile(processedDocxPath);
            if (status != GdPictureStatus.OK)
            {
                throw new UserFriendlyException($"Failed to save processed template: {status}");
            }

            // Read the processed DOCX file
            var processedDocxBytes = await File.ReadAllBytesAsync(processedDocxPath);

            // Generate a filename for the PDF
            var filename = $"{template.Name}_{DateTime.Now:yyyyMMdd_HHmmss}";

            // Convert the processed DOCX to PDF using Syncfusion
            var result = await _syncfusionDocxToPdfService.ConvertDocxToPdfAsync(processedDocxBytes, filename);

            // Clean up temporary files
            try
            {
                File.Delete(tempDocxPath);
                File.Delete(processedDocxPath);
            }
            catch (Exception ex)
            {
                Logger.LogWarning(ex, "Failed to delete temporary files");
            }

            // Return the PDF file
            return result;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error converting DOCX to PDF");
            throw new UserFriendlyException(L["ErrorConvertingDocxToPdf"], ex.Message);
        }
    }

    private DocumentTemplateDto MapToDto(DocumentTemplate template)
    {
        return new DocumentTemplateDto
        {
            Id = template.Id,
            Name = template.Name,
            DocumentType = template.DocumentType,
            DocumentTypeName = L[$"DocumentType:{template.DocumentType}"],
            AttachmentId = template.AttachmentId,
            Description = template.Description,
            IsDefault = template.IsDefault,
            CreationTime = template.CreationTime
        };
    }
}