using System;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Documents;
using Imip.HotelFrontOffice.Documents.Invoice;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp;
using Volo.Abp.AspNetCore.Mvc;

namespace Imip.HotelFrontOffice.Web.Controllers;

/// <summary>
/// Controller for invoice document operations
/// </summary>
[RemoteService]
[Route("api/invoice")]
[Authorize]
public class InvoiceDocumentController : AbpController
{
    private readonly IInvoiceDocumentService _invoiceDocumentService;

    /// <summary>
    /// Constructor
    /// </summary>
    public InvoiceDocumentController(IInvoiceDocumentService invoiceDocumentService)
    {
        _invoiceDocumentService = invoiceDocumentService;
    }

    /// <summary>
    /// Generates an invoice document for a payment
    /// </summary>
    [HttpPost]
    [Route("generate")]
    public async Task<IActionResult> GenerateInvoiceAsync([FromBody] InvoiceGenerationDto input)
    {
        try
        {
            var result = await _invoiceDocumentService.GenerateInvoiceAsync(input);
            return File(result.Content, result.ContentType, result.FileName);
        }
        catch (UserFriendlyException ex)
        {
            return BadRequest(new { error = ex.Message, details = ex.Details });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "An error occurred while generating the invoice", details = ex.Message });
        }
    }

    /// <summary>
    /// Generates a Wisma invoice document for a payment
    /// </summary>
    [HttpPost]
    [Route("generate-wisma")]
    public async Task<IActionResult> GenerateWismaInvoiceAsync([FromBody] InvoiceGenerationDto input)
    {
        try
        {
            // Generate the invoice and save it as an attachment to get a stream URL
            var result = await _invoiceDocumentService.GenerateWismaInvoiceAsAttachmentAsync(input);

            // Return the stream URL instead of the file content
            return Ok(new
            {
                success = true,
                streamUrl = result.StreamUrl,
                fileName = result.FileName,
                fileId = result.Id
            });
        }
        catch (UserFriendlyException ex)
        {
            return BadRequest(new { error = ex.Message, details = ex.Details });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "An error occurred while generating the invoice", details = ex.Message });
        }
    }

    /// <summary>
    /// Generates a Wisma invoice document for a payment (direct download)
    /// </summary>
    [HttpPost]
    [Route("generate-wisma-download")]
    public async Task<IActionResult> GenerateWismaInvoiceDownloadAsync([FromBody] InvoiceGenerationDto input)
    {
        try
        {
            var result = await _invoiceDocumentService.GenerateWismaInvoiceAsync(input);
            return File(result.Content, result.ContentType, result.FileName);
        }
        catch (UserFriendlyException ex)
        {
            return BadRequest(new { error = ex.Message, details = ex.Details });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "An error occurred while generating the invoice", details = ex.Message });
        }
    }

    /// <summary>
    /// Gets the invoice template data for a payment
    /// </summary>
    [HttpGet]
    [Route("template-data/{paymentId}")]
    public async Task<ActionResult<InvoiceTemplateDataDto>> GetInvoiceTemplateDataAsync(Guid paymentId)
    {
        try
        {
            var result = await _invoiceDocumentService.GetInvoiceTemplateDataAsync(paymentId);
            return Ok(result);
        }
        catch (UserFriendlyException ex)
        {
            return BadRequest(new { error = ex.Message, details = ex.Details });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { error = "An error occurred while getting the invoice template data", details = ex.Message });
        }
    }
}
