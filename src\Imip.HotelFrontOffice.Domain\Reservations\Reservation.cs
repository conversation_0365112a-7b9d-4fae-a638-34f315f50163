﻿using System;
using System.Collections.Generic;
using Imip.HotelFrontOffice.Companies;
using Imip.HotelFrontOffice.DiningOptions;
using Imip.HotelFrontOffice.MasterStatuses;
using Imip.HotelFrontOffice.PaymentMethods;
using Imip.HotelFrontOffice.ReservationDetails;
using Imip.HotelFrontOffice.ReservationTypes;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.HotelFrontOffice.Reservations;

public class Reservation : FullAuditedAggregateRoot<Guid>
{
    public required string ReservationCode { get; set; }
    public string? GroupCode { get; set; }
    public string? BookerName { get; set; }
    public string? BookerIdentityNumber { get; set; }
    public string? BookerPhoneNumber { get; set; }
    public string? BookerEmail { get; set; }
    public DateTime ArrivalDate { get; set; }
    public int Days { get; set; }
    public string? Attachment { get; set; }

    public Guid? CompanyId { get; set; }
    public virtual Company? Company { get; set; }

    public Guid? StatusId { get; set; }
    public virtual MasterStatus? Status { get; set; }

    public Guid? PaymentMethodId { get; set; }
    public virtual PaymentMethod? PaymentMethod { get; set; }

    public Guid? DiningOptionsId { get; set; }
    public virtual DiningOption? DiningOptions { get; set; }

    public Guid ReservationTypeId { get; set; }
    public required ReservationType ReservationType { get; set; }

    public virtual ICollection<ReservationDetail>? ReservationDetails { get; set; }

    //public virtual ICollection<Services> Services { get; set; }

    //protected ServiceTypes() 
    //{
    //Services = new HashSet<Services>();
    //}

    protected Reservation()
    {
        ReservationDetails = new HashSet<ReservationDetail>();
    }

    public Reservation(
        Guid id,
        string reservationCode,
        string groupCode,
        string bookerName,
        string bookerIdentityNumber,
        string bookerPhoneNumber,
        string? bookerEmail,
        Guid companyId,
        DateTime arrivalDate,
        Guid? paymentMethodId,
        int days,
        Guid? diningOptionId,
        string attachment,
        Guid? statusId,
        Guid reservationTypeId
    )
    {
        Id = id;
        ReservationCode = reservationCode;
        GroupCode = groupCode;
        BookerName = bookerName;
        BookerIdentityNumber = bookerIdentityNumber;
        BookerPhoneNumber = bookerPhoneNumber;
        BookerEmail = bookerEmail;
        CompanyId = companyId;
        ArrivalDate = arrivalDate;
        PaymentMethodId = paymentMethodId;
        Days = days;
        DiningOptionsId = diningOptionId;
        Attachment = attachment;
        StatusId = statusId;
        ReservationTypeId = reservationTypeId;
    }
}