#!/bin/bash
# Script to check if an image can be pulled from the GitLab registry
# Usage: ./check-image-pull.sh [image-tag]

# Default values
IMAGE_TAG=${1:-"latest"}
REGISTRY_USER=${CI_REGISTRY_USER:-"gitlab-ci-token"}
REGISTRY_TOKEN=${GITLAB_REGISTRY_TOKEN:-"your-token-here"}
REGISTRY_IMAGE=${CI_REGISTRY_IMAGE:-"registry.gitlab.com/itimipdev/imip.hotelfrontoffice"}

# Login to Docker registry
echo "Logging in to GitLab registry..."
echo "$REGISTRY_TOKEN" | docker login -u $REGISTRY_USER --password-stdin registry.gitlab.com

# Try to pull the image
echo "Trying to pull image: $REGISTRY_IMAGE/db-migrator:$IMAGE_TAG"
if docker pull "$REGISTRY_IMAGE/db-migrator:$IMAGE_TAG"; then
  echo "Image pull successful!"
  
  # Show image details
  echo "Image details:"
  docker inspect "$REGISTRY_IMAGE/db-migrator:$IMAGE_TAG"
else
  echo "Failed to pull image: $REGISTRY_IMAGE/db-migrator:$IMAGE_TAG"
  
  # Check if the image exists in the registry
  echo "Checking available tags for $REGISTRY_IMAGE/db-migrator..."
  curl -s --header "PRIVATE-TOKEN: $REGISTRY_TOKEN" \
    "https://gitlab.com/api/v4/projects/$(echo $REGISTRY_IMAGE | sed 's/registry.gitlab.com\///g' | sed 's/\//%2F/g')/registry/repositories" | \
    grep -o '"path":"[^"]*"' | sed 's/"path":"//g' | sed 's/"//g'
  
  echo "Make sure the image exists and you have the correct permissions."
fi

# Clean up
echo "Logging out from GitLab registry..."
docker logout registry.gitlab.com
