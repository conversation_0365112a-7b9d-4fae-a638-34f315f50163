﻿using System;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.HotelFrontOffice.Repositories
{
    public class ReservationFoodAndBeveragesRepository : EfCoreRepository<HotelFrontOfficeDbContext, ReservationFoodAndBeverages.ReservationFoodAndBeverage, Guid>, IReservationFoodAndBeveragesRepository
    {
        public ReservationFoodAndBeveragesRepository(IDbContextProvider<HotelFrontOfficeDbContext> dbContextProvider)
            : base(dbContextProvider)
        {
        }

        public async Task<ReservationFoodAndBeverages.ReservationFoodAndBeverage?> FindByNameAsync(string name)
        {
            var dbContext = await GetDbContextAsync();
            return await dbContext.ReservationFoodAndBeverages
                .Include(x => x.ReservationDetails)

                .FirstOrDefaultAsync(); 
        }
    }
}