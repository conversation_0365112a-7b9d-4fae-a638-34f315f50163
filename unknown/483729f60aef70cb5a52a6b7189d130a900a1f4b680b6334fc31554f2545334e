﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.ReservationRooms;
using Imip.HotelFrontOffice.Services;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.HotelFrontOffice.ReservationFoodAndBeverages
{
    public interface ICreateUpdateReservationFoodAndBeverages : ICrudAppService<
        ReservationFoodAndBeveragesDto,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdateReservationFoodAndBeveragesDto,
        CreateUpdateReservationFoodAndBeveragesDto
    >
    {
    }
}