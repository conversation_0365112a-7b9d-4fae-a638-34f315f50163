using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using System;
using Imip.HotelFrontOffice.MultiTenancy;
using Imip.HotelFrontOffice.BlobStorage;
using Imip.HotelFrontOffice.Attachments.BackgroundWorkers;
using Imip.HotelFrontOffice.Attachments.Jobs;
using TickerQ.DependencyInjection;

using Volo.Abp.Localization;
using Volo.Abp.Modularity;
using Volo.Abp.MultiTenancy;
using Volo.Abp.SettingManagement;
using Volo.Abp.BlobStoring;
using Volo.Abp.BlobStoring.Database;
using Volo.Abp.Caching;
using Volo.Abp.OpenIddict;
using Volo.Abp.AuditLogging;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.Emailing;
using Volo.Abp.FeatureManagement;
using Volo.Abp.Identity;
using Volo.Abp.TenantManagement;
using Volo.Abp.Auditing;

namespace Imip.HotelFrontOffice;

[DependsOn(
    typeof(HotelFrontOfficeDomainSharedModule),
    typeof(AbpAuditLoggingDomainModule),
    typeof(AbpCachingModule),
    typeof(AbpBackgroundJobsDomainModule),
    typeof(AbpFeatureManagementDomainModule),
    // typeof(AbpPermissionManagementDomainIdentityModule),
    // typeof(AbpPermissionManagementDomainOpenIddictModule),
    typeof(AbpSettingManagementDomainModule),
    typeof(AbpEmailingModule),
    typeof(AbpIdentityDomainModule),
    typeof(AbpOpenIddictDomainModule),
    typeof(AbpTenantManagementDomainModule),
    typeof(BlobStoringDatabaseDomainModule),
    typeof(SftpBlobProviderModule)
    )]
public class HotelFrontOfficeDomainModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        Configure<AbpMultiTenancyOptions>(options =>
        {
            options.IsEnabled = MultiTenancyConsts.IsEnabled;
        });

        // Configure SFTP blob storage provider
        Configure<AbpBlobStoringOptions>(options =>
        {
            // Configure the default container to use SFTP provider
            options.Containers.ConfigureDefault(container =>
            {
                container.ProviderType = typeof(SftpBlobProvider);
            });
        });

        Configure<AbpLocalizationOptions>(options =>
        {
            options.Languages.Add(new LanguageInfo("ar", "ar", "العربية"));
            options.Languages.Add(new LanguageInfo("cs", "cs", "Čeština"));
            options.Languages.Add(new LanguageInfo("en", "en", "English"));
            options.Languages.Add(new LanguageInfo("en-GB", "en-GB", "English (UK)"));
            options.Languages.Add(new LanguageInfo("hu", "hu", "Magyar"));
            options.Languages.Add(new LanguageInfo("fi", "fi", "Finnish"));
            options.Languages.Add(new LanguageInfo("fr", "fr", "Français"));
            options.Languages.Add(new LanguageInfo("hi", "hi", "Hindi"));
            options.Languages.Add(new LanguageInfo("it", "it", "Italiano"));
            options.Languages.Add(new LanguageInfo("pt-BR", "pt-BR", "Português"));
            options.Languages.Add(new LanguageInfo("ru", "ru", "Русский"));
            options.Languages.Add(new LanguageInfo("sk", "sk", "Slovak"));
            options.Languages.Add(new LanguageInfo("tr", "tr", "Türkçe"));
            options.Languages.Add(new LanguageInfo("zh-Hans", "zh-Hans", "简体中文"));
            options.Languages.Add(new LanguageInfo("zh-Hant", "zh-Hant", "繁體中文"));
            options.Languages.Add(new LanguageInfo("de-DE", "de-DE", "Deutsch"));
            options.Languages.Add(new LanguageInfo("es", "es", "Español"));
            options.Languages.Add(new LanguageInfo("sv", "sv", "Svenska"));
        });

        // Configure TickerQ - basic configuration
        context.Services.AddTickerQ(options =>
        {
            // Set max concurrency for job processing
            options.SetMaxConcurrency(Environment.ProcessorCount);

            // Register the TickerQ controllers
            context.Services.AddTransient<DeleteTemporaryZipFileTickerController>();
            context.Services.AddTransient<TemporaryZipFileCleanupTickerController>();
        });

        // Register TickerQ services
        context.Services.AddTransient<ITemporaryZipFileTickerService, TemporaryZipFileTickerService>();

        // Register background workers
        context.Services.AddTransient<TemporaryZipFileCleanupWorker>();
        // context.Services.AddTransient<EntityChangeTrackingWorker>();

        // Register entity change loggers
        // context.Services.AddTransient<DirectEntityChangeLogger>();

        // Configure audit logging to track all entity changes
        Configure<AbpAuditingOptions>(options =>
        {
            options.IsEnabled = true;
            // options.IsEnabledForIntegrationServices = true;
            // options.IsEnabledForGetRequests = true;
            // options.IsEnabledForAnonymousUsers = true;
            options.EntityHistorySelectors.AddAllEntities();
        });

#if DEBUG
        context.Services.Replace(ServiceDescriptor.Singleton<IEmailSender, NullEmailSender>());
#endif
    }
}
