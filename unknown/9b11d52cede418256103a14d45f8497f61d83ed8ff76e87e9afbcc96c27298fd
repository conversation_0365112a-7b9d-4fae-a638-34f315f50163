using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Imip.HotelFrontOffice.Attachments;
using Microsoft.AspNetCore.Http;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Imip.HotelFrontOffice.Web.Swagger
{
    /// <summary>
    /// Operation filter to handle file uploads in Swagger
    /// </summary>
    public class SwaggerFileOperationFilter : IOperationFilter
    {
        /// <summary>
        /// Applies the file upload operation filter
        /// </summary>
        public void Apply(OpenApiOperation operation, OperationFilterContext context)
        {
            // Check if any parameter is FileUploadFormDto
            var fileUploadFormDtoParams = context.MethodInfo.GetParameters()
                .Where(p => p.ParameterType == typeof(FileUploadFormDto))
                .ToList();

            // Check if any parameter is IFormFile
            var fileParameters = context.MethodInfo.GetParameters()
                .Where(p => p.ParameterType == typeof(IFormFile) ||
                           (p.ParameterType.IsGenericType && p.ParameterType.GetGenericArguments().Any(a => a == typeof(IFormFile))))
                .ToList();

            if (fileParameters.Count == 0 && fileUploadFormDtoParams.Count == 0)
                return;

            // Set the content type to multipart/form-data
            operation.RequestBody = new OpenApiRequestBody
            {
                Content = new Dictionary<string, OpenApiMediaType>
                {
                    ["multipart/form-data"] = new OpenApiMediaType
                    {
                        Schema = new OpenApiSchema
                        {
                            Type = "object",
                            Properties = new Dictionary<string, OpenApiSchema>(),
                            Required = new HashSet<string>()
                        }
                    }
                }
            };

            var schema = operation.RequestBody.Content["multipart/form-data"].Schema;

            // Handle FileUploadFormDto parameters
            if (fileUploadFormDtoParams.Count > 0)
            {
                // Add file property
                schema.Properties.Add("File", new OpenApiSchema
                {
                    Type = "string",
                    Format = "binary",
                    Description = "The file to upload"
                });
                schema.Required.Add("File");

                // Add other properties from FileUploadFormDto
                schema.Properties.Add("Description", new OpenApiSchema
                {
                    Type = "string",
                    Description = "Optional description of the file"
                });

                schema.Properties.Add("ReferenceId", new OpenApiSchema
                {
                    Type = "string",
                    Format = "uuid",
                    Description = "Optional reference ID that this file is associated with"
                });

                schema.Properties.Add("ReferenceType", new OpenApiSchema
                {
                    Type = "string",
                    Description = "Optional reference type that this file is associated with"
                });

                return; // We've handled the FileUploadFormDto, no need to process further
            }

            // Handle IFormFile parameters
            foreach (var fileParameter in fileParameters)
            {
                schema.Properties.Add(fileParameter.Name, new OpenApiSchema
                {
                    Type = "string",
                    Format = "binary"
                });

                if (fileParameter.ParameterType.IsRequired())
                {
                    schema.Required.Add(fileParameter.Name);
                }
            }

            // Add other form parameters
            var formParameters = context.MethodInfo.GetParameters()
                .Where(p => p.GetCustomAttributes(true)
                    .Any(a => a.GetType().Name == "FromFormAttribute" && p.ParameterType != typeof(IFormFile)))
                .ToList();

            foreach (var formParameter in formParameters)
            {
                var parameterType = formParameter.ParameterType;
                var isNullable = Nullable.GetUnderlyingType(parameterType) != null ||
                                 parameterType.IsClass ||
                                 parameterType.Name.EndsWith("?");

                var propertyType = "string";
                var propertyFormat = (string)null;

                if (parameterType == typeof(Guid) || parameterType == typeof(Guid?))
                {
                    propertyType = "string";
                    propertyFormat = "uuid";
                }
                else if (parameterType == typeof(int) || parameterType == typeof(int?) ||
                         parameterType == typeof(long) || parameterType == typeof(long?))
                {
                    propertyType = "integer";
                }
                else if (parameterType == typeof(double) || parameterType == typeof(double?) ||
                         parameterType == typeof(decimal) || parameterType == typeof(decimal?))
                {
                    propertyType = "number";
                }
                else if (parameterType == typeof(bool) || parameterType == typeof(bool?))
                {
                    propertyType = "boolean";
                }
                else if (parameterType == typeof(DateTime) || parameterType == typeof(DateTime?))
                {
                    propertyType = "string";
                    propertyFormat = "date-time";
                }

                schema.Properties.Add(formParameter.Name, new OpenApiSchema
                {
                    Type = propertyType,
                    Format = propertyFormat,
                    Nullable = isNullable
                });

                if (!isNullable)
                {
                    schema.Required.Add(formParameter.Name);
                }
            }
        }
    }

    /// <summary>
    /// Extension methods for parameter type checking
    /// </summary>
    public static class ParameterTypeExtensions
    {
        /// <summary>
        /// Checks if a parameter type is required
        /// </summary>
        public static bool IsRequired(this Type type)
        {
            return type.IsValueType && Nullable.GetUnderlyingType(type) == null;
        }
    }
}
