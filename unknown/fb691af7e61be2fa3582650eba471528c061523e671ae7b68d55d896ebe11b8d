using System.Collections.Generic;
using System.Linq;

namespace Imip.HotelFrontOffice.Documents.Invoice;

/// <summary>
/// Utility class for generating invoice tables
/// </summary>
public static class InvoiceTableGenerator
{
    /// <summary>
    /// Creates a simple invoice table
    /// </summary>
    public static TableGenerationDto CreateSimpleInvoiceTable(List<InvoicePaymentDetailDto> paymentDetails)
    {
        // Create table data
        var tableData = new TableGenerationDto
        {
            Headers = 
            [
                new() { Text = "No.", WidthPercentage = 10 },
                new() { Text = "Description", WidthPercentage = 60 },
                new() { Text = "Amount", WidthPercentage = 30 }
            ],
            Rows = []
        };

        // Add data rows
        for (int i = 0; i < paymentDetails.Count; i++)
        {
            var detail = paymentDetails[i];
            tableData.Rows.Add(new TableRowDto
            {
                Cells = 
                [
                    new() { Text = (i + 1).ToString() },
                    new() { Text = detail.Description },
                    new() { Text = detail.Amount.ToString("N2"), HorizontalAlignment = TableCellAlignment.Right }
                ]
            });
        }

        // Add total row
        tableData.Rows.Add(new TableRowDto
        {
            Cells = 
            [
                new() { Text = "Total", IsBold = true, HorizontalAlignment = TableCellAlignment.Right, ColSpan = 2 },
                new() { Text = paymentDetails.Sum(d => d.Amount).ToString("N2"), IsBold = true, HorizontalAlignment = TableCellAlignment.Right }
            ]
        });

        return tableData;
    }

    /// <summary>
    /// Creates an advanced invoice table with merged cells
    /// </summary>
    public static TableGenerationDto CreateAdvancedInvoiceTable(List<InvoicePaymentDetailDto> paymentDetails)
    {
        // Create table data
        var tableData = new TableGenerationDto
        {
            Headers = 
            [
                new() { Text = "No.", WidthPercentage = 10 },
                new() { Text = "Description", WidthPercentage = 50 },
                new() { Text = "Quantity", WidthPercentage = 10 },
                new() { Text = "Amount", WidthPercentage = 30 }
            ],
            Rows = []
        };

        // Add regular data rows
        for (int i = 0; i < paymentDetails.Count; i++)
        {
            var detail = paymentDetails[i];
            tableData.Rows.Add(new TableRowDto
            {
                Cells = 
                [
                    new() { Text = (i + 1).ToString() },
                    new() { Text = detail.Description },
                    new() { Text = "1", HorizontalAlignment = TableCellAlignment.Center },
                    new() { Text = detail.Amount.ToString("N2"), HorizontalAlignment = TableCellAlignment.Right }
                ]
            });
        }

        // Add a separator row
        tableData.Rows.Add(new TableRowDto
        {
            Cells = 
            [
                new() { Text = "", ColSpan = 4 }
            ]
        });

        // Add a section header with colspan
        tableData.Rows.Add(new TableRowDto
        {
            Cells = 
            [
                new() { Text = "Additional Information", IsBold = true, ColSpan = 4, HorizontalAlignment = TableCellAlignment.Center }
            ]
        });

        // Add a row with rowspan
        tableData.Rows.Add(new TableRowDto
        {
            Cells = 
            [
                new() { Text = "Notes:", IsBold = true, RowSpan = 3, VerticalAlignment = TableCellAlignment.Top },
                new() { Text = "1. Payment is due within 30 days", ColSpan = 3 }
            ]
        });

        // Add a row that continues the rowspan
        tableData.Rows.Add(new TableRowDto
        {
            Cells = 
            [
                // This cell is merged with the cell above (no need to specify it)
                new() { Text = "2. Please include invoice number with payment", ColSpan = 3 }
            ]
        });

        // Add another row that continues the rowspan
        tableData.Rows.Add(new TableRowDto
        {
            Cells = 
            [
                // This cell is merged with the cell above (no need to specify it)
                new() { Text = "3. Thank you for your business", ColSpan = 3 }
            ]
        });

        // Add a separator row
        tableData.Rows.Add(new TableRowDto
        {
            Cells = 
            [
                new() { Text = "", ColSpan = 4 }
            ]
        });

        // Add subtotal row with colspan
        tableData.Rows.Add(new TableRowDto
        {
            Cells = 
            [
                new() { Text = "Subtotal", IsBold = true, HorizontalAlignment = TableCellAlignment.Right, ColSpan = 3 },
                new() { Text = paymentDetails.Sum(d => d.Amount).ToString("N2"), IsBold = true, HorizontalAlignment = TableCellAlignment.Right }
            ]
        });

        // Add tax row with colspan
        tableData.Rows.Add(new TableRowDto
        {
            Cells = 
            [
                new() { Text = "Tax (10%)", IsBold = true, HorizontalAlignment = TableCellAlignment.Right, ColSpan = 3 },
                new() { 
                    Text = (paymentDetails.Sum(d => d.Amount) * 0.1m).ToString("N2"), 
                    IsBold = true, 
                    HorizontalAlignment = TableCellAlignment.Right 
                }
            ]
        });

        // Add total row with colspan
        tableData.Rows.Add(new TableRowDto
        {
            Cells = 
            [
                new() { Text = "Total", IsBold = true, HorizontalAlignment = TableCellAlignment.Right, ColSpan = 3 },
                new() { 
                    Text = (paymentDetails.Sum(d => d.Amount) * 1.1m).ToString("N2"), 
                    IsBold = true, 
                    HorizontalAlignment = TableCellAlignment.Right 
                }
            ]
        });

        return tableData;
    }
}
