using System;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using System.Xml.Linq;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Imip.HotelFrontOffice.Controllers;

[Route("api/exchange-rates")]
public class BankIndonesiaController : HotelFrontOfficeController
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ILogger<BankIndonesiaController> _logger;

    public BankIndonesiaController(IHttpClientFactory httpClientFactory, ILogger<BankIndonesiaController> logger)
    {
        _httpClientFactory = httpClientFactory;
        _logger = logger;
    }

    [HttpGet]
    public async Task<IActionResult> GetExchangeRates(
        [FromQuery] string currency = "USD",
        [FromQuery] string startDate = "",
        [FromQuery] string endDate = "")
    {
        try
        {
            // Set default dates if not provided
            if (string.IsNullOrEmpty(startDate))
                startDate = DateTime.Now.AddDays(-7).ToString("yyyyMMdd");

            if (string.IsNullOrEmpty(endDate))
                endDate = DateTime.Now.ToString("yyyyMMdd");

            // Create HTTP client
            var client = _httpClientFactory.CreateClient();

            // Construct the URL
            string url = $"https://www.bi.go.id/biwebservice/wskursbi.asmx/getSubKursLokal3?mts={currency}&startdate={startDate}&enddate={endDate}";

            // Make the request
            var response = await client.GetStringAsync(url);

            // Load the XML response
            XDocument xdoc = XDocument.Parse(response);

            // Define namespaces
            XNamespace diffgrNs = "urn:schemas-microsoft-com:xml-diffgram-v1";

            // Get the data from the diffgram
            var diffgram = xdoc.Descendants(diffgrNs + "diffgram").FirstOrDefault();

            if (diffgram == null)
            {
                return BadRequest("Invalid response format from Bank Indonesia API");
            }

            // Extract the exchange rate data
            var exchangeRates = diffgram.Descendants("Table")
                .Select(table => new ExchangeRateDto
                {
                    Id = Convert.ToInt32(table.Element("id_subkurslokal")?.Value),
                    Link = Convert.ToInt32(table.Element("lnk_subkurslokal")?.Value),
                    Value = Convert.ToDecimal(table.Element("nil_subkurslokal")?.Value),
                    BuyRate = Convert.ToDecimal(table.Element("beli_subkurslokal")?.Value),
                    SellRate = Convert.ToDecimal(table.Element("jual_subkurslokal")?.Value),
                    Date = table.Element("tgl_subkurslokal")?.Value,
                    Currency = table.Element("mts_subkurslokal")?.Value.Trim()
                })
                .ToList();

            // Return as JSON response
            return Ok(new
            {
                Success = true,
                Message = "Exchange rates retrieved successfully",
                Data = exchangeRates
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching exchange rates from Bank Indonesia");
            return StatusCode(500, new
            {
                Success = false,
                Message = "Error retrieving exchange rates",
                Error = ex.Message
            });
        }
    }
}

public class ExchangeRateDto
{
    public int Id { get; set; }
    public int Link { get; set; }
    public decimal Value { get; set; }
    public decimal BuyRate { get; set; }
    public decimal SellRate { get; set; }
    public string Date { get; set; }
    public string Currency { get; set; }
}