FROM mcr.microsoft.com/dotnet/aspnet:9.0

ARG PUBLISH_DIR=db-migrator
COPY ${PUBLISH_DIR}/ app/
WORKDIR /app
ENV ASPNETCORE_ENVIRONMENT=Production

# Install necessary locale packages
RUN apt-get update && apt-get install -y locales && \
    sed -i -e 's/# en_US.UTF-8 UTF-8/en_US.UTF-8 UTF-8/' /etc/locale.gen && \
    locale-gen

# Set locale environment variables
ENV LANG=en_US.UTF-8 \
    LANGUAGE=en_US:en \
    LC_ALL=en_US.UTF-8

# Create directory for certificates
RUN mkdir -p /app/certs

ENTRYPOINT ["dotnet", "Imip.HotelFrontOffice.DbMigrator.dll"]
