﻿apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: imip-wisma-ingress
  namespace: imip-wisma-dev-new
  annotations:
    nginx.ingress.kubernetes.io/proxy-buffer-size: "256k"
    nginx.ingress.kubernetes.io/proxy-buffers-number: "16"
    nginx.ingress.kubernetes.io/proxy-busy-buffers-size: "512k"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "600"
    nginx.ingress.kubernetes.io/client-header-timeout: "120"
    nginx.ingress.kubernetes.io/client-body-timeout: "120"
    nginx.ingress.kubernetes.io/proxy-next-upstream: "error timeout http_500 http_502 http_503 http_504"
    nginx.ingress.kubernetes.io/proxy-next-upstream-tries: "3"
    nginx.ingress.kubernetes.io/proxy-next-upstream-timeout: "120"
spec:
  ingressClassName: nginx
  rules:
    - host: api-wisma-dev.imip.co.id
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: imip-wisma-web
                port:
                  number: 80
