﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.HotelFrontOffice.Repositories
{
    public class PaymentRepository : EfCoreRepository<HotelFrontOfficeDbContext, Payments.Payment, Guid>, IPaymentsRepository
    {
        public PaymentRepository(IDbContextProvider<HotelFrontOfficeDbContext> dbContextProvider)
            : base(dbContextProvider)
        {
        }

        public async Task<List<Payments.Payment>> GetActiveAsync()
        {
            var dbContext = await GetDbContextAsync();
            return await dbContext.Payments
                .ToListAsync();
        }

        public async Task<Payments.Payment?> FindByNameAsync(string name)
        {
            var dbContext = await GetDbContextAsync();
            return await dbContext.Payments
                .Include(x => x.PaymentDetails)
                .Include(x => x.Reservations)
                .FirstOrDefaultAsync();

        }
    }
}