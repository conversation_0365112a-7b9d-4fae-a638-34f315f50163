﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.MasterStatuses;
using Volo.Abp.Application.Dtos;

namespace Imip.HotelFrontOffice.ServiceTypes
{
    public class ServiceTypesDto : AuditedEntityDto<Guid>
    {
        public string Name { get; set; } = default!;
        public Guid? StatusId { get; set; } = default!;

        public MasterStatusDto Status { get; set; } = default!;
    }
}