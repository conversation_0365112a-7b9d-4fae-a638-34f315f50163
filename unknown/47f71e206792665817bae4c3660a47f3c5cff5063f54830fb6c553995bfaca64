﻿using System;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.Account;
using Volo.Abp.Modularity;
using Volo.Abp.PermissionManagement;
using Volo.Abp.SettingManagement;
using Volo.Abp.VirtualFileSystem;
using Volo.Abp.FeatureManagement;
using Volo.Abp.Identity;
using Volo.Abp.TenantManagement;

namespace Imip.HotelFrontOffice;

[DependsOn(
    typeof(HotelFrontOfficeApplicationContractsModule),
    // typeof(AbpPermissionManagementHttpApiClientModule),
    typeof(AbpFeatureManagementHttpApiClientModule),
    // typeof(AbpAccountHttpApiClientModule),
    // typeof(AbpIdentityHttpApiClientModule),
    typeof(AbpTenantManagementHttpApiClientModule),
    typeof(AbpSettingManagementHttpApiClientModule)
)]
public class HotelFrontOfficeHttpApiClientModule : AbpModule
{
    public const string RemoteServiceName = "Default";

    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        var configuration = context.Services.GetConfiguration();

        context.Services.AddHttpClient(RemoteServiceName, client =>
        {
            client.BaseAddress = new Uri(configuration["RemoteServices:Default:BaseUrl"]);
        })
        .AddHttpMessageHandler<BearerTokenHandler>();  // This will handle token acquisition

        context.Services.AddTransient<BearerTokenHandler>();
        context.Services.AddSingleton<ITokenCache, InMemoryTokenCache>();

        context.Services.AddHttpClientProxies(
            typeof(HotelFrontOfficeApplicationContractsModule).Assembly,
            RemoteServiceName
        );

        Configure<AbpVirtualFileSystemOptions>(options =>
        {
            options.FileSets.AddEmbedded<HotelFrontOfficeHttpApiClientModule>();
        });
    }
}
