﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.HotelFrontOffice.PaymentGuests
{
    public interface ICreateUpdatePaymentGuests : ICrudAppService<
        PaymentGuestsDto,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdatePaymentGuestsDto,
        CreateUpdatePaymentGuestsDto
    >
    {
    }
}