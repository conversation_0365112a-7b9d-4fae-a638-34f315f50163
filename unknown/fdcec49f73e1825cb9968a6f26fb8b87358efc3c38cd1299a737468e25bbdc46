﻿using System;
using System.IO;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;

namespace Imip.HotelFrontOffice.EntityFrameworkCore;

/* This class is needed for EF Core console commands
 * (like Add-Migration and Update-Database commands) */
public class HotelFrontOfficeDbContextFactory : IDesignTimeDbContextFactory<HotelFrontOfficeDbContext>
{
    public HotelFrontOfficeDbContext CreateDbContext(string[] args)
    {
        var configuration = BuildConfiguration();
        
        HotelFrontOfficeEfCoreEntityExtensionMappings.Configure();

        var builder = new DbContextOptionsBuilder<HotelFrontOfficeDbContext>()
            .UseSqlServer(configuration.GetConnectionString("Default"));
        
        return new HotelFrontOfficeDbContext(builder.Options);
    }

    private static IConfigurationRoot BuildConfiguration()
    {
        var builder = new ConfigurationBuilder()
            .SetBasePath(Path.Combine(Directory.GetCurrentDirectory(), "../Imip.HotelFrontOffice.DbMigrator/"))
            .AddJsonFile("appsettings.json", optional: false);

        return builder.Build();
    }
}
