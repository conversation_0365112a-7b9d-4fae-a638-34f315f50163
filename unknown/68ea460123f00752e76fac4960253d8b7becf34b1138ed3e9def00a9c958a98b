﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.HotelFrontOffice.Repositories
{
    public class ReservationsRepository : EfCoreRepository<HotelFrontOfficeDbContext, Reservations.Reservation, Guid>, IReservationRepository
    {
        public ReservationsRepository(IDbContextProvider<HotelFrontOfficeDbContext> dbContextProvider)
            : base(dbContextProvider)
        {
        }

        public async Task<Reservations.Reservation?> FindByNameAsync(string name)
        {
            var dbContext = await GetDbContextAsync();
            return await dbContext.Reservations
                .Include(x => x.ReservationType)
                .Where(x => x.BookerName == name)
                .FirstOrDefaultAsync(); 
        }
    }
}