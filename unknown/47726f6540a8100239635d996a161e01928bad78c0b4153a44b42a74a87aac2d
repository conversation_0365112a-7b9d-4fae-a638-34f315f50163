using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Modularity;

namespace Imip.HotelFrontOffice.Permissions;

public class PermissionCheckerModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        // Register an HTTP client for Identity Server communication
        context.Services.AddHttpClient("IdentityServer");

        // Ensure HttpContextAccessor is registered
        context.Services.TryAddSingleton<IHttpContextAccessor, HttpContextAccessor>();

        // Register our custom permission checker
        // Use Replace to ensure our implementation is used instead of the default
        context.Services.Replace(ServiceDescriptor.Transient<IPermissionChecker, CentralizedPermissionChecker>());
    }
}