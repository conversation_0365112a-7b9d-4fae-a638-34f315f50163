{"ConnectionStrings": {"Default": "Server=localhost;Database=fowisma_dev;User ID=sa;Password=******;Integrated Security=false;TrustServerCertificate=true;Encrypt=true;MultipleActiveResultSets=false;"}, "Seq": {"ServerUrl": "http://**********:5341", "ApiKey": "uELPnmIUMo5mkQDNN61T"}, "OpenIddict": {"Applications": {"HotelFrontOffice_App": {"ClientId": "HotelFrontOffice_Web", "ClientSecret": "O6pBjKEGh2jnANJXMIZS7n64BSw0VA7h"}, "HotelFrontOffice_Swagger": {"ClientId": "HotelFrontOffice_Swagger", "RootUrl": "https://localhost:44357/"}}}, "BlobStoring": {"Default": {"Type": "SFTP", "SFTP": {"Host": "your-sftp-server.com", "Port": 22, "UserName": "your-username", "Password": "your-password", "PrivateKeyPath": "", "PrivateKeyPassphrase": "", "BaseDirectory": "/path/to/your/directory", "ConnectionTimeout": 30000, "OperationTimeout": 60000, "BufferSize": 4096, "CreateDirectoryIfNotExists": true}}}}