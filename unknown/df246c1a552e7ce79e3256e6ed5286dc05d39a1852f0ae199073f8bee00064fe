﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.HotelFrontOffice.Migrations
{
    /// <inheritdoc />
    public partial class update_payments_reservations_andRoomstatusLogs : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AppPaymentGuests_AppGuests_GuestId",
                table: "AppPaymentGuests");

            migrationBuilder.DropIndex(
                name: "IX_AppGuests_IdentifyNumber",
                table: "AppGuests");

            migrationBuilder.DropColumn(
                name: "FirstName",
                table: "AppGuests");

            migrationBuilder.DropColumn(
                name: "IdentifyNumber",
                table: "AppGuests");

            migrationBuilder.DropColumn(
                name: "LastName",
                table: "AppGuests");

            migrationBuilder.AddColumn<Guid>(
                name: "RoomStatusesId",
                table: "AppRoomStatusLogs",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "GuestId1",
                table: "AppPaymentGuests",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "PhoneNumber",
                table: "AppGuests",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(100)",
                oldMaxLength: 100,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Nationality",
                table: "AppGuests",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(100)",
                oldMaxLength: 100,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Email",
                table: "AppGuests",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(100)",
                oldMaxLength: 100,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CompanyName",
                table: "AppGuests",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(200)",
                oldMaxLength: 200,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Attachment",
                table: "AppGuests",
                type: "nvarchar(1000)",
                maxLength: 1000,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(1000)",
                oldMaxLength: 1000,
                oldNullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "AppGuests",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "AppGuests",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Fullname",
                table: "AppGuests",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "IdentityNumber",
                table: "AppGuests",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "AppGuests",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.CreateIndex(
                name: "IX_AppRoomStatusLogs_RoomStatusesId",
                table: "AppRoomStatusLogs",
                column: "RoomStatusesId");

            migrationBuilder.CreateIndex(
                name: "IX_AppPaymentGuests_GuestId1",
                table: "AppPaymentGuests",
                column: "GuestId1");

            migrationBuilder.CreateIndex(
                name: "IX_AppGuests_IdentityNumber",
                table: "AppGuests",
                column: "IdentityNumber");

            migrationBuilder.AddForeignKey(
                name: "FK_AppPaymentGuests_AppGuests_GuestId",
                table: "AppPaymentGuests",
                column: "GuestId",
                principalTable: "AppGuests",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_AppPaymentGuests_AppGuests_GuestId1",
                table: "AppPaymentGuests",
                column: "GuestId1",
                principalTable: "AppGuests",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AppRoomStatusLogs_AppRoomStatuses_RoomStatusesId",
                table: "AppRoomStatusLogs",
                column: "RoomStatusesId",
                principalTable: "AppRoomStatuses",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AppPaymentGuests_AppGuests_GuestId",
                table: "AppPaymentGuests");

            migrationBuilder.DropForeignKey(
                name: "FK_AppPaymentGuests_AppGuests_GuestId1",
                table: "AppPaymentGuests");

            migrationBuilder.DropForeignKey(
                name: "FK_AppRoomStatusLogs_AppRoomStatuses_RoomStatusesId",
                table: "AppRoomStatusLogs");

            migrationBuilder.DropIndex(
                name: "IX_AppRoomStatusLogs_RoomStatusesId",
                table: "AppRoomStatusLogs");

            migrationBuilder.DropIndex(
                name: "IX_AppPaymentGuests_GuestId1",
                table: "AppPaymentGuests");

            migrationBuilder.DropIndex(
                name: "IX_AppGuests_IdentityNumber",
                table: "AppGuests");

            migrationBuilder.DropColumn(
                name: "RoomStatusesId",
                table: "AppRoomStatusLogs");

            migrationBuilder.DropColumn(
                name: "GuestId1",
                table: "AppPaymentGuests");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "AppGuests");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "AppGuests");

            migrationBuilder.DropColumn(
                name: "Fullname",
                table: "AppGuests");

            migrationBuilder.DropColumn(
                name: "IdentityNumber",
                table: "AppGuests");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "AppGuests");

            migrationBuilder.AlterColumn<string>(
                name: "PhoneNumber",
                table: "AppGuests",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(100)",
                oldMaxLength: 100);

            migrationBuilder.AlterColumn<string>(
                name: "Nationality",
                table: "AppGuests",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(100)",
                oldMaxLength: 100);

            migrationBuilder.AlterColumn<string>(
                name: "Email",
                table: "AppGuests",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(100)",
                oldMaxLength: 100);

            migrationBuilder.AlterColumn<string>(
                name: "CompanyName",
                table: "AppGuests",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(200)",
                oldMaxLength: 200);

            migrationBuilder.AlterColumn<string>(
                name: "Attachment",
                table: "AppGuests",
                type: "nvarchar(1000)",
                maxLength: 1000,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(1000)",
                oldMaxLength: 1000);

            migrationBuilder.AddColumn<string>(
                name: "FirstName",
                table: "AppGuests",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "IdentifyNumber",
                table: "AppGuests",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LastName",
                table: "AppGuests",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_AppGuests_IdentifyNumber",
                table: "AppGuests",
                column: "IdentifyNumber");

            migrationBuilder.AddForeignKey(
                name: "FK_AppPaymentGuests_AppGuests_GuestId",
                table: "AppPaymentGuests",
                column: "GuestId",
                principalTable: "AppGuests",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
