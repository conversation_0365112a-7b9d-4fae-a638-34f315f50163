﻿using System;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Permissions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.PaymentMethods;

[Route("api/master/payment-method")]
[Authorize(WismaAppPermissions.PolicyPaymentMethod.Default)]
public class PaymentMethodAppService : CrudAppService<
    PaymentMethod,
    PaymentMethodDto,
    Guid,
    PagedAndSortedResultRequestDto,
    CreateUpdatePaymentMethodDto,
    CreateUpdatePaymentMethodDto
>
{
    public PaymentMethodAppService(IRepository<PaymentMethod, Guid> repository) : base(repository)
    {
    }

    [HttpGet]
    [Authorize(WismaAppPermissions.PolicyPaymentMethod.View)]
    public override Task<PagedResultDto<PaymentMethodDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        return base.GetListAsync(input);
    }

    [HttpGet("{id}")]
    [Authorize(WismaAppPermissions.PolicyPaymentMethod.View)]
    public override Task<PaymentMethodDto> GetAsync(Guid id)
    {
        return base.GetAsync(id);
    }

    [HttpPost]
    [Authorize(WismaAppPermissions.PolicyPaymentMethod.Create)]
    public override Task<PaymentMethodDto> CreateAsync(CreateUpdatePaymentMethodDto input)
    {
        return base.CreateAsync(input);
    }

    [HttpPut("{id}")]
    [Authorize(WismaAppPermissions.PolicyPaymentMethod.Edit)]
    public override Task<PaymentMethodDto> UpdateAsync(Guid id, CreateUpdatePaymentMethodDto input)
    {
        return base.UpdateAsync(id, input);
    }

    [HttpDelete("{id}")]
    [Authorize(WismaAppPermissions.PolicyPaymentMethod.Delete)]
    public override Task DeleteAsync(Guid id)
    {
        return base.DeleteAsync(id);
    }
}