﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.HotelFrontOffice.Migrations
{
    /// <inheritdoc />
    public partial class Added_RoomStatuses : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AppRoomStatuses",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Color = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    ExtraProperties = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ConcurrencyStamp = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AppRoomStatuses", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "AppRoomTypes",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Status = table.Column<short>(type: "smallint", nullable: false, defaultValue: (short)1),
                    ExtraProperties = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ConcurrencyStamp = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AppRoomTypes", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "AppRoom",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Price = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    Size = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    RoomNumber = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    RoomCode = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Information = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    RoomStatusId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    RoomTypeId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ExtraProperties = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ConcurrencyStamp = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AppRoom", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AppRoom_AppRoomStatuses_RoomStatusId",
                        column: x => x.RoomStatusId,
                        principalTable: "AppRoomStatuses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_AppRoom_AppRoomTypes_RoomTypeId",
                        column: x => x.RoomTypeId,
                        principalTable: "AppRoomTypes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AppRoom_Name",
                table: "AppRoom",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_AppRoom_RoomStatusId",
                table: "AppRoom",
                column: "RoomStatusId");

            migrationBuilder.CreateIndex(
                name: "IX_AppRoom_RoomTypeId",
                table: "AppRoom",
                column: "RoomTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_AppRoomStatuses_Name",
                table: "AppRoomStatuses",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_AppRoomTypes_Name",
                table: "AppRoomTypes",
                column: "Name");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AppRoom");

            migrationBuilder.DropTable(
                name: "AppRoomStatuses");

            migrationBuilder.DropTable(
                name: "AppRoomTypes");
        }
    }
}
