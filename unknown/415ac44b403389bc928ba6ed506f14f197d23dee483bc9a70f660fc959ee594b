#!/bin/bash
# Script to clean up Kubernetes resources and debug issues
# Usage: ./k8s-cleanup.sh [namespace]

# Default values
NAMESPACE=${1:-"imip-wisma-dev-new"}

echo "=== Kubernetes Cleanup and Debug Tool ==="
echo "Working with namespace: $NAMESPACE"
echo

# Function to clean up resources
cleanup_resources() {
  echo "=== Cleaning up resources ==="
  
  # Delete pending pods
  echo "Deleting pending pods..."
  kubectl get pods -n $NAMESPACE --field-selector=status.phase=Pending -o name | xargs -r kubectl delete -n $NAMESPACE
  
  # Delete failed pods
  echo "Deleting failed pods..."
  kubectl get pods -n $NAMESPACE --field-selector=status.phase=Failed -o name | xargs -r kubectl delete -n $NAMESPACE
  
  # Force delete pods with ImagePullBackOff
  echo "Force deleting pods with ImagePullBackOff..."
  for pod in $(kubectl get pods -n $NAMESPACE | grep ImagePullBackOff | awk '{print $1}'); do
    kubectl delete pod $pod -n $NAMESPACE --force --grace-period=0
  done
  
  # Delete completed jobs
  echo "Deleting completed jobs..."
  kubectl get jobs -n $NAMESPACE --field-selector status.successful=1 -o name | xargs -r kubectl delete -n $NAMESPACE
  
  # Delete failed jobs
  echo "Deleting failed jobs..."
  kubectl get jobs -n $NAMESPACE --field-selector status.failed=1 -o name | xargs -r kubectl delete -n $NAMESPACE
  
  echo "Cleanup completed."
  echo
}

# Function to recreate registry credentials
recreate_registry_credentials() {
  echo "=== Recreating GitLab registry credentials ==="
  
  # Get GitLab credentials
  read -p "Enter GitLab registry username: " REGISTRY_USER
  read -s -p "Enter GitLab registry token: " REGISTRY_TOKEN
  echo
  
  # Delete existing secret
  kubectl delete secret gitlab-registry-credentials -n $NAMESPACE --ignore-not-found
  
  # Create new secret
  kubectl create secret docker-registry gitlab-registry-credentials \
    --namespace=$NAMESPACE \
    --docker-server=registry.gitlab.com \
    --docker-username=$REGISTRY_USER \
    --docker-password=$REGISTRY_TOKEN \
    --docker-email=$<EMAIL>
  
  echo "Registry credentials recreated."
  echo
}

# Function to check node resources
check_node_resources() {
  echo "=== Checking node resources ==="
  
  # Get nodes
  kubectl get nodes
  
  # Check resource allocation
  echo "Resource allocation by node:"
  kubectl describe nodes | grep -A 10 "Allocated resources"
  
  echo
}

# Function to check pod status
check_pod_status() {
  echo "=== Checking pod status ==="
  
  # Get all pods
  kubectl get pods -n $NAMESPACE -o wide
  
  # Check events
  echo "Recent events:"
  kubectl get events -n $NAMESPACE --sort-by='.lastTimestamp' | tail -n 20
  
  echo
}

# Function to restart deployment
restart_deployment() {
  echo "=== Restarting deployment ==="
  
  # Get deployments
  kubectl get deployments -n $NAMESPACE
  
  # Ask which deployment to restart
  read -p "Enter deployment name to restart: " DEPLOYMENT
  
  # Restart deployment
  kubectl rollout restart deployment/$DEPLOYMENT -n $NAMESPACE
  
  echo "Deployment $DEPLOYMENT restarted."
  echo
}

# Main menu
while true; do
  echo "=== Main Menu ==="
  echo "1. Clean up resources"
  echo "2. Recreate registry credentials"
  echo "3. Check node resources"
  echo "4. Check pod status"
  echo "5. Restart deployment"
  echo "6. Exit"
  read -p "Enter your choice: " CHOICE
  
  case $CHOICE in
    1) cleanup_resources ;;
    2) recreate_registry_credentials ;;
    3) check_node_resources ;;
    4) check_pod_status ;;
    5) restart_deployment ;;
    6) echo "Exiting."; exit 0 ;;
    *) echo "Invalid choice. Please try again." ;;
  esac
done
