﻿apiVersion: v1
kind: Secret
metadata:
  name: imip-wisma-secrets
  namespace: imip-wisma-prod
type: Opaque
stringData:
  ConnectionStrings__Default: "${PROD_DB_CONNECTION}"
  Seq__ApiKey: "${SEQ_API_KEY}"
  AuthServer__CertificatePassPhrase: "${CERT_PASSPHRASE}"
  StringEncryption__DefaultPassPhrase: "${ENCRYPTION_PASSPHRASE}"
  AuthServer__ClientId: "${PROD_AUTH_CLIENT_ID}"
  AuthServer__ClientSecret: "${PROD_AUTH_CLIENT_SECRET}"
