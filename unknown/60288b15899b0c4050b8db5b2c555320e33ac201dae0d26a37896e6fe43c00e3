using System;
using System.Collections.Generic;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.HotelFrontOffice.EntityChangeLogs;

/// <summary>
/// Represents a log entry for an entity change
/// </summary>
public class EntityChangeLog : FullAuditedAggregateRoot<Guid>
{
    /// <summary>
    /// The time when the entity was changed
    /// </summary>
    public DateTime ChangeTime { get; set; }

    /// <summary>
    /// Type of change: Created, Updated, or Deleted
    /// </summary>
    public EntityChangeType ChangeType { get; set; }

    /// <summary>
    /// The ID of the entity that was changed
    /// </summary>
    public string? EntityId { get; set; }

    /// <summary>
    /// The tenant ID of the entity that was changed (if multi-tenant)
    /// </summary>
    public Guid? EntityTenantId { get; set; }

    /// <summary>
    /// Full type name of the entity
    /// </summary>
    public string? EntityTypeFullName { get; set; }

    /// <summary>
    /// User-friendly display name of the entity type
    /// </summary>
    public string? EntityDisplayName { get; set; }

    /// <summary>
    /// User-friendly display value for the entity (usually the name or title)
    /// </summary>
    public string? EntityDisplayValue { get; set; }

    /// <summary>
    /// Reference to the original ABP audit log ID
    /// </summary>
    public Guid AuditLogId { get; set; }

    /// <summary>
    /// Collection of property changes for this entity change
    /// </summary>
    public virtual ICollection<EntityPropertyChangeLog> PropertyChanges { get; set; }

    protected EntityChangeLog()
    {
        PropertyChanges = new List<EntityPropertyChangeLog>();
    }

    public EntityChangeLog(
        Guid id,
        DateTime changeTime,
        EntityChangeType changeType,
        string? entityId,
        string? entityTypeFullName,
        Guid auditLogId,
        Guid? entityTenantId = null,
        string? entityDisplayName = null,
        string? entityDisplayValue = null) : base(id)
    {
        ChangeTime = changeTime;
        ChangeType = changeType;
        EntityId = entityId;
        EntityTypeFullName = entityTypeFullName;
        AuditLogId = auditLogId;
        EntityTenantId = entityTenantId;
        EntityDisplayName = entityDisplayName;
        EntityDisplayValue = entityDisplayValue;
        PropertyChanges = new List<EntityPropertyChangeLog>();
    }
}

/// <summary>
/// Type of entity change
/// </summary>
public enum EntityChangeType : byte
{
    Created = 0,
    Updated = 1,
    Deleted = 2
}