#!/bin/bash
# Script to clean up stuck pods in Kubernetes
# Usage: ./cleanup-pods.sh [namespace]

# Default values
NAMESPACE=${1:-"imip-wisma-dev-new"}

echo "Checking for stuck pods in namespace: $NAMESPACE"

# Check for Pending pods
echo "Looking for Pending pods..."
PENDING_PODS=$(kubectl get pods -n $NAMESPACE --field-selector=status.phase=Pending -o name)
if [ -n "$PENDING_PODS" ]; then
  echo "Found Pending pods:"
  echo "$PENDING_PODS"
  
  read -p "Do you want to delete these pods? (y/n): " -n 1 -r
  echo
  if [[ $REPLY =~ ^[Yy]$ ]]; then
    kubectl delete $PENDING_PODS -n $NAMESPACE
    echo "Pending pods deleted."
  else
    echo "Skipping deletion of Pending pods."
  fi
else
  echo "No Pending pods found."
fi

# Check for ImagePullBackOff pods
echo "Looking for pods with ImagePullBackOff or other issues..."
FAILED_PODS=$(kubectl get pods -n $NAMESPACE | grep -v "Running\|Completed\|Succeeded" | grep -v "NAME" | awk '{print $1}')
if [ -n "$FAILED_PODS" ]; then
  echo "Found problematic pods:"
  for pod in $FAILED_PODS; do
    echo "- $pod ($(kubectl get pod $pod -n $NAMESPACE -o jsonpath='{.status.phase}'))"
    echo "  Status: $(kubectl get pod $pod -n $NAMESPACE -o jsonpath='{.status.containerStatuses[0].state}')"
  done
  
  read -p "Do you want to force delete these pods? (y/n): " -n 1 -r
  echo
  if [[ $REPLY =~ ^[Yy]$ ]]; then
    for pod in $FAILED_PODS; do
      kubectl delete pod $pod -n $NAMESPACE --force --grace-period=0
      echo "Pod $pod force deleted."
    done
  else
    echo "Skipping deletion of problematic pods."
  fi
else
  echo "No problematic pods found."
fi

# Check for orphaned jobs
echo "Looking for completed or failed jobs..."
JOBS=$(kubectl get jobs -n $NAMESPACE -o name)
if [ -n "$JOBS" ]; then
  echo "Found jobs:"
  kubectl get jobs -n $NAMESPACE
  
  read -p "Do you want to delete completed/failed jobs? (y/n): " -n 1 -r
  echo
  if [[ $REPLY =~ ^[Yy]$ ]]; then
    kubectl delete jobs -n $NAMESPACE --field-selector status.successful=1
    kubectl delete jobs -n $NAMESPACE --field-selector status.failed=1
    echo "Completed and failed jobs deleted."
  else
    echo "Skipping deletion of jobs."
  fi
else
  echo "No jobs found."
fi

# Check node status
echo "Checking node status..."
kubectl get nodes
echo

# Check for resource constraints
echo "Checking resource usage by node..."
kubectl describe nodes | grep -A 5 "Allocated resources"
echo

# Show recent events
echo "Recent events in namespace $NAMESPACE:"
kubectl get events -n $NAMESPACE --sort-by='.lastTimestamp' | tail -n 20
