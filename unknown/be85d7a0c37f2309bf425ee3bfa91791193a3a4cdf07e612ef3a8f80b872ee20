using System;
using System.Threading.Tasks;

public interface ITokenCache
{
    Task<string> GetAsync();
    Task SetAsync(string token, int expiresIn);
}

public class InMemoryTokenCache : ITokenCache
{
    private string _token;
    private DateTime _expiration;

    public Task<string> GetAsync()
    {
        if (_token != null && DateTime.UtcNow < _expiration)
        {
            return Task.FromResult(_token);
        }
        return Task.FromResult<string>(null);
    }

    public Task SetAsync(string token, int expiresIn)
    {
        _token = token;
        _expiration = DateTime.UtcNow.AddSeconds(expiresIn - 60); // Buffer of 1 minute
        return Task.CompletedTask;
    }
}