using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;

namespace Imip.HotelFrontOffice.Web.Controllers
{
    [Route("api/test-authorization")]
    [ApiController]
    public class TestAuthorizationController : ControllerBase
    {
        private readonly ILogger<TestAuthorizationController> _logger;

        public TestAuthorizationController(ILogger<TestAuthorizationController> logger)
        {
            _logger = logger;
        }

        [HttpGet("public")]
        public IActionResult GetPublic()
        {
            return Ok(new { message = "This is a public endpoint that anyone can access" });
        }

        [HttpGet("authenticated")]
        [Authorize]
        public IActionResult GetAuthenticated()
        {
            return Ok(new { message = "This is an authenticated endpoint that requires a valid token" });
        }

        [HttpGet("admin-only")]
        [Authorize("IdentityServer.OpenIddictResources.Delete")]
        public IActionResult GetAdminOnly()
        {
            return Ok(new { message = "This is an admin-only endpoint that requires the IdentityServer.OpenIddictResources.Delete permission" });
        }

        [HttpGet("wisma-room")]
        [Authorize("WismaApp.Room")]
        public IActionResult GetWismaRoom()
        {
            return Ok(new { message = "This is an endpoint that requires the WismaApp.Room permission" });
        }
    }
}
