﻿using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace Imip.HotelFrontOffice.TypeFoodAndBeverages
{
    /*public interface ITypeFoodAndBeverageAppService : ICrudAppService<
        TypeFoodAndBeverageDTO,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdateTypeFoodAndBeverageDTO,
        CreateUpdateTypeFoodAndBeverageDTO>
    {
        
    }*/
    
    public interface ITypeFoodAndBeverageAppService : IApplicationService
    {
        Task<TypeFoodAndBeverageDto> CreateAsync(CreateUpdateTypeFoodAndBeverageDto input);
    }
}