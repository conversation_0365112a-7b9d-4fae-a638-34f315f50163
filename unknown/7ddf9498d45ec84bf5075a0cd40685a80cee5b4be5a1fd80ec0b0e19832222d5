using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Volo.Abp.DependencyInjection;

namespace Imip.HotelFrontOffice.Web.Middleware;

public class ExternalUserAuthenticationMiddleware : IMiddleware, ITransientDependency
{
    private readonly ILogger<ExternalUserAuthenticationMiddleware> _logger;

    public ExternalUserAuthenticationMiddleware(ILogger<ExternalUserAuthenticationMiddleware> logger)
    {
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, RequestDelegate next)
    {
        try
        {
            // Check if we have a user from the token
            if (context.User?.Identity?.IsAuthenticated == true)
            {
                _logger.LogDebug("User is authenticated from token");

                // Continue with the authenticated user
                await next(context);
                return;
            }

            // If we get here, continue with the pipeline
            await next(context);
        }
        catch (Exception ex) when (IsUserNotFoundError(ex))
        {
            _logger.LogWarning(ex, "User not found in local database, but token is valid. Continuing with token claims.");

            // Extract the token from the Authorization header
            var authHeader = context.Request.Headers.Authorization.FirstOrDefault();
            if (authHeader != null && authHeader.StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase))
            {
                _logger.LogDebug("Valid bearer token found, continuing with pipeline");
            }

            // Continue with the pipeline regardless of token presence
            await next(context);
        }
        catch (Exception ex)
        {
            // For other exceptions, log and rethrow
            _logger.LogError(ex, "Error in ExternalUserAuthenticationMiddleware");
            throw;
        }
    }

    private static bool IsUserNotFoundError(Exception ex)
    {
        // Check if the exception is related to a user not found in the local database
        var exceptionMessage = ex.ToString();
        return exceptionMessage.Contains("User not found") ||
               exceptionMessage.Contains("EntityNotFoundException") ||
               exceptionMessage.Contains("There is no such an entity. Entity type: Volo.Abp.Identity.IdentityUser");
    }
}
