using System;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Permissions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.MasterStatuses;

[Route("api/master/status")]
[Authorize(WismaAppPermissions.PolicyMasterStatus.Default)]
public class MasterStatusAppService : CrudAppService<
    MasterStatus,
    MasterStatusDto,
    Guid,
    PagedAndSortedResultRequestDto,
    CreateUpdateMasterStatusDto,
    CreateUpdateMasterStatusDto
>
{
    public MasterStatusAppService(IRepository<MasterStatus, Guid> repository) : base(repository)
    {
    }

    [HttpGet]
    [Authorize(WismaAppPermissions.PolicyMasterStatus.View)]
    public override Task<PagedResultDto<MasterStatusDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        return base.GetListAsync(input);
    }

    [HttpGet("{id}")]
    [Authorize(WismaAppPermissions.PolicyMasterStatus.View)]
    public override Task<MasterStatusDto> GetAsync(Guid id)
    {
        return base.GetAsync(id);
    }

    [HttpPost]
    [Authorize(WismaAppPermissions.PolicyMasterStatus.Create)]
    public override Task<MasterStatusDto> CreateAsync(CreateUpdateMasterStatusDto input)
    {
        return base.CreateAsync(input);
    }

    [HttpPut("{id}")]
    [Authorize(WismaAppPermissions.PolicyMasterStatus.Edit)]
    public override Task<MasterStatusDto> UpdateAsync(Guid id, CreateUpdateMasterStatusDto input)
    {
        return base.UpdateAsync(id, input);
    }

    [HttpDelete("{id}")]
    [Authorize(WismaAppPermissions.PolicyMasterStatus.Delete)]
    public override Task DeleteAsync(Guid id)
    {
        return base.DeleteAsync(id);
    }
}