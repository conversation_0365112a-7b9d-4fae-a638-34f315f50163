using System;
using System.ComponentModel.DataAnnotations;
using Imip.HotelFrontOffice.Documents;

namespace Imip.HotelFrontOffice.Documents;

/// <summary>
/// DTO for DOCX to PDF conversion
/// </summary>
public class DocxToPdfConversionDto
{
    /// <summary>
    /// The document type to determine which template to use
    /// </summary>
    [Required]
    public DocumentType DocumentType { get; set; }

    /// <summary>
    /// Optional specific template ID to use (if not provided, the default template for the document type will be used)
    /// </summary>
    public Guid? TemplateId { get; set; }

    /// <summary>
    /// Configuration for the template
    /// </summary>
    [Required]
    public TemplateConfigDto? Config { get; set; }

    /// <summary>
    /// Data model for the template
    /// </summary>
    [Required]
    public object? Model { get; set; }
}

/// <summary>
/// DTO for template configuration
/// </summary>
public class TemplateConfigDto
{
    /// <summary>
    /// Delimiter configuration for the template
    /// </summary>
    [Required]
    public DelimiterConfigDto? Delimiter { get; set; }
}

/// <summary>
/// DTO for delimiter configuration
/// </summary>
public class DelimiterConfigDto
{
    /// <summary>
    /// Start delimiter for placeholders
    /// </summary>
    [Required]
    public string Start { get; set; } = "{{";

    /// <summary>
    /// End delimiter for placeholders
    /// </summary>
    [Required]
    public string End { get; set; } = "}}";
}
