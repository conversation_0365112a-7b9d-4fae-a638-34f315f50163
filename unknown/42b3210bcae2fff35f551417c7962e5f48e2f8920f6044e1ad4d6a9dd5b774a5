using Microsoft.EntityFrameworkCore.Migrations;

namespace Imip.HotelFrontOffice.Migrations
{
    public partial class Update_Guest_Columns : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Drop LastName column
            migrationBuilder.DropColumn(
                name: "LastName",
                table: "AppGuests");

            // Rename FirstName to Fullname
            migrationBuilder.RenameColumn(
                name: "FirstName",
                table: "AppGuests",
                newName: "Fullname");

            // Rename IdentifyNumber to IdentityNumber
            migrationBuilder.RenameColumn(
                name: "IdentifyNumber",
                table: "AppGuests",
                newName: "IdentityNumber");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Add back LastName column
            migrationBuilder.AddColumn<string>(
                name: "LastName",
                table: "AppGuests",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true);

            // Rename back Fullname to FirstName
            migrationBuilder.RenameColumn(
                name: "Fullname",
                table: "AppGuests",
                newName: "FirstName");

            // Rename back IdentityNumber to IdentifyNumber
            migrationBuilder.RenameColumn(
                name: "IdentityNumber",
                table: "AppGuests",
                newName: "IdentifyNumber");
        }
    }
}