{"template": "app", "imports": {"Volo.Abp.LeptonXLiteTheme": {"version": "4.0.5", "isInstalled": true}, "Volo.Abp.Account": {"version": "9.0.4", "isInstalled": true}, "Volo.Abp.OpenIddict": {"version": "9.0.4", "isInstalled": true}, "Volo.Abp.Identity": {"version": "9.0.4", "isInstalled": true}, "Volo.Abp.TenantManagement": {"version": "9.0.4", "isInstalled": true}, "Volo.Abp.SettingManagement": {"version": "9.0.4", "isInstalled": true}, "Volo.Abp.PermissionManagement": {"version": "9.0.4", "isInstalled": true}, "Volo.Abp.FeatureManagement": {"version": "9.0.4", "isInstalled": true}}, "folders": {"items": {"src": {}, "test": {}}}, "packages": {"Imip.HotelFrontOffice.Application": {"path": "src/Imip.HotelFrontOffice.Application/Imip.HotelFrontOffice.Application.abppkg", "folder": "src"}, "Imip.HotelFrontOffice.Application.Tests": {"path": "test/Imip.HotelFrontOffice.Application.Tests/Imip.HotelFrontOffice.Application.Tests.abppkg", "folder": "test"}, "Imip.HotelFrontOffice.Domain.Shared": {"path": "src/Imip.HotelFrontOffice.Domain.Shared/Imip.HotelFrontOffice.Domain.Shared.abppkg", "folder": "src"}, "Imip.HotelFrontOffice.Application.Contracts": {"path": "src/Imip.HotelFrontOffice.Application.Contracts/Imip.HotelFrontOffice.Application.Contracts.abppkg", "folder": "src"}, "Imip.HotelFrontOffice.HttpApi": {"path": "src/Imip.HotelFrontOffice.HttpApi/Imip.HotelFrontOffice.HttpApi.abppkg", "folder": "src"}, "Imip.HotelFrontOffice.HttpApi.Client": {"path": "src/Imip.HotelFrontOffice.HttpApi.Client/Imip.HotelFrontOffice.HttpApi.Client.abppkg", "folder": "src"}, "Imip.HotelFrontOffice.Web": {"path": "src/Imip.HotelFrontOffice.Web/Imip.HotelFrontOffice.Web.abppkg", "folder": "src"}, "Imip.HotelFrontOffice.Web.Tests": {"path": "test/Imip.HotelFrontOffice.Web.Tests/Imip.HotelFrontOffice.Web.Tests.abppkg", "folder": "test"}, "Imip.HotelFrontOffice.EntityFrameworkCore.Tests": {"path": "test/Imip.HotelFrontOffice.EntityFrameworkCore.Tests/Imip.HotelFrontOffice.EntityFrameworkCore.Tests.abppkg", "folder": "test"}, "Imip.HotelFrontOffice.EntityFrameworkCore": {"path": "src/Imip.HotelFrontOffice.EntityFrameworkCore/Imip.HotelFrontOffice.EntityFrameworkCore.abppkg", "folder": "src"}, "Imip.HotelFrontOffice.TestBase": {"path": "test/Imip.HotelFrontOffice.TestBase/Imip.HotelFrontOffice.TestBase.abppkg", "folder": "test"}, "Imip.HotelFrontOffice.Domain.Tests": {"path": "test/Imip.HotelFrontOffice.Domain.Tests/Imip.HotelFrontOffice.Domain.Tests.abppkg", "folder": "test"}, "Imip.HotelFrontOffice.HttpApi.Client.ConsoleTestApp": {"path": "test/Imip.HotelFrontOffice.HttpApi.Client.ConsoleTestApp/Imip.HotelFrontOffice.HttpApi.Client.ConsoleTestApp.abppkg", "folder": "test"}, "Imip.HotelFrontOffice.DbMigrator": {"path": "src/Imip.HotelFrontOffice.DbMigrator/Imip.HotelFrontOffice.DbMigrator.abppkg", "folder": "src"}, "Imip.HotelFrontOffice.Domain": {"path": "src/Imip.HotelFrontOffice.Domain/Imip.HotelFrontOffice.Domain.abppkg", "folder": "src"}}}