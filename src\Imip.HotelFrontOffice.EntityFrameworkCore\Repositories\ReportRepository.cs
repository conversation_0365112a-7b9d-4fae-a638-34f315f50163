using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.EntityFrameworkCore;
using Imip.HotelFrontOffice.Reports;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.HotelFrontOffice.Repositories;

public class ReportRepository : EfCoreRepository<HotelFrontOfficeDbContext, Report, Guid>, IReportRepository
{
    public ReportRepository(IDbContextProvider<HotelFrontOfficeDbContext> dbContextProvider)
        : base(dbContextProvider)
    {
    }

    public async Task<List<Report>> GetActiveReportsAsync()
    {
        var dbSet = await GetDbSetAsync();
        return await dbSet.Where(r => r.IsActive).ToListAsync();
    }

    public async Task<Report> GetByNameAsync(string name)
    {
        var dbSet = await GetDbSetAsync();
        return await dbSet.FirstOrDefaultAsync(r => r.Name == name);
    }
}