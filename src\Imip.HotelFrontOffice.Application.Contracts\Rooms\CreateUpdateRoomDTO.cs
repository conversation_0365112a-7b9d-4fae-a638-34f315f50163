﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Imip.HotelFrontOffice.Rooms;

public class CreateUpdateRoomDto
{

    [Required]
    public string RoomNumber { get; set; } = default!;

    [Required]
    public string RoomCode { get; set; } = default!;
    [Required]
    public string Size { get; set; } = default!;
    public string Information { get; set; } = default!;
    public Guid RoomStatusId { get; set; } = default!;
    public Guid RoomTypeId { get; set; } = default!;
    public decimal Price { get; set; } = default!;

    // Dictionary for extra properties
    public Dictionary<string, object>? ExtraProperties { get; set; }
}