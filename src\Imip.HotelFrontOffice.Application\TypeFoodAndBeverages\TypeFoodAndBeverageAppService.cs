﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Permissions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.TypeFoodAndBeverages;

[Authorize(WismaAppPermissions.PolicyServiceType.Default)]
public class TypeFoodAndBeverageAppService : PermissionCheckedCrudAppService<
        TypeFoodAndBeverage,
        TypeFoodAndBeverageDto,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdateTypeFoodAndBeverageDto,
        CreateUpdateTypeFoodAndBeverageDto
    >, ITypeFoodAndBeverageAppService
{
    private readonly IRepository<TypeFoodAndBeverage, Guid> _repository;
    public TypeFoodAndBeverageAppService(IRepository<TypeFoodAndBeverage, Guid> repository, IPermissionChecker permissionChecker)
        : base(repository, permissionChecker)
    {
        _repository = repository;

        GetPolicyName = WismaAppPermissions.PolicyServiceType.View;
        GetListPolicyName = WismaAppPermissions.PolicyServiceType.View;
        CreatePolicyName = WismaAppPermissions.PolicyServiceType.Create;
        UpdatePolicyName = WismaAppPermissions.PolicyServiceType.Edit;
        DeletePolicyName = WismaAppPermissions.PolicyServiceType.Delete;
    }

    protected override async Task<IQueryable<TypeFoodAndBeverage>> CreateFilteredQueryAsync(
        PagedAndSortedResultRequestDto input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .Include(x => x.Status);
    }

    public override async Task<TypeFoodAndBeverageDto> GetAsync(Guid id)
    {
        await CheckGetPolicyAsync();

        var query = await Repository.GetQueryableAsync();
        var typeFoodAndBeverage = await query
            .Include(x => x.Status)
            .FirstOrDefaultAsync(x => x.Id == id);

        if (typeFoodAndBeverage is null)
        {
            throw new EntityNotFoundException(typeof(TypeFoodAndBeverage), id);
        }

        return ObjectMapper.Map<TypeFoodAndBeverage, TypeFoodAndBeverageDto>(typeFoodAndBeverage);
    }

}