﻿using System;
using System.Collections.Generic;
using Imip.HotelFrontOffice.Guests;
using Imip.HotelFrontOffice.MasterStatuses;
using Imip.HotelFrontOffice.PaymentDetails;
using Imip.HotelFrontOffice.ReservationFoodAndBeverages;
using Imip.HotelFrontOffice.ReservationRooms;
using Imip.HotelFrontOffice.Reservations;
using Imip.HotelFrontOffice.Rooms;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.HotelFrontOffice.ReservationDetails;

public class ReservationDetail : FullAuditedAggregateRoot<Guid>
{
    public DateTime? CheckInDate { get; set; }
    public DateTime? CheckOutDate { get; set; }
    public string? Rfid { get; set; }
    public decimal Price { get; set; }
    public Guid ReservationId { get; set; }
    public Guid RoomId { get; set; }
    public Guid GuestId { get; set; }
    public Guid? StatusId { get; set; }
    public Guid? PaymentStatusId { get; set; }
    public Guid? CriteriaId { get; set; }
    public virtual Room? Room { get; set; }
    public virtual Guest? Guest { get; set; }
    public virtual MasterStatus? Status { get; set; }
    public virtual MasterStatus? PaymentStatus { get; set; }
    public virtual MasterStatus? Criteria { get; set; }
    public virtual Reservation? Reservation { get; set; }

    public virtual ICollection<ReservationRoom>? ReservationRooms { get; set; }

    public virtual ICollection<ReservationFoodAndBeverage>? ReservationFoodAndBeverages
    {
        get;
        set;
    }

    public virtual ICollection<PaymentDetail>? PaymentDetails { get; set; }


    protected ReservationDetail()
    {
        ReservationRooms = new HashSet<ReservationRoom>();
        ReservationFoodAndBeverages = new HashSet<ReservationFoodAndBeverage>();
        PaymentDetails = new HashSet<PaymentDetail>();
    }

    public ReservationDetail(
        Guid id,
        DateTime? checkInDate,
        DateTime? checkOutDate,
        string rfid,
        decimal price,
        Guid reservationId,
        Guid roomId,
        Guid? statusId,
        Guid? paymentStatusId,
        Guid guestId,
        Guid? criteriaId = null)
    {
        Id = id;
        CheckInDate = checkInDate;
        CheckOutDate = checkOutDate;
        Rfid = rfid;
        Price = price;
        ReservationId = reservationId;
        RoomId = roomId;
        StatusId = statusId;
        PaymentStatusId = paymentStatusId;
        GuestId = guestId;
        CriteriaId = criteriaId;
    }
}