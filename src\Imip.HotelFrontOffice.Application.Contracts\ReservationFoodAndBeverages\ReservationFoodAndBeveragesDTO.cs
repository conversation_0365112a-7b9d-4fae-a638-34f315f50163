﻿using System;
using Imip.HotelFrontOffice.FoodAndBeverages;
using Imip.HotelFrontOffice.MasterStatuses;
using Imip.HotelFrontOffice.PaymentDetails;
using Volo.Abp.Application.Dtos;

namespace Imip.HotelFrontOffice.ReservationFoodAndBeverages
{
    public class ReservationFoodAndBeveragesDto : AuditedEntityDto<Guid>
    {
        public Guid ReservationDetailsId { get; set; } = default!;
        public Guid FoodAndBeverageId { get; set; } = default!;
        public Guid? PaymentStatusId { get; set; } = default!; // Optional property for payment status ID
        public decimal TotalPrice { get; set; } = default!;
        public int Quantity { get; set; } = default!;
        public string FoodAndBeverageName { get; set; } = default!;
        public string FoodAndBeverageTypeName { get; set; } = default!;
        public DateTime? TransactionDate { get; set; } = default!;
        public MasterStatusDto? PaymentStatus { get; set; } = default!; // Optional property for payment status
        public FoodAndBeverageDto? FoodAndBeverage { get; set; } = default!;
        public PaymentDetailsDto? PaymentDetails { get; set; } = default!;
    }
}