﻿using System;
using Imip.HotelFrontOffice.Guests;
using Imip.HotelFrontOffice.MasterStatuses;

namespace Imip.HotelFrontOffice.ReservationDetails;

public class CreateUpdateReservationDetailsDto
{
    public Guid ReservationId { get; set; } = default!;
    public Guid RoomId { get; set; } = default!;
    public Guid StatusId { get; set; } = default!;
    public Guid? PaymentStatusId { get; set; } = default!;
    public Guid? CriteriaId { get; set; } = default!;
    public Guid? GuestId { get; set; }  // Make nullable
    public DateTime? CheckInDate { get; set; } = default!;
    public DateTime? CheckOutDate { get; set; } = default!;
    public string Rfid { get; set; } = default!;
    public decimal Price { get; set; } = default!;
    public GuestDataDto? GuestData { get; set; }  // Add GuestData
    public MasterStatusDto? Status { get; set; }  // Add GuestData
    public MasterStatusDto? PaymentStatus { get; set; }  // Add GuestData
}