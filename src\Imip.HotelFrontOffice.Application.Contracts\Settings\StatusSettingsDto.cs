using System.ComponentModel.DataAnnotations;

namespace Imip.HotelFrontOffice.Settings;

/// <summary>
/// DTO for status settings
/// </summary>
public class StatusSettingsDto
{
    /// <summary>
    /// Reservation status name for Check IN
    /// </summary>
    [Required]
    [StringLength(50)]
    public string CheckInStatusName { get; set; } = default!;

    /// <summary>
    /// Reservation status name for Check OUT
    /// </summary>
    [Required]
    [StringLength(50)]
    public string CheckOutStatusName { get; set; } = default!;

    /// <summary>
    /// Room status name for Occupied In Stay
    /// </summary>
    [Required]
    [StringLength(50)]
    public string OccupiedInStayStatusName { get; set; } = default!;

    /// <summary>
    /// Room status name for Dirty
    /// </summary>
    [Required]
    [StringLength(50)]
    public string DirtyStatusName { get; set; } = default!;
}

/// <summary>
/// DTO for updating a single setting
/// </summary>
public class UpdateSettingDto
{
    /// <summary>
    /// The setting name
    /// </summary>
    [Required]
    public string Name { get; set; } = default!;

    /// <summary>
    /// The setting value
    /// </summary>
    [Required]
    public string Value { get; set; } = default!;
}
