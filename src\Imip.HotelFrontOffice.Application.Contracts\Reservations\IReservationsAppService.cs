﻿using System;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Models;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.HotelFrontOffice.Reservations;

public interface IReservationsAppService : ICrudAppService<
    ReservationsDto,
    Guid,
    PagedAndSortedResultRequestDto,
    CreateUpdateReservationsDto,
    CreateUpdateReservationsDto
>
{
    Task<PagedResultDto<ReservationsDto>> GetListAsync(PagedAndSortedResultRequestDto input);
    // Task<PagedResultDto<ReservationsDto>> FilterAsync(FilteredPagedAndSortedResultRequestDto input);
    Task<object> CreateReservationWithDetailsAsync(CreateReservationWithDetailsDto input);
    Task<ReservationsDto> UpdateWithDetailsAsync(Guid id, UpdateReservationWithDetailsDto input);
    Task<ReservationsDto> GetWithDetailsAsync(Guid id);
}
