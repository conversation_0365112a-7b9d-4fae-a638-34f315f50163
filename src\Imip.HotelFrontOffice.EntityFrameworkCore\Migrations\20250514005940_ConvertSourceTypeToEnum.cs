﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.HotelFrontOffice.Migrations
{
    /// <inheritdoc />
    public partial class ConvertSourceTypeToEnum : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AppPayments_AppReservationDetails_ReservationDetailsId",
                table: "AppPayments");

            migrationBuilder.DropForeignKey(
                name: "FK_AppRoomStatusLogs_AppRoomStatuses_RoomStatusesId",
                table: "AppRoomStatusLogs");

            migrationBuilder.DropIndex(
                name: "IX_AppPayments_ReservationDetailsId",
                table: "AppPayments");

            migrationBuilder.DropIndex(
                name: "IX_AppPayments_ReservationsId_ReservationDetailsId_StatusId_PaymentMethodId",
                table: "AppPayments");

            migrationBuilder.DropColumn(
                name: "ReservationDetailsId",
                table: "AppPayments");


            migrationBuilder.AlterColumn<string>(
                name: "PropertyDisplayName",
                table: "LogEntityPropertyChangeLogs",
                type: "nvarchar(128)",
                maxLength: 128,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(128)",
                oldMaxLength: 128);

            migrationBuilder.AlterColumn<string>(
                name: "OriginalValue",
                table: "LogEntityPropertyChangeLogs",
                type: "nvarchar(512)",
                maxLength: 512,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(512)",
                oldMaxLength: 512);

            migrationBuilder.AlterColumn<string>(
                name: "NewValue",
                table: "LogEntityPropertyChangeLogs",
                type: "nvarchar(512)",
                maxLength: 512,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(512)",
                oldMaxLength: 512);

            migrationBuilder.AlterColumn<string>(
                name: "EntityDisplayValue",
                table: "LogEntityChangeLogs",
                type: "nvarchar(256)",
                maxLength: 256,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(256)",
                oldMaxLength: 256);

            migrationBuilder.AlterColumn<string>(
                name: "EntityDisplayName",
                table: "LogEntityChangeLogs",
                type: "nvarchar(128)",
                maxLength: 128,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(128)",
                oldMaxLength: 128);

            migrationBuilder.AddColumn<decimal>(
                name: "Qty",
                table: "AppPaymentDetails",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "ReservationDetailsId",
                table: "AppPaymentDetails",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "PhoneNumber",
                table: "AppGuests",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(100)",
                oldMaxLength: 100);

            migrationBuilder.AlterColumn<string>(
                name: "Nationality",
                table: "AppGuests",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(100)",
                oldMaxLength: 100);

            migrationBuilder.AlterColumn<string>(
                name: "IdentityNumber",
                table: "AppGuests",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(50)",
                oldMaxLength: 50);

            migrationBuilder.AlterColumn<string>(
                name: "Email",
                table: "AppGuests",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(100)",
                oldMaxLength: 100);

            migrationBuilder.AlterColumn<string>(
                name: "CompanyName",
                table: "AppGuests",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(200)",
                oldMaxLength: 200);

            migrationBuilder.AlterColumn<string>(
                name: "Attachment",
                table: "AppGuests",
                type: "nvarchar(1000)",
                maxLength: 1000,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(1000)",
                oldMaxLength: 1000);

            migrationBuilder.AlterColumn<string>(
                name: "Description",
                table: "AppDocumentTemplates",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(500)",
                oldMaxLength: 500);

            migrationBuilder.AlterColumn<string>(
                name: "ReferenceType",
                table: "AppAttachments",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(50)",
                oldMaxLength: 50);

            migrationBuilder.AlterColumn<string>(
                name: "Description",
                table: "AppAttachments",
                type: "nvarchar(1000)",
                maxLength: 1000,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(1000)",
                oldMaxLength: 1000);

            migrationBuilder.CreateIndex(
                name: "IX_AppPayments_ReservationsId_StatusId_PaymentMethodId",
                table: "AppPayments",
                columns: new[] { "ReservationsId", "StatusId", "PaymentMethodId" });

            migrationBuilder.CreateIndex(
                name: "IX_AppPaymentDetails_ReservationDetailsId",
                table: "AppPaymentDetails",
                column: "ReservationDetailsId");

            // Update existing SourceType values to match the enum names
            migrationBuilder.Sql(@"
                UPDATE AppPaymentDetails
                SET SourceType = 'ReservationRoom'
                WHERE SourceType = 'Room' OR SourceType LIKE '%room%' OR SourceType IS NULL;

                UPDATE AppPaymentDetails
                SET SourceType = 'ReservationRoomFoodAndBeverage'
                WHERE SourceType LIKE '%food%' OR SourceType LIKE '%beverage%';

                UPDATE AppPaymentDetails
                SET SourceType = 'ReservationRoomService'
                WHERE SourceType LIKE '%service%';
            ");

            migrationBuilder.AddForeignKey(
                name: "FK_AppPaymentDetails_AppReservationDetails_ReservationDetailsId",
                table: "AppPaymentDetails",
                column: "ReservationDetailsId",
                principalTable: "AppReservationDetails",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AppPaymentDetails_AppReservationDetails_ReservationDetailsId",
                table: "AppPaymentDetails");

            migrationBuilder.DropIndex(
                name: "IX_AppPayments_ReservationsId_StatusId_PaymentMethodId",
                table: "AppPayments");

            migrationBuilder.DropIndex(
                name: "IX_AppPaymentDetails_ReservationDetailsId",
                table: "AppPaymentDetails");

            migrationBuilder.DropColumn(
                name: "Qty",
                table: "AppPaymentDetails");

            migrationBuilder.DropColumn(
                name: "ReservationDetailsId",
                table: "AppPaymentDetails");

            migrationBuilder.AlterColumn<string>(
                name: "PropertyDisplayName",
                table: "LogEntityPropertyChangeLogs",
                type: "nvarchar(128)",
                maxLength: 128,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(128)",
                oldMaxLength: 128,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "OriginalValue",
                table: "LogEntityPropertyChangeLogs",
                type: "nvarchar(512)",
                maxLength: 512,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(512)",
                oldMaxLength: 512,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "NewValue",
                table: "LogEntityPropertyChangeLogs",
                type: "nvarchar(512)",
                maxLength: 512,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(512)",
                oldMaxLength: 512,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "EntityDisplayValue",
                table: "LogEntityChangeLogs",
                type: "nvarchar(256)",
                maxLength: 256,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(256)",
                oldMaxLength: 256,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "EntityDisplayName",
                table: "LogEntityChangeLogs",
                type: "nvarchar(128)",
                maxLength: 128,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(128)",
                oldMaxLength: 128,
                oldNullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "ReservationDetailsId",
                table: "AppPayments",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AlterColumn<string>(
                name: "PhoneNumber",
                table: "AppGuests",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(100)",
                oldMaxLength: 100,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Nationality",
                table: "AppGuests",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(100)",
                oldMaxLength: 100,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "IdentityNumber",
                table: "AppGuests",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(50)",
                oldMaxLength: 50,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Email",
                table: "AppGuests",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(100)",
                oldMaxLength: 100,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CompanyName",
                table: "AppGuests",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(200)",
                oldMaxLength: 200,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Attachment",
                table: "AppGuests",
                type: "nvarchar(1000)",
                maxLength: 1000,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(1000)",
                oldMaxLength: 1000,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Description",
                table: "AppDocumentTemplates",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(500)",
                oldMaxLength: 500,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "ReferenceType",
                table: "AppAttachments",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(50)",
                oldMaxLength: 50,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Description",
                table: "AppAttachments",
                type: "nvarchar(1000)",
                maxLength: 1000,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(1000)",
                oldMaxLength: 1000,
                oldNullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_AppPayments_ReservationDetailsId",
                table: "AppPayments",
                column: "ReservationDetailsId");

            migrationBuilder.CreateIndex(
                name: "IX_AppPayments_ReservationsId_ReservationDetailsId_StatusId_PaymentMethodId",
                table: "AppPayments",
                columns: new[] { "ReservationsId", "ReservationDetailsId", "StatusId", "PaymentMethodId" });

            migrationBuilder.AddForeignKey(
                name: "FK_AppPayments_AppReservationDetails_ReservationDetailsId",
                table: "AppPayments",
                column: "ReservationDetailsId",
                principalTable: "AppReservationDetails",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_AppRoomStatusLogs_AppRoomStatuses_RoomStatusesId",
                table: "AppRoomStatusLogs",
                column: "RoomStatusesId",
                principalTable: "AppRoomStatuses",
                principalColumn: "Id");
        }
    }
}
