﻿using System;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;
using Imip.HotelFrontOffice.Permissions;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Authorization.Permissions;

namespace Imip.HotelFrontOffice.PaymentGuests;

[Authorize(WismaAppPermissions.PolicyPaymentGuest.Default)]
public class PaymentGuestsAppService : PermissionCheckedCrudAppService<
    PaymentGuest,
    PaymentGuestsDto,
    Guid,
    PagedAndSortedResultRequestDto,
    CreateUpdatePaymentGuestsDto,
    CreateUpdatePaymentGuestsDto
>, IPaymentGuestsAppService
{
    private readonly IRepository<PaymentGuest, Guid> _repository;

    public PaymentGuestsAppService(IRepository<PaymentGuest, Guid> repository,
        IPermissionChecker permissionChecker)
        : base(repository, permissionChecker)
    {
        _repository = repository;

        GetPolicyName = WismaAppPermissions.PolicyPaymentGuest.View;
        GetListPolicyName = WismaAppPermissions.PolicyPaymentGuest.View;
        CreatePolicyName = WismaAppPermissions.PolicyPaymentGuest.Create;
        UpdatePolicyName = WismaAppPermissions.PolicyPaymentGuest.Edit;
        DeletePolicyName = WismaAppPermissions.PolicyPaymentGuest.Delete;
    }
}