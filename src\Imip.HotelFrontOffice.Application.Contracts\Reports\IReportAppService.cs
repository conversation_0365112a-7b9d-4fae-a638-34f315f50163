using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.HotelFrontOffice.Reports;

public interface IReportAppService : ICrudAppService<
    ReportDto,
    Guid,
    PagedAndSortedResultRequestDto,
    CreateUpdateReportDto,
    CreateUpdateReportDto
>
{
    // Task<List<Report>> GetActiveReportsAsync();
    // Task<Report> GetByNameAsync(string name);

    Task<ReportPreviewDto> PreviewReportAsync(ReportExecutionDto input);
    Task<byte[]> ExportReportToCsvAsync(ReportExecutionDto input);
    Task<byte[]> ExportReportToExcelAsync(ReportExecutionDto input);
    Task<List<ReportParameterDto>> GetReportParametersAsync(Guid reportId);
    Task<List<ReportDto>> GetActiveReportsAsync();
}
