﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Volo.Abp.Authorization;
using Volo.Abp.DependencyInjection;

namespace Imip.HotelFrontOffice.Permissions;

[Dependency(ReplaceServices = true)]
[ExposeServices(typeof(IMethodInvocationAuthorizationService))]
public class CustomMethodInvocationAuthorizationService : IMethodInvocationAuthorizationService, ITransientDependency
{
    private readonly IAuthorizationService _authorizationService;
    private readonly ILogger<CustomMethodInvocationAuthorizationService> _logger;
    private readonly ApplicationConfigurationService _applicationConfigurationService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public CustomMethodInvocationAuthorizationService(
        IAuthorizationService authorizationService,
        ILogger<CustomMethodInvocationAuthorizationService> logger,
        ApplicationConfigurationService applicationConfigurationService,
        IHttpContextAccessor httpContextAccessor)
    {
        _authorizationService = authorizationService;
        _logger = logger;
        _applicationConfigurationService = applicationConfigurationService;
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task CheckAsync(MethodInvocationAuthorizationContext context)
    {
        if (context.Method.IsDefined(typeof(AllowAnonymousAttribute), true))
        {
            _logger.LogDebug("Method has AllowAnonymous attribute, skipping authorization");
            return;
        }

        var authorizeAttributes = GetAuthorizeAttributes(context.Method);
        if (authorizeAttributes.Count == 0)
        {
            _logger.LogDebug("Method has no Authorize attributes, skipping authorization");
            return;
        }

        _logger.LogDebug("Method has {Count} Authorize attributes", authorizeAttributes.Count);

        var authorizationPolicy = await CreateAuthorizationPolicyAsync(authorizeAttributes);

        // Get the current user from the HttpContext
        var user = _httpContextAccessor.HttpContext?.User;
        if (user == null)
        {
            _logger.LogWarning("User is null when checking authorization for method {Method}", context.Method.Name);
            throw new AbpAuthorizationException("Authorization failed! User is not authenticated.");
        }

        var authorizationResult = await _authorizationService.AuthorizeAsync(user, authorizationPolicy);

        if (!authorizationResult.Succeeded)
        {
            _logger.LogWarning("Authorization failed for method {Method}", context.Method.Name);
            throw new AbpAuthorizationException("Authorization failed! Given policy has not granted!");
        }

        _logger.LogDebug("Authorization succeeded for method {Method}", context.Method.Name);
    }

    private static List<IAuthorizeData> GetAuthorizeAttributes(MethodInfo methodInfo)
    {
        var attributes = methodInfo.GetCustomAttributes(true)
            .OfType<IAuthorizeData>()
            .ToList();

        if (attributes.Count == 0 && methodInfo.DeclaringType != null)
        {
            attributes.AddRange(methodInfo.DeclaringType.GetCustomAttributes(true).OfType<IAuthorizeData>());
        }

        return attributes;
    }

    // Helper method to get access token from HttpContext
    private async Task<string?> GetAccessTokenAsync(HttpContext httpContext)
    {
        // First try to get the token from the authentication properties
        var accessToken = await httpContext.GetTokenAsync("access_token");

        // If the token is not found in the authentication properties, try to extract it from the Authorization header
        if (string.IsNullOrEmpty(accessToken))
        {
            var authHeader = httpContext.Request.Headers.Authorization.FirstOrDefault();
            if (!string.IsNullOrEmpty(authHeader) &&
                authHeader.StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase))
            {
                accessToken = authHeader["Bearer ".Length..].Trim();
                _logger.LogDebug("Extracted access token from Authorization header");
            }
        }

        // Only log token length for security
        if (!string.IsNullOrEmpty(accessToken))
        {
            _logger.LogDebug("Retrieved access token: {TokenLength} characters", accessToken.Length);
        }

        return accessToken;
    }

    private async Task<AuthorizationPolicy> CreateAuthorizationPolicyAsync(List<IAuthorizeData> authorizeData)
    {
        var policies = new List<AuthorizationPolicy>();
        var dynamicPolicies = new List<string>();

        // Process each authorize attribute
        foreach (var data in authorizeData)
        {
            if (!string.IsNullOrWhiteSpace(data.Policy))
            {
                _logger.LogDebug("Processing policy: {Policy}", data.Policy);

                // Check if the policy is granted in the application configuration
                var httpContext = _httpContextAccessor.HttpContext;
                if (httpContext != null)
                {
                    // Get access token
                    var accessToken = await GetAccessTokenAsync(httpContext);

                    if (!string.IsNullOrEmpty(accessToken))
                    {
                        var grantedPolicies =
                            await _applicationConfigurationService.GetGrantedPoliciesAsync(accessToken);

                        // Try with the original policy name
                        if (grantedPolicies.TryGetValue(data.Policy, out var isGranted) && isGranted)
                        {
                            _logger.LogDebug("Policy {Policy} is granted in application configuration", data.Policy);
                            // No need to add a requirement, the policy is already granted
                            continue;
                        }

                        // Try with the "IdentityServer." prefix if the policy doesn't have it
                        if (!data.Policy.StartsWith("IdentityServer."))
                        {
                            var identityServerPolicyName = "IdentityServer." + data.Policy;
                            if (grantedPolicies.TryGetValue(identityServerPolicyName, out isGranted) && isGranted)
                            {
                                _logger.LogDebug("Policy {Policy} is granted as {IdentityServerPolicyName}",
                                    data.Policy, identityServerPolicyName);
                                // No need to add a requirement, the policy is already granted
                                continue;
                            }
                        }

                        _logger.LogDebug(
                            "Policy {Policy} not found in application configuration, adding to dynamic policies",
                            data.Policy);
                        dynamicPolicies.Add(data.Policy);
                        continue;
                    }
                }

                // If we couldn't check with the application configuration, add a dynamic policy requirement
                dynamicPolicies.Add(data.Policy);
            }
        }

        // Create a combined policy
        var policyBuilder = new AuthorizationPolicyBuilder();

        // Always require authenticated user
        policyBuilder.RequireAuthenticatedUser();

        // Add the policies
        foreach (var policy in policies)
        {
            policyBuilder.Combine(policy);
        }

        // Add the dynamic policies
        if (dynamicPolicies.Count > 0)
        {
            foreach (var policyName in dynamicPolicies)
            {
                policyBuilder.AddRequirements(new DynamicPolicyRequirement(policyName));
            }
        }
        else if (authorizeData.Count > 0 && policies.Count == 0)
        {
            // If we have authorize attributes but no policies were added,
            // add a default requirement to ensure the policy has at least one requirement
            _logger.LogDebug(
                "No dynamic policies were added, but authorize attributes exist. Adding default requirement.");
            policyBuilder.AddRequirements(new DynamicPolicyRequirement("Default"));
        }

        return policyBuilder.Build();
    }
}

public class DynamicPolicyRequirement(string policyName) : IAuthorizationRequirement
{
    public string PolicyName { get; } = policyName;
}