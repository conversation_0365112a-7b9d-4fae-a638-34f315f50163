using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.HotelFrontOffice.Migrations
{
    /// <inheritdoc />
    public partial class Update_Relationship : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AppReservations_MasterCompany_CompanyId1",
                table: "AppReservations");

            migrationBuilder.DropForeignKey(
                name: "FK_AppReservations_MasterDiningOptions_DiningOptionsId1",
                table: "AppReservations");

            migrationBuilder.DropIndex(
                name: "IX_AppReservations_CompanyId1",
                table: "AppReservations");

            migrationBuilder.DropIndex(
                name: "IX_AppReservations_DiningOptionsId1",
                table: "AppReservations");

            migrationBuilder.DropIndex(
                name: "IX_AppPaymentGuests_PaymentId",
                table: "AppPaymentGuests");

            migrationBuilder.DropColumn(
                name: "Status",
                table: "AppTypeFoodAndBeverage");

            migrationBuilder.DropColumn(
                name: "Status",
                table: "AppServiceTypes");

            migrationBuilder.DropColumn(
                name: "Status",
                table: "AppRoomTypes");

            migrationBuilder.DropColumn(
                name: "Status",
                table: "AppReservationTypes");

            migrationBuilder.DropColumn(
                name: "CompanyId1",
                table: "AppReservations");

            migrationBuilder.DropColumn(
                name: "DiningOptionsId1",
                table: "AppReservations");

            migrationBuilder.DropColumn(
                name: "Status",
                table: "AppGuests");

            migrationBuilder.AddColumn<string>(
                name: "Code",
                table: "MasterStatus",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Color",
                table: "MasterStatus",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "StatusId",
                table: "AppTypeFoodAndBeverage",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "StatusId",
                table: "AppServiceTypes",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "StatusId",
                table: "AppRoomTypes",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "StatusId",
                table: "AppReservationTypes",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "StatusId",
                table: "AppGuests",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_MasterStatus_DocType",
                table: "MasterStatus",
                column: "DocType");

            migrationBuilder.CreateIndex(
                name: "IX_AppTypeFoodAndBeverage_StatusId",
                table: "AppTypeFoodAndBeverage",
                column: "StatusId");

            migrationBuilder.CreateIndex(
                name: "IX_AppServiceTypes_StatusId",
                table: "AppServiceTypes",
                column: "StatusId");

            migrationBuilder.CreateIndex(
                name: "IX_AppRoomTypes_StatusId",
                table: "AppRoomTypes",
                column: "StatusId");

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationTypes_StatusId",
                table: "AppReservationTypes",
                column: "StatusId");

            migrationBuilder.CreateIndex(
                name: "IX_AppReservations_GroupCode_BookerName",
                table: "AppReservations",
                columns: new[] { "GroupCode", "BookerName" });

            migrationBuilder.CreateIndex(
                name: "IX_AppPaymentGuests_PaymentId_GuestId",
                table: "AppPaymentGuests",
                columns: new[] { "PaymentId", "GuestId" });

            migrationBuilder.CreateIndex(
                name: "IX_AppPaymentDetails_SourceId_SourceType",
                table: "AppPaymentDetails",
                columns: new[] { "SourceId", "SourceType" });

            migrationBuilder.CreateIndex(
                name: "IX_AppGuests_StatusId",
                table: "AppGuests",
                column: "StatusId");

            migrationBuilder.AddForeignKey(
                name: "FK_AppGuests_MasterStatus_StatusId",
                table: "AppGuests",
                column: "StatusId",
                principalTable: "MasterStatus",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_AppReservations_MasterCompany_CompanyId",
                table: "AppReservations",
                column: "CompanyId",
                principalTable: "MasterCompany",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_AppReservationTypes_MasterStatus_StatusId",
                table: "AppReservationTypes",
                column: "StatusId",
                principalTable: "MasterStatus",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_AppRoomTypes_MasterStatus_StatusId",
                table: "AppRoomTypes",
                column: "StatusId",
                principalTable: "MasterStatus",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_AppServiceTypes_MasterStatus_StatusId",
                table: "AppServiceTypes",
                column: "StatusId",
                principalTable: "MasterStatus",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_AppTypeFoodAndBeverage_MasterStatus_StatusId",
                table: "AppTypeFoodAndBeverage",
                column: "StatusId",
                principalTable: "MasterStatus",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AppGuests_MasterStatus_StatusId",
                table: "AppGuests");

            migrationBuilder.DropForeignKey(
                name: "FK_AppReservations_MasterCompany_CompanyId",
                table: "AppReservations");

            migrationBuilder.DropForeignKey(
                name: "FK_AppReservationTypes_MasterStatus_StatusId",
                table: "AppReservationTypes");

            migrationBuilder.DropForeignKey(
                name: "FK_AppRoomTypes_MasterStatus_StatusId",
                table: "AppRoomTypes");

            migrationBuilder.DropForeignKey(
                name: "FK_AppServiceTypes_MasterStatus_StatusId",
                table: "AppServiceTypes");

            migrationBuilder.DropForeignKey(
                name: "FK_AppTypeFoodAndBeverage_MasterStatus_StatusId",
                table: "AppTypeFoodAndBeverage");

            migrationBuilder.DropIndex(
                name: "IX_MasterStatus_DocType",
                table: "MasterStatus");

            migrationBuilder.DropIndex(
                name: "IX_AppTypeFoodAndBeverage_StatusId",
                table: "AppTypeFoodAndBeverage");

            migrationBuilder.DropIndex(
                name: "IX_AppServiceTypes_StatusId",
                table: "AppServiceTypes");

            migrationBuilder.DropIndex(
                name: "IX_AppRoomTypes_StatusId",
                table: "AppRoomTypes");

            migrationBuilder.DropIndex(
                name: "IX_AppReservationTypes_StatusId",
                table: "AppReservationTypes");

            migrationBuilder.DropIndex(
                name: "IX_AppReservations_GroupCode_BookerName",
                table: "AppReservations");

            migrationBuilder.DropIndex(
                name: "IX_AppPaymentGuests_PaymentId_GuestId",
                table: "AppPaymentGuests");

            migrationBuilder.DropIndex(
                name: "IX_AppPaymentDetails_SourceId_SourceType",
                table: "AppPaymentDetails");

            migrationBuilder.DropIndex(
                name: "IX_AppGuests_StatusId",
                table: "AppGuests");

            migrationBuilder.DropColumn(
                name: "Code",
                table: "MasterStatus");

            migrationBuilder.DropColumn(
                name: "Color",
                table: "MasterStatus");

            migrationBuilder.DropColumn(
                name: "StatusId",
                table: "AppTypeFoodAndBeverage");

            migrationBuilder.DropColumn(
                name: "StatusId",
                table: "AppServiceTypes");

            migrationBuilder.DropColumn(
                name: "StatusId",
                table: "AppRoomTypes");

            migrationBuilder.DropColumn(
                name: "StatusId",
                table: "AppReservationTypes");

            migrationBuilder.DropColumn(
                name: "StatusId",
                table: "AppGuests");

            migrationBuilder.AddColumn<short>(
                name: "Status",
                table: "AppTypeFoodAndBeverage",
                type: "smallint",
                nullable: false,
                defaultValue: (short)1);

            migrationBuilder.AddColumn<short>(
                name: "Status",
                table: "AppServiceTypes",
                type: "smallint",
                nullable: false,
                defaultValue: (short)1);

            migrationBuilder.AddColumn<short>(
                name: "Status",
                table: "AppRoomTypes",
                type: "smallint",
                nullable: false,
                defaultValue: (short)1);

            migrationBuilder.AddColumn<short>(
                name: "Status",
                table: "AppReservationTypes",
                type: "smallint",
                nullable: false,
                defaultValue: (short)1);

            migrationBuilder.AddColumn<Guid>(
                name: "CompanyId1",
                table: "AppReservations",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "DiningOptionsId1",
                table: "AppReservations",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<short>(
                name: "Status",
                table: "AppGuests",
                type: "smallint",
                nullable: false,
                defaultValue: (short)1);

            migrationBuilder.CreateIndex(
                name: "IX_AppReservations_CompanyId1",
                table: "AppReservations",
                column: "CompanyId1");

            migrationBuilder.CreateIndex(
                name: "IX_AppReservations_DiningOptionsId1",
                table: "AppReservations",
                column: "DiningOptionsId1");

            migrationBuilder.CreateIndex(
                name: "IX_AppPaymentGuests_PaymentId",
                table: "AppPaymentGuests",
                column: "PaymentId");

            migrationBuilder.AddForeignKey(
                name: "FK_AppReservations_MasterCompany_CompanyId1",
                table: "AppReservations",
                column: "CompanyId1",
                principalTable: "MasterCompany",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AppReservations_MasterDiningOptions_CompanyId",
                table: "AppReservations",
                column: "CompanyId",
                principalTable: "MasterDiningOptions",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_AppReservations_MasterDiningOptions_DiningOptionsId1",
                table: "AppReservations",
                column: "DiningOptionsId1",
                principalTable: "MasterDiningOptions",
                principalColumn: "Id");
        }
    }
}
