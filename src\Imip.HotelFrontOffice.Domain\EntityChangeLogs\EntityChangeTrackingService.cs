using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Volo.Abp.AuditLogging;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Uow;
using EntityChangeType = Imip.HotelFrontOffice.EntityChangeLogs.EntityChangeType;

namespace Imip.HotelFrontOffice.EntityChangeLogs
{
    /// <summary>
    /// Service for tracking entity changes
    /// </summary>
    public class EntityChangeTrackingService : ITransientDependency
    {
        private readonly IAuditLogRepository _auditLogRepository;
        private readonly IRepository<EntityChangeLog, Guid> _entityChangeLogRepository;
        private readonly IRepository<EntityPropertyChangeLog, Guid> _entityPropertyChangeLogRepository;
        private readonly ILogger<EntityChangeTrackingService> _logger;
        private readonly IUnitOfWorkManager _unitOfWorkManager;

        public EntityChangeTrackingService(
            IAuditLogRepository auditLogRepository,
            IRepository<EntityChangeLog, Guid> entityChangeLogRepository,
            IRepository<EntityPropertyChangeLog, Guid> entityPropertyChangeLogRepository,
            ILogger<EntityChangeTrackingService> logger,
            IUnitOfWorkManager unitOfWorkManager)
        {
            _auditLogRepository = auditLogRepository;
            _entityChangeLogRepository = entityChangeLogRepository;
            _entityPropertyChangeLogRepository = entityPropertyChangeLogRepository;
            _logger = logger;
            _unitOfWorkManager = unitOfWorkManager;
        }

        /// <summary>
        /// Processes audit logs and creates entity change logs
        /// </summary>
        /// <param name="startTime">Start time for processing audit logs</param>
        /// <param name="endTime">End time for processing audit logs</param>
        /// <returns>Number of processed audit logs</returns>
        public async Task<int> ProcessAuditLogsAsync(DateTime startTime, DateTime endTime)
        {
            try
            {
                // Get audit logs with entity changes
                var auditLogs = await _auditLogRepository.GetListAsync(
                    startTime: startTime,
                    endTime: endTime,
                    maxResultCount: 1000);

                int processedCount = 0;
                int entityChangeCount = 0;
                int propertyChangeCount = 0;

                foreach (var auditLog in auditLogs)
                {
                    if (auditLog.EntityChanges == null || auditLog.EntityChanges.Count == 0)
                    {
                        continue;
                    }

                    using var uow = _unitOfWorkManager.Begin(requiresNew: true);
                    try
                    {
                        foreach (var entityChange in auditLog.EntityChanges)
                        {
                            // Skip system tables
                            if (ShouldSkipEntityType(entityChange.EntityTypeFullName))
                            {
                                _logger.LogDebug("Skipping system entity: {EntityType}", entityChange.EntityTypeFullName);
                                continue;
                            }

                            // Check if this entity change has already been processed
                            var existingLog = await _entityChangeLogRepository.FirstOrDefaultAsync(
                                x => x.AuditLogId == auditLog.Id &&
                                     x.EntityId == entityChange.EntityId &&
                                     x.EntityTypeFullName == entityChange.EntityTypeFullName);

                            if (existingLog != null)
                            {
                                continue;
                            }

                            // Create entity change log
                            var entityChangeLog = new EntityChangeLog(
                                id: Guid.NewGuid(),
                                changeTime: entityChange.ChangeTime,
                                changeType: (EntityChangeType)entityChange.ChangeType,
                                entityId: entityChange.EntityId,
                                entityTypeFullName: entityChange.EntityTypeFullName,
                                auditLogId: auditLog.Id,
                                entityTenantId: entityChange.EntityTenantId);

                            // Set display name based on entity type
                            entityChangeLog.EntityDisplayName = GetEntityDisplayName(entityChange.EntityTypeFullName);
                            entityChangeLog.EntityDisplayValue = GetEntityDisplayValue(entityChange);

                            await _entityChangeLogRepository.InsertAsync(entityChangeLog);
                            entityChangeCount++;

                            // Create property change logs
                            if (entityChange.PropertyChanges != null && entityChange.PropertyChanges.Count > 0)
                            {
                                foreach (var propertyChange in entityChange.PropertyChanges)
                                {
                                    var propertyChangeLog = new EntityPropertyChangeLog(
                                        id: Guid.NewGuid(),
                                        entityChangeLogId: entityChangeLog.Id,
                                        propertyName: propertyChange.PropertyName,
                                        propertyTypeFullName: propertyChange.PropertyTypeFullName,
                                        originalValue: propertyChange.OriginalValue,
                                        newValue: propertyChange.NewValue,
                                        propertyDisplayName: GetPropertyDisplayName(propertyChange.PropertyName));

                                    await _entityPropertyChangeLogRepository.InsertAsync(propertyChangeLog);
                                    propertyChangeCount++;
                                }
                            }
                        }

                        await uow.CompleteAsync();
                        processedCount++;
                    }
                    catch (Exception ex)
                    {
                        await uow.RollbackAsync();
                        _logger.LogError(ex, "Error processing audit log {AuditLogId}", auditLog.Id);
                    }
                }

                return processedCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing audit logs");
                throw;
            }
        }

        /// <summary>
        /// Determines if an entity type should be skipped for tracking
        /// </summary>
        /// <param name="entityTypeFullName">Full type name of the entity</param>
        /// <returns>True if the entity should be skipped, false otherwise</returns>
        private static bool ShouldSkipEntityType(string entityTypeFullName)
        {
            if (string.IsNullOrEmpty(entityTypeFullName))
            {
                return false;
            }

            // Exclude system tables
            return entityTypeFullName.Contains("EntityChangeLog") ||
                   entityTypeFullName.Contains("EntityPropertyChangeLog") ||
                   entityTypeFullName.Contains("AuditLog") ||
                   entityTypeFullName.Contains("AuditLogAction");
        }

        /// <summary>
        /// Gets a user-friendly display name for an entity type
        /// </summary>
        /// <param name="entityTypeFullName">Full type name of the entity</param>
        /// <returns>User-friendly display name</returns>
        private static string GetEntityDisplayName(string entityTypeFullName)
        {
            if (string.IsNullOrEmpty(entityTypeFullName))
            {
                return "Unknown";
            }

            // Extract the class name from the full type name
            var parts = entityTypeFullName.Split('.');
            var className = parts.LastOrDefault() ?? entityTypeFullName;

            // Add spaces between pascal case words
            return System.Text.RegularExpressions.Regex.Replace(className, "([A-Z])", " $1").Trim();
        }

        /// <summary>
        /// Gets a user-friendly display name for a property
        /// </summary>
        /// <param name="propertyName">Name of the property</param>
        /// <returns>User-friendly display name</returns>
        private static string GetPropertyDisplayName(string propertyName)
        {
            if (string.IsNullOrEmpty(propertyName))
            {
                return "Unknown";
            }

            // Add spaces between pascal case words
            return System.Text.RegularExpressions.Regex.Replace(propertyName, "([A-Z])", " $1").Trim();
        }

        /// <summary>
        /// Gets a user-friendly display value for an entity
        /// </summary>
        /// <param name="entityChange">Entity change</param>
        /// <returns>User-friendly display value</returns>
        private static string GetEntityDisplayValue(EntityChange entityChange)
        {
            if (entityChange.PropertyChanges == null || entityChange.PropertyChanges.Count == 0)
            {
                return entityChange.EntityId;
            }

            // Try to find a property that could represent the entity (name, title, etc.)
            var nameProperty = entityChange.PropertyChanges.FirstOrDefault(p =>
                p.PropertyName.Equals("Name", StringComparison.OrdinalIgnoreCase) ||
                p.PropertyName.Equals("Title", StringComparison.OrdinalIgnoreCase) ||
                p.PropertyName.Equals("FullName", StringComparison.OrdinalIgnoreCase) ||
                p.PropertyName.Equals("DisplayName", StringComparison.OrdinalIgnoreCase));

            if (nameProperty != null)
            {
                return entityChange.ChangeType == 0 // 0 is Created in ABP's EntityChangeType enum
                    ? nameProperty.NewValue
                    : nameProperty.OriginalValue;
            }

            return entityChange.EntityId;
        }
    }
}
