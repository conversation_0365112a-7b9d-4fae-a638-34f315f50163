using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using TickerQ;
using TickerQ.EntityFrameworkCore;
using TickerQ.EntityFrameworkCore.Entities;

namespace Imip.HotelFrontOffice.Attachments.Jobs
{
    /// <summary>
    /// Service for scheduling temporary ZIP file deletion using TickerQ
    /// </summary>
    public interface ITemporaryZipFileTickerService
    {
        /// <summary>
        /// Schedules the deletion of a temporary ZIP file
        /// </summary>
        /// <param name="temporaryZipFileId">The ID of the temporary ZIP file</param>
        /// <param name="executionTime">The time when the deletion should be executed</param>
        /// <returns>A task representing the asynchronous operation</returns>
        Task ScheduleDeleteAsync(Guid temporaryZipFileId, DateTime executionTime);
    }

    /// <summary>
    /// Implementation of ITemporaryZipFileTickerService
    /// </summary>
    public class TemporaryZipFileTickerService : ITemporaryZipFileTickerService
    {
        private readonly ITimeTickerManager<TimeTicker> _timeTickerManager;
        private readonly ILogger<TemporaryZipFileTickerService> _logger;

        /// <summary>
        /// Creates a new instance of TemporaryZipFileTickerService
        /// </summary>
        public TemporaryZipFileTickerService(
            ITimeTickerManager<TimeTicker> timeTickerManager,
            ILogger<TemporaryZipFileTickerService> logger)
        {
            _timeTickerManager = timeTickerManager;
            _logger = logger;
        }

        /// <summary>
        /// Schedules the deletion of a temporary ZIP file
        /// </summary>
        public async Task ScheduleDeleteAsync(Guid temporaryZipFileId, DateTime executionTime)
        {
            try
            {
                await _timeTickerManager.AddAsync(new TimeTicker
                {
                    Function = "DeleteTemporaryZipFile",
                    ExecutionTime = executionTime,
                    Request = TickerHelper.CreateTickerRequest(temporaryZipFileId),
                    Retries = 3,
                    RetryIntervals = new[] { 60, 300, 900 }, // Retry after 1 min, 5 min, 15 min
                    Description = $"Delete temporary ZIP file {temporaryZipFileId}"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error scheduling deletion of temporary ZIP file with ID {Id}", temporaryZipFileId);
                throw;
            }
        }
    }
}
