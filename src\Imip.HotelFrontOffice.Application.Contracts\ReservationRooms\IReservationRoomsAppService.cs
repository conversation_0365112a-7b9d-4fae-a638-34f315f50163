﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace Imip.HotelFrontOffice.ReservationRooms;

public interface IReservationRoomsAppService : IApplicationService
{
    Task<ReservationRoomsDto> CreateAsync(CreateUpdateReservationRoomsDto input);

    // Add bulk operation methods
    /// <summary>
    /// Creates or updates multiple reservation food and beverage items in a single operation.
    /// If an item has a non-empty Id and the entity exists, it will be updated.
    /// If an item has a non-empty Id but the entity doesn't exist, or if the Id is empty, a new entity will be created.
    /// </summary>
    /// <param name="items">List of items to create or update</param>
    /// <returns>A sample of the created/updated entities</returns>
    Task<ReservationRoomsDto> CreateManyAsync(List<CreateUpdateReservationRoomsDto> items);
}