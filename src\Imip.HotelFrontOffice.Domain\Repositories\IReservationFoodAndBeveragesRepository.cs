﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.Repositories
{
    public interface IReservationFoodAndBeveragesRepository : IRepository<ReservationFoodAndBeverages.ReservationFoodAndBeverage, Guid>
    {
        Task<ReservationFoodAndBeverages.ReservationFoodAndBeverage?> FindByNameAsync(string name);

    }
}