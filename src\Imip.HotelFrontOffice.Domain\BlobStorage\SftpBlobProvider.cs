using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Renci.SshNet;
using Renci.SshNet.Sftp;
using Volo.Abp.BlobStoring;
using Volo.Abp.DependencyInjection;

namespace Imip.HotelFrontOffice.BlobStorage;

public class SftpBlobProvider : BlobProviderBase, ITransientDependency
{
    protected ILogger<SftpBlobProvider> Logger { get; }
    protected IConfiguration Configuration { get; }

    public SftpBlobProvider(
        ILogger<SftpBlobProvider> logger,
        IConfiguration configuration)
    {
        Logger = logger;
        Configuration = configuration;
    }

    public override async Task SaveAsync(BlobProviderSaveArgs args)
    {
        var blobPath = GetBlobPath(args);

        using var client = await CreateSftpClientAsync(args);

        try
        {
            // Ensure directory exists
            var configSection = Configuration.GetSection($"BlobStoring:{args.ContainerName}:SFTP");
            var createDirectoryIfNotExists = configSection.GetValue<bool>("CreateDirectoryIfNotExists", true);

            if (createDirectoryIfNotExists)
            {
                var directoryPath = Path.GetDirectoryName(blobPath);
                if (!string.IsNullOrEmpty(directoryPath))
                {
                    await EnsureDirectoryExistsAsync(client, directoryPath);
                }
            }

            // Upload the file
            using var stream = args.BlobStream;
            using var uploadFileStream = await client.OpenWriteAsync(blobPath);

            await stream.CopyToAsync(uploadFileStream);
            await uploadFileStream.FlushAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error saving blob to SFTP server. Path: {Path}", blobPath);
            throw;
        }
    }

    public override async Task<bool> DeleteAsync(BlobProviderDeleteArgs args)
    {
        var blobPath = GetBlobPath(args);

        using var client = await CreateSftpClientAsync(args);

        try
        {
            if (await client.ExistsAsync(blobPath))
            {
                await client.DeleteFileAsync(blobPath);
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error deleting blob from SFTP server. Path: {Path}", blobPath);
            throw;
        }
    }

    public override async Task<bool> ExistsAsync(BlobProviderExistsArgs args)
    {
        var blobPath = GetBlobPath(args);

        using var client = await CreateSftpClientAsync(args);

        try
        {
            return await client.ExistsAsync(blobPath);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error checking if blob exists on SFTP server. Path: {Path}", blobPath);
            throw;
        }
    }

    public override async Task<Stream?> GetOrNullAsync(BlobProviderGetArgs args)
    {
        var blobPath = GetBlobPath(args);

        using var client = await CreateSftpClientAsync(args);

        try
        {
            if (!await client.ExistsAsync(blobPath))
            {
                return null;
            }

            var memoryStream = new MemoryStream();
            using var downloadStream = await client.OpenReadAsync(blobPath);
            await downloadStream.CopyToAsync(memoryStream);
            memoryStream.Position = 0;

            return memoryStream;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error getting blob from SFTP server. Path: {Path}", blobPath);
            throw;
        }
    }

    private async Task<SftpClient> CreateSftpClientAsync(BlobProviderArgs args)
    {
        var containerName = args.ContainerName;
        var configSection = Configuration.GetSection($"BlobStoring:{containerName}:SFTP");

        // Read configuration from appsettings.json
        var host = configSection["Host"];
        var port = configSection.GetValue<int>("Port", 22);
        var userName = configSection["UserName"];
        var password = configSection["Password"];
        var privateKeyPath = configSection["PrivateKeyPath"];
        var privateKeyPassphrase = configSection["PrivateKeyPassphrase"];
        var connectionTimeout = configSection.GetValue<int>("ConnectionTimeout", 30000);

        if (string.IsNullOrEmpty(host))
        {
            throw new Exception($"SFTP host is not configured for container '{containerName}'");
        }

        if (string.IsNullOrEmpty(userName))
        {
            throw new Exception($"SFTP username is not configured for container '{containerName}'");
        }

        ConnectionInfo connectionInfo;

        // Create connection info based on authentication method
        if (!string.IsNullOrEmpty(privateKeyPath))
        {
            var privateKeyFile = new PrivateKeyFile(
                privateKeyPath,
                !string.IsNullOrEmpty(privateKeyPassphrase) ? privateKeyPassphrase : null
            );

            connectionInfo = new ConnectionInfo(
                host,
                port,
                userName,
                new PrivateKeyAuthenticationMethod(userName, privateKeyFile)
            );
        }
        else
        {
            if (string.IsNullOrEmpty(password))
            {
                throw new Exception($"SFTP password is not configured for container '{containerName}' and no private key is provided");
            }

            connectionInfo = new ConnectionInfo(
                host,
                port,
                userName,
                new PasswordAuthenticationMethod(userName, password)
            );
        }

        // Set timeouts
        connectionInfo.Timeout = TimeSpan.FromMilliseconds(connectionTimeout);

        var client = new SftpClient(connectionInfo);

        try
        {
            await Task.Run(() => client.Connect());
            return client;
        }
        catch (Exception ex)
        {
            client.Dispose();
            Logger.LogError(ex, "Error connecting to SFTP server. Host: {Host}, Port: {Port}", host, port);
            throw;
        }
    }

    private string GetBlobPath(BlobProviderArgs args)
    {
        var blobName = args.BlobName;
        var containerName = args.ContainerName;

        // Read base directory from configuration
        var configSection = Configuration.GetSection($"BlobStoring:{containerName}:SFTP");
        var baseDirectory = configSection["BaseDirectory"] ?? "/";

        var basePath = baseDirectory.TrimEnd('/');
        var containerPath = $"{basePath}/{containerName}";

        return $"{containerPath}/{blobName}";
    }

    private async Task EnsureDirectoryExistsAsync(SftpClient client, string directoryPath)
    {
        if (string.IsNullOrEmpty(directoryPath) || directoryPath == "/")
        {
            return;
        }

        directoryPath = directoryPath.Replace('\\', '/');

        if (await client.ExistsAsync(directoryPath))
        {
            return;
        }

        // Create parent directories recursively
        var parentPath = Path.GetDirectoryName(directoryPath);
        if (!string.IsNullOrEmpty(parentPath))
        {
            await EnsureDirectoryExistsAsync(client, parentPath);
        }

        // Create this directory
        await Task.Run(() => client.CreateDirectory(directoryPath));
    }
}