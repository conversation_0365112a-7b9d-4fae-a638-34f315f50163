﻿using System;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Permissions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.DiningOptions;

[Route("api/master/dining-options")]
[Authorize(WismaAppPermissions.PolicyDiningOptions.Default)]
public class DiningOptionsAppService : CrudAppService<
    HotelFrontOffice.DiningOptions.DiningOption,
    DiningOptionsDto,
    Guid,
    PagedAndSortedResultRequestDto,
    CreateUpdateDiningOptionsDto,
    CreateUpdateDiningOptionsDto
>
{
    public DiningOptionsAppService(IRepository<HotelFrontOffice.DiningOptions.DiningOption, Guid> repository) : base(repository)
    {
    }

    [HttpGet]
    [Authorize(WismaAppPermissions.PolicyDiningOptions.View)]
    public override Task<PagedResultDto<DiningOptionsDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        return base.GetListAsync(input);
    }

    [HttpGet("{id}")]
    [Authorize(WismaAppPermissions.PolicyDiningOptions.View)]
    public override Task<DiningOptionsDto> GetAsync(Guid id)
    {
        return base.GetAsync(id);
    }

    [HttpPost]
    [Authorize(WismaAppPermissions.PolicyDiningOptions.Create)]
    public override Task<DiningOptionsDto> CreateAsync(CreateUpdateDiningOptionsDto input)
    {
        return base.CreateAsync(input);
    }

    [HttpPut("{id}")]
    [Authorize(WismaAppPermissions.PolicyDiningOptions.Edit)]
    public override Task<DiningOptionsDto> UpdateAsync(Guid id, CreateUpdateDiningOptionsDto input)
    {
        return base.UpdateAsync(id, input);
    }

    [HttpDelete("{id}")]
    [Authorize(WismaAppPermissions.PolicyDiningOptions.Delete)]
    public override Task DeleteAsync(Guid id)
    {
        return base.DeleteAsync(id);
    }
}