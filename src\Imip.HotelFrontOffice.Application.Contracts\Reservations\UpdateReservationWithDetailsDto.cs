﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Imip.HotelFrontOffice.Companies;
using Imip.HotelFrontOffice.MasterStatuses;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.Reservations;

public class UpdateReservationWithDetailsDto : AuditedEntityDto<Guid>, IValidatableObject
{
    [Required]
    [StringLength(128)] public string ReservationCode { get; set; } = default!;

    public string GroupCode { get; set; } = default!;

    [Required]
    [StringLength(128)]
    public string BookerName { get; set; } = default!;

    public string BookerIdentityNumber { get; set; } = default!;
    public string BookerPhoneNumber { get; set; } = default!;
    public string? BookerEmail { get; set; }

    [Required]
    public DateTime ArrivalDate { get; set; }

    [Range(1, 365)]
    public int Days { get; set; }

    public string Attachment { get; set; } = default!;
    public Guid? CompanyId { get; set; }

    [Required]
    public Guid StatusId { get; set; }

    public Guid? PaymentMethodId { get; set; }
    public Guid? DiningOptionsId { get; set; }

    [Required]
    public Guid ReservationTypeId { get; set; }

    // Concurrency stamp for optimistic concurrency control
    public string? ConcurrencyStamp { get; set; }

    public List<UpdateReservationDetailDto> ReservationDetail { get; set; } = new();

    // Alternative property name to match the client payload
    public List<UpdateReservationDetailDto>? ReservationDetails { get; set; }

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        // Get repositories from validation context
        var companyRepository =
            validationContext.GetService(typeof(IRepository<Company, Guid>)) as IRepository<Company, Guid>;
        var statusRepository =
            validationContext.GetService(typeof(IRepository<MasterStatus, Guid>)) as IRepository<MasterStatus, Guid>;
        var reservationTypeRepository =
            validationContext.GetService(typeof(IRepository<ReservationTypes.ReservationType, Guid>)) as IRepository<ReservationTypes.ReservationType, Guid>;

        // Validate CompanyId if provided
        if (CompanyId.HasValue && CompanyId.Value != Guid.Empty && companyRepository != null)
        {
            if (!companyRepository.AnyAsync(c => c.Id == CompanyId.Value).Result)
            {
                yield return new ValidationResult(
                    $"Company with ID {CompanyId.Value} does not exist.",
                    new[] { nameof(CompanyId) }
                );
            }
        }

        // Validate StatusId
        if (StatusId != Guid.Empty && statusRepository != null)
        {
            if (!statusRepository.AnyAsync(s => s.Id == StatusId).Result)
            {
                yield return new ValidationResult(
                    $"Status with ID {StatusId} does not exist.",
                    new[] { nameof(StatusId) }
                );
            }
        }

        // Validate ReservationTypeId
        if (ReservationTypeId != Guid.Empty && reservationTypeRepository != null)
        {
            if (!reservationTypeRepository.AnyAsync(rt => rt.Id == ReservationTypeId).Result)
            {
                yield return new ValidationResult(
                    $"Reservation Type with ID {ReservationTypeId} does not exist.",
                    new[] { nameof(ReservationTypeId) }
                );
            }
        }

        // Validate ArrivalDate is not in the past
        // if (ArrivalDate.Date < DateTime.Now.Date)
        // {
        //     yield return new ValidationResult(
        //         "Arrival date cannot be in the past.",
        //         new[] { nameof(ArrivalDate) }
        //     );
        // }
    }
}

public class UpdateReservationDetailDto : AuditedEntityDto<Guid>, IValidatableObject
{
    public string Rfid { get; set; } = default!;
    public decimal? Price { get; set; }

    [Required]
    public DateTime? CheckInDate { get; set; }

    [Required]
    public DateTime? CheckOutDate { get; set; }

    [Required]
    public Guid RoomId { get; set; }

    [Required]
    public Guid GuestId { get; set; }

    [Required]
    public Guid? StatusId { get; set; }

    public Guid? CriteriaId { get; set; }

    // Concurrency stamp for optimistic concurrency control
    public string? ConcurrencyStamp { get; set; }

    public UpdateReservationGuestDto? Guest { get; set; }

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        // Get repositories from validation context
        var reservationDetailsRepository =
            validationContext.GetService(typeof(IRepository<ReservationDetails.ReservationDetail, Guid>)) as IRepository<ReservationDetails.ReservationDetail, Guid>;
        // var reservationDetailsRepository =
        //     (IRepository<Entities.ReservationDetails, Guid>)validationContext.GetService(
        //         typeof(IRepository<Entities.ReservationDetails, Guid>));
        var roomRepository =
            validationContext.GetService(typeof(IRepository<Rooms.Room, Guid>)) as IRepository<Rooms.Room, Guid>;
        var statusRepository =
            validationContext.GetService(typeof(IRepository<MasterStatus, Guid>)) as IRepository<MasterStatus, Guid>;
        var guestRepository =
            validationContext.GetService(typeof(IRepository<Guests.Guest, Guid>)) as IRepository<Guests.Guest, Guid>;

        // Check if the detail exists
        if (reservationDetailsRepository != null)
        {
            if (!reservationDetailsRepository.AnyAsync(rd => rd.Id == Id).Result)
            {
                yield return new ValidationResult(
                    $"Reservation detail with ID {Id} does not exist.",
                    new[] { nameof(Id) }
                );
            }
        }

        // Validate RoomId
        if (RoomId != Guid.Empty && roomRepository != null)
        {
            if (!roomRepository.AnyAsync(r => r.Id == RoomId).Result)
            {
                yield return new ValidationResult(
                    $"Room with ID {RoomId} does not exist.",
                    new[] { nameof(RoomId) }
                );
            }
        }

        // Validate StatusId
        if (StatusId != Guid.Empty && statusRepository != null)
        {
            if (!statusRepository.AnyAsync(s => s.Id == StatusId).Result)
            {
                yield return new ValidationResult(
                    $"Status with ID {StatusId} does not exist.",
                    new[] { nameof(StatusId) }
                );
            }
        }

        // Validate GuestId
        if (GuestId != Guid.Empty && guestRepository != null)
        {
            if (!guestRepository.AnyAsync(g => g.Id == GuestId).Result)
            {
                yield return new ValidationResult(
                    $"Guest with ID {GuestId} does not exist.",
                    new[] { nameof(GuestId) }
                );
            }
        }

        // Validate CheckIn and CheckOut dates
        if (CheckInDate.HasValue && CheckOutDate.HasValue)
        {
            if (CheckOutDate.Value < CheckInDate.Value)
            {
                yield return new ValidationResult(
                    "Check-out date cannot be earlier than check-in date.",
                    new[] { nameof(CheckOutDate) }
                );
            }
        }
    }
}

public class UpdateReservationGuestDto : AuditedEntityDto<Guid>
{
    [Required]
    [StringLength(200)]
    public string Fullname { get; set; } = default!;

    [Required]
    [StringLength(50)]
    public string IdentityNumber { get; set; } = default!;

    [StringLength(100)]
    public string PhoneNumber { get; set; } = default!;

    [EmailAddress]
    [StringLength(100)]
    public string Email { get; set; } = default!;

    [StringLength(100)]
    public string Nationality { get; set; } = default!;

    [StringLength(200)]
    public string CompanyName { get; set; } = default!;

    public string Attachment { get; set; } = string.Empty;

    public string? ConcurrencyStamp { get; set; }
}