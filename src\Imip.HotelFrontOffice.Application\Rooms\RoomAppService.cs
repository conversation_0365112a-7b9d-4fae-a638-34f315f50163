﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Permissions;
using Imip.HotelFrontOffice.RoomStatuses;
using Imip.HotelFrontOffice.RoomTypes;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Data;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.Rooms;

[Authorize(WismaAppPermissions.PolicyRoom.Default)]
public class RoomAppService : PermissionCheckedCrudAppService<
    Room,
    RoomDto,
    Guid,
    PagedAndSortedResultRequestDto,
    CreateUpdateRoomDto,
    CreateUpdateRoomDto
>, IRoomAppService
{
    private readonly IRepository<RoomStatus, Guid> _roomStatusesRepository;
    private readonly IRepository<RoomType, Guid> _roomTypesRepository;
    private readonly ILogger<RoomAppService> _logger;

    public RoomAppService(
        IRepository<Room, Guid> repository,
        IRepository<RoomStatus, Guid> roomStatusesRepository,
        IRepository<RoomType, Guid> roomTypesRepository,
        ILogger<RoomAppService> logger,
        IPermissionChecker permissionChecker)
        : base(repository, permissionChecker)
    {
        _roomStatusesRepository = roomStatusesRepository;
        _roomTypesRepository = roomTypesRepository;
        _logger = logger;

        GetPolicyName = WismaAppPermissions.PolicyRoom.View;
        GetListPolicyName = WismaAppPermissions.PolicyRoom.View;
        CreatePolicyName = WismaAppPermissions.PolicyRoom.Create;
        UpdatePolicyName = WismaAppPermissions.PolicyRoom.Edit;
        DeletePolicyName = WismaAppPermissions.PolicyRoom.Delete;
    }

    protected override async Task<IQueryable<Room>> CreateFilteredQueryAsync(
        PagedAndSortedResultRequestDto input)
    {
        return (await base.CreateFilteredQueryAsync(input))
            .AsNoTracking() // Disable change tracking for better performance
            .Include(x => x.RoomType)
                .ThenInclude(rt => rt.Status) // Include nested related entities
            .Include(x => x.RoomStatus);
    }

    public override async Task<RoomDto> CreateAsync(CreateUpdateRoomDto input)
    {
        await CheckCreatePolicyAsync();

        // Validate that RoomStatusId exists
        if (input.RoomStatusId != Guid.Empty)
        {
            var roomStatus = await _roomStatusesRepository.FindAsync(input.RoomStatusId);
            if (roomStatus == null)
            {
                throw new BusinessException("RoomStatus:NotFound",
                        $"Room Status with id {input.RoomStatusId} does not exist.")
                    .WithData("id", input.RoomStatusId);
            }
        }

        // Validate that RoomTypeId exists
        if (input.RoomTypeId != Guid.Empty)
        {
            var roomType = await _roomTypesRepository.FindAsync(input.RoomTypeId);
            if (roomType == null)
            {
                throw new BusinessException("RoomType:NotFound",
                        $"Room Type with id {input.RoomTypeId} does not exist.")
                    .WithData("id", input.RoomTypeId);
            }
        }

        var entity = await MapToEntityAsync(input);

        // Set ExtraProperties for foreign names if provided in the input
        if (input.ExtraProperties != null)
        {
            foreach (var property in input.ExtraProperties)
            {
                entity.SetProperty(property.Key, property.Value);
            }
        }
        else
        {
            // Set default foreign names if not provided
            entity.SetProperty("ForeignName1", string.Empty);
            entity.SetProperty("ForeignName2", string.Empty);
        }

        await Repository.InsertAsync(entity);

        return await MapToGetOutputDtoAsync(entity);
    }

    public override async Task<RoomDto> GetAsync(Guid id)
    {
        // This will call our custom CheckGetPolicyAsync implementation
        await CheckGetPolicyAsync();

        var query = await Repository.GetQueryableAsync();
        var room = await query
            .AsNoTracking() // Disable change tracking for better performance
            .Include(x => x.RoomType)
                .ThenInclude(rt => rt.Status) // Include nested related entities
            .Include(x => x.RoomStatus)
            .FirstOrDefaultAsync(x => x.Id == id);

        if (room is null)
        {
            throw new EntityNotFoundException(typeof(Room), id);
        }

        // If the navigation properties are null but we have the IDs, load them separately
        if (room.RoomType == null && room.RoomTypeId != Guid.Empty)
        {
            room.RoomType = await _roomTypesRepository.GetAsync(room.RoomTypeId);
        }

        if (room.RoomStatus == null && room.RoomStatusId != Guid.Empty)
        {
            room.RoomStatus = await _roomStatusesRepository.GetAsync(room.RoomStatusId);
        }

        return ObjectMapper.Map<Room, RoomDto>(room);
    }

    public override async Task<RoomDto> UpdateAsync(Guid id, CreateUpdateRoomDto input)
    {
        await CheckUpdatePolicyAsync();

        // Validate that RoomStatusId exists
        if (input.RoomStatusId != Guid.Empty)
        {
            var roomStatus = await _roomStatusesRepository.FindAsync(input.RoomStatusId);
            if (roomStatus == null)
            {
                throw new BusinessException("RoomStatus:NotFound",
                        $"Room Status with id {input.RoomStatusId} does not exist.")
                    .WithData("id", input.RoomStatusId);
            }
        }

        // Validate that RoomTypeId exists
        if (input.RoomTypeId != Guid.Empty)
        {
            var roomType = await _roomTypesRepository.FindAsync(input.RoomTypeId);
            if (roomType == null)
            {
                throw new BusinessException("RoomType:NotFound",
                        $"Room Type with id {input.RoomTypeId} does not exist.")
                    .WithData("id", input.RoomTypeId);
            }
        }

        var entity = await Repository.GetAsync(id);

        // Map properties from input to entity
        entity.RoomNumber = input.RoomNumber;
        entity.RoomCode = input.RoomCode;
        entity.Size = input.Size;
        entity.Information = input.Information;
        entity.RoomStatusId = input.RoomStatusId;
        entity.RoomTypeId = input.RoomTypeId;
        entity.Price = input.Price;

        // Update ExtraProperties for foreign names if provided in the input
        if (input.ExtraProperties != null)
        {
            foreach (var property in input.ExtraProperties)
            {
                entity.SetProperty(property.Key, property.Value);
            }
        }

        await Repository.UpdateAsync(entity);

        // Reload the entity with related entities to ensure they're properly loaded
        var updatedRoom = await Repository.GetAsync(id, includeDetails: true);

        // If the navigation properties are null but we have the IDs, load them separately
        if (updatedRoom.RoomType == null && updatedRoom.RoomTypeId != Guid.Empty)
        {
            updatedRoom.RoomType = await _roomTypesRepository.GetAsync(updatedRoom.RoomTypeId);
        }

        if (updatedRoom.RoomStatus == null && updatedRoom.RoomStatusId != Guid.Empty)
        {
            updatedRoom.RoomStatus = await _roomStatusesRepository.GetAsync(updatedRoom.RoomStatusId);
        }

        return ObjectMapper.Map<Room, RoomDto>(updatedRoom);
    }
}