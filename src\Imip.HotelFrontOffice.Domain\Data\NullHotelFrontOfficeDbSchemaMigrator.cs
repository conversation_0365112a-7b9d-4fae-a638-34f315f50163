﻿using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace Imip.HotelFrontOffice.Data;

/* This is used if database provider does't define
 * IHotelFrontOfficeDbSchemaMigrator implementation.
 */
public class NullHotelFrontOfficeDbSchemaMigrator : IHotelFrontOfficeDbSchemaMigrator, ITransientDependency
{
    public Task MigrateAsync()
    {
        return Task.CompletedTask;
    }
}
