﻿using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Http;

namespace Imip.HotelFrontOffice.Documents;

/// <summary>
/// DTO for uploading a document template with a file
/// </summary>
public class UploadDocumentTemplateDto
{
    [Required]
    [StringLength(100)]
    public required string Name { get; set; }
    
    [Required]
    public DocumentType DocumentType { get; set; }
    
    [StringLength(500)]
    public string? Description { get; set; }
    
    public bool IsDefault { get; set; }
    
    /// <summary>
    /// The template file (must be a DOCX file)
    /// </summary>
    [Required]
    public IFormFile File { get; set; }

}