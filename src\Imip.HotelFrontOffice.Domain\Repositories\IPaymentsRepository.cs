﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.Repositories
{
    public interface IPaymentsRepository : IRepository<Payments.Payment, Guid>
    {
        Task<Payments.Payment?> FindByNameAsync(string name);

        Task<List<Payments.Payment>> GetActiveAsync();
    }
}