{"id": "4a7ed82c-bd59-4ba6-b92a-e258dd492574", "template": "app", "versions": {"AbpFramework": "9.0.4", "AbpStudio": "0.9.23", "TargetDotnetFramework": "net9.0"}, "modules": {"Imip.HotelFrontOffice": {"path": "Imip.HotelFrontOffice.abpmdl"}}, "runProfiles": {"Default": {"path": "etc/abp-studio/run-profiles/Default.abprun.json"}}, "options": {"httpRequests": {"ignoredUrls": ["^/metrics$"]}}, "creatingStudioConfiguration": {"template": "app", "createdAbpStudioVersion": "0.9.23", "tiered": "false", "runInstallLibs": "true", "useLocalReferences": "false", "multiTenancy": "true", "includeTests": "true", "kubernetesConfiguration": "false", "uiFramework": "mvc", "mobileFramework": "none", "distributedEventBus": "none", "databaseProvider": "ef", "runDbMigrator": "true", "databaseManagementSystem": "sqlserver", "separateTenantSchema": "false", "createInitialMigration": "true", "theme": "leptonx-lite", "themeStyle": "", "publicWebsite": "false", "socialLogin": ""}}