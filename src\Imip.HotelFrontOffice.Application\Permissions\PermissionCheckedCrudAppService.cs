using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.Permissions;

/// <summary>
/// Base class for CRUD app services that check permissions using the Identity Server
/// </summary>
public abstract class PermissionCheckedCrudAppService<TEntity, TEntityDto, TKey, TGetListInput, TCreateInput,
    TUpdateInput> :
    CrudAppService<TEntity, TEntityDto, TKey, TGetListInput, TCreateInput, TUpdateInput>
    where TEntity : class, IEntity<TKey>
    where TEntityDto : IEntityDto<TKey>
    where TGetListInput : PagedAndSortedResultRequestDto
{
    protected readonly IPermissionChecker PermissionChecker;

    protected PermissionCheckedCrudAppService(
        IRepository<TEntity, TK<PERSON>> repository,
        IPermissionChecker permissionChecker)
        : base(repository)
    {
        PermissionChecker = permissionChecker;
    }

    /// <summary>
    /// Override to check permissions against Identity Server
    /// </summary>
    protected override async Task CheckGetListPolicyAsync()
    {
        if (string.IsNullOrEmpty(GetListPolicyName))
        {
            return;
        }

        var isGranted = await PermissionChecker.IsGrantedAsync(GetListPolicyName);
        if (!isGranted)
        {
            throw new Volo.Abp.Authorization.AbpAuthorizationException(
                $"Authorization failed! Given policy has not granted: {GetListPolicyName}");
        }
    }

    /// <summary>
    /// Override to check permissions against Identity Server
    /// </summary>
    protected override async Task CheckGetPolicyAsync()
    {
        if (string.IsNullOrEmpty(GetPolicyName))
        {
            return;
        }

        var isGranted = await PermissionChecker.IsGrantedAsync(GetPolicyName);
        if (!isGranted)
        {
            throw new Volo.Abp.Authorization.AbpAuthorizationException(
                $"Authorization failed! Given policy has not granted: {GetPolicyName}");
        }
    }

    /// <summary>
    /// Override to check permissions against Identity Server
    /// </summary>
    protected override async Task CheckCreatePolicyAsync()
    {
        if (string.IsNullOrEmpty(CreatePolicyName))
        {
            return;
        }

        var isGranted = await PermissionChecker.IsGrantedAsync(CreatePolicyName);
        if (!isGranted)
        {
            throw new Volo.Abp.Authorization.AbpAuthorizationException(
                $"Authorization failed! Given policy has not granted: {CreatePolicyName}");
        }
    }

    /// <summary>
    /// Override to check permissions against Identity Server
    /// </summary>
    protected override async Task CheckUpdatePolicyAsync()
    {
        if (string.IsNullOrEmpty(UpdatePolicyName))
        {
            return;
        }

        var isGranted = await PermissionChecker.IsGrantedAsync(UpdatePolicyName);
        if (!isGranted)
        {
            throw new Volo.Abp.Authorization.AbpAuthorizationException(
                $"Authorization failed! Given policy has not granted: {UpdatePolicyName}");
        }
    }

    /// <summary>
    /// Override to check permissions against Identity Server
    /// </summary>
    protected override async Task CheckDeletePolicyAsync()
    {
        if (string.IsNullOrEmpty(DeletePolicyName))
        {
            return;
        }

        var isGranted = await PermissionChecker.IsGrantedAsync(DeletePolicyName);
        if (!isGranted)
        {
            throw new Volo.Abp.Authorization.AbpAuthorizationException(
                $"Authorization failed! Given policy has not granted: {DeletePolicyName}");
        }
    }
}