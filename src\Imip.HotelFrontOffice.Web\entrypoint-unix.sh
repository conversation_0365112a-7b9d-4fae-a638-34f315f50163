#!/bin/bash
set -e

# Print script information for debugging
echo "Starting entrypoint script at $(date)"
echo "Current directory: $(pwd)"
echo "Script location: $0"

# Set environment
echo "Setting ASPNETCORE_ENVIRONMENT to ${ASPNETCORE_ENVIRONMENT:-Production}"
export ASPNETCORE_ENVIRONMENT=${ASPNETCORE_ENVIRONMENT:-Production}

# Set up SkiaSharp libraries
if [ -f "/app/setup-skiasharp.sh" ]; then
    echo "Running SkiaSharp setup script..."
    bash /app/setup-skiasharp.sh
else
    echo "WARNING: SkiaSharp setup script not found"
fi

# Verify SkiaSharp installation
if [ -f "/app/verify-skiasharp.sh" ]; then
    echo "Running SkiaSharp verification script..."
    bash /app/verify-skiasharp.sh
else
    echo "WARNING: SkiaSharp verification script not found"
fi

# Additional SkiaSharp troubleshooting
echo "Checking for SkiaSharp native libraries..."
find /app -name "libSkiaSharp.so" -type f || echo "No libSkiaSharp.so found in /app"
find /usr/lib -name "libSkiaSharp.so" -type f || echo "No libSkiaSharp.so found in /usr/lib"
find /usr/lib/x86_64-linux-gnu -name "libSkiaSharp.so" -type f || echo "No libSkiaSharp.so found in /usr/lib/x86_64-linux-gnu"

# Check for fontconfig
echo "Checking for fontconfig..."
find / -name "libfontconfig.so.1" -type f 2>/dev/null || echo "No libfontconfig.so.1 found"

# Force copy of SkiaSharp libraries if they exist in the runtimes directory
if [ -d "/app/runtimes/linux-x64/native" ]; then
    echo "Force copying SkiaSharp libraries from runtimes directory..."
    cp -vf /app/runtimes/linux-x64/native/libSkiaSharp.so /usr/lib/ || echo "Failed to copy libSkiaSharp.so to /usr/lib/"
    cp -vf /app/runtimes/linux-x64/native/libSkiaSharp.so /usr/lib/x86_64-linux-gnu/ || echo "Failed to copy libSkiaSharp.so to /usr/lib/x86_64-linux-gnu/"
    cp -vf /app/runtimes/linux-x64/native/libSkiaSharp.so /lib/ || echo "Failed to copy libSkiaSharp.so to /lib/"

    # Create symbolic links
    ln -sf /app/runtimes/linux-x64/native/libSkiaSharp.so /usr/lib/libSkiaSharp.so || echo "Failed to create symlink in /usr/lib/"
    ln -sf /app/runtimes/linux-x64/native/libSkiaSharp.so /usr/lib/x86_64-linux-gnu/libSkiaSharp.so || echo "Failed to create symlink in /usr/lib/x86_64-linux-gnu/"
    ln -sf /app/runtimes/linux-x64/native/libSkiaSharp.so /lib/libSkiaSharp.so || echo "Failed to create symlink in /lib/"

    # Update library cache
    ldconfig || echo "Failed to run ldconfig"
else
    echo "Runtimes directory not found, skipping force copy"
fi

# Update library cache
echo "Updating library cache with ldconfig..."
ldconfig

# Check for certificates
if [ -d "/app/certs" ]; then
    echo "Certificate directory contents:"
    ls -la /app/certs

    if [ -f "/app/certs/identity-server.pfx" ]; then
        echo "Found certificate at /app/certs/identity-server.pfx"
    else
        echo "WARNING: Certificate not found at /app/certs/identity-server.pfx"
        echo "This will cause the application to fail"
    fi
else
    echo "WARNING: Certificate directory /app/certs does not exist"
    echo "This will cause the application to fail"
fi

# Ensure data protection keys directory exists and has proper permissions
echo "Setting up data protection keys directory..."
mkdir -p /app/data-protection-keys
chmod -R 777 /app/data-protection-keys
echo "Data protection keys directory contents:"
ls -la /app/data-protection-keys

# Print environment for debugging
echo "Current environment: $ASPNETCORE_ENVIRONMENT"
echo "Current directory: $(pwd)"
echo "Directory listing: $(ls -la)"
echo "LD_LIBRARY_PATH: $LD_LIBRARY_PATH"

# Print Redis configuration
echo "Redis configuration:"
echo "Redis:IsEnabled=${Redis__IsEnabled:-true}"
echo "Redis:Configuration=${Redis__Configuration:-not set}"

# Print hostname and node information
echo "Pod hostname: $(hostname)"
echo "Node name: ${NODE_NAME:-unknown}"

# Check if the application DLL exists
if [ -f "Imip.HotelFrontOffice.Web.dll" ]; then
    echo "Found application DLL at $(pwd)/Imip.HotelFrontOffice.Web.dll"
else
    echo "ERROR: Application DLL not found at $(pwd)/Imip.HotelFrontOffice.Web.dll"
    echo "Directory contents:"
    ls -la
    echo "Searching for DLL in /app:"
    find /app -name "*.dll" | sort
fi

# Start the application
echo "Starting application..."
exec dotnet Imip.HotelFrontOffice.Web.dll
