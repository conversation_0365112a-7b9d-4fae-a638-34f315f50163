using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace Imip.HotelFrontOffice.Services;

public class ApplicationConfigurationService
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ILogger<ApplicationConfigurationService> _logger;

    public ApplicationConfigurationService(
        IHttpClientFactory httpClientFactory,
        ILogger<ApplicationConfigurationService> logger)
    {
        _httpClientFactory = httpClientFactory;
        _logger = logger;
    }

    public async Task<Dictionary<string, bool>> GetGrantedPoliciesAsync(string accessToken)
    {
        try
        {
            var client = _httpClientFactory.CreateClient("IdentityServer");
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

            var response = await client.GetAsync("/api/abp/application-configuration");

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning("Failed to get application configuration: {StatusCode}", response.StatusCode);
                return new Dictionary<string, bool>();
            }

            var content = await response.Content.ReadAsStringAsync();
            var appConfig = JsonDocument.Parse(content);

            // Check if the auth element exists
            if (!appConfig.RootElement.TryGetProperty("auth", out var authElement))
            {
                _logger.LogWarning("Auth element not found in application configuration");
                return new Dictionary<string, bool>();
            }

            // Check if the grantedPolicies element exists
            if (!authElement.TryGetProperty("grantedPolicies", out var grantedPoliciesElement))
            {
                _logger.LogWarning("GrantedPolicies element not found in auth element");
                return new Dictionary<string, bool>();
            }

            // Convert the grantedPolicies element to a dictionary
            var grantedPolicies = new Dictionary<string, bool>();
            foreach (var property in grantedPoliciesElement.EnumerateObject())
            {
                grantedPolicies[property.Name] = property.Value.GetBoolean();
            }

            return grantedPolicies;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting granted policies from Identity Server");
            return new Dictionary<string, bool>();
        }
    }
}
