using System;
using System.Net;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Imip.HotelFrontOffice.Web.Middleware;

public class UnsupportedMediaTypeMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<UnsupportedMediaTypeMiddleware> _logger;

    public UnsupportedMediaTypeMiddleware(RequestDelegate next, ILogger<UnsupportedMediaTypeMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            // Log headers for debugging
            foreach (var header in context.Request.Headers)
            {
                _logger.LogDebug("Header: {Key} = {Value}", header.Key, header.Value);
            }

            // Check if this is a file upload request
            bool isFileUploadRequest = IsFileUploadRequest(context);
            if (isFileUploadRequest)
            {
                // Check if the content type is missing but should be multipart/form-data
                if (string.IsNullOrEmpty(context.Request.ContentType) &&
                    context.Request.Path.Value?.Contains("/reservations") == true &&
                    context.Request.Method == "POST")
                {
                    _logger.LogWarning("Missing Content-Type for reservation creation. This should be multipart/form-data for file uploads.");
                }
            }

            await _next(context);

            // If we get a 415 status code, provide a proper JSON response
            if (context.Response.StatusCode == (int)HttpStatusCode.UnsupportedMediaType)
            {
                // If the response hasn't been started yet, we can modify it
                if (!context.Response.HasStarted)
                {
                    _logger.LogWarning("Handling 415 Unsupported Media Type response for path: {Path}, Method: {Method}, ContentType: {ContentType}",
                        context.Request.Path,
                        context.Request.Method,
                        context.Request.ContentType ?? "null");

                    // Create a proper error response following the required format
                    var errorResponse = new
                    {
                        error = new
                        {
                            code = "UnsupportedMediaType",
                            message = "The file type is not supported. Only PDF and image files are allowed.",
                            details = "For file uploads, ensure you're using multipart/form-data content type.",
                            data = new
                            {
                                statusCode = 415,
                                errorType = "FileValidationError",
                                allowedTypes = "PDF and image files (JPEG, PNG, GIF, etc.)",
                                contentType = context.Request.ContentType ?? "null"
                            },
                            validationErrors = new[]
                            {
                                new
                                {
                                    message = "Invalid content type for file upload",
                                    members = new[] { "AttachmentFile" }
                                }
                            }
                        }
                    };

                    // Set the response content type to JSON
                    context.Response.ContentType = "application/json";

                    // Write the JSON response
                    var jsonResponse = JsonSerializer.Serialize(errorResponse);
                    await context.Response.WriteAsync(jsonResponse);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in UnsupportedMediaTypeMiddleware");
            throw;
        }
    }

    private bool IsFileUploadRequest(HttpContext context)
    {
        // Check if the request is a POST or PUT
        if (context.Request.Method != "POST" && context.Request.Method != "PUT")
        {
            return false;
        }

        // Check if the content type indicates a file upload
        var contentType = context.Request.ContentType?.ToLower();
        if (contentType == null)
        {
            return false;
        }

        // Check for multipart/form-data
        if (contentType.Contains("multipart/form-data"))
        {
            return true;
        }

        // Check if the path contains keywords that suggest file upload
        var path = context.Request.Path.Value?.ToLower();
        if (path == null)
        {
            return false;
        }

        return path.Contains("upload") ||
               path.Contains("file") ||
               path.Contains("attachment") ||
               path.Contains("reservation") && (context.Request.Method == "POST" || context.Request.Method == "PUT");
    }
}

public static class UnsupportedMediaTypeMiddlewareExtensions
{
    public static IApplicationBuilder UseUnsupportedMediaTypeMiddleware(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<UnsupportedMediaTypeMiddleware>();
    }
}