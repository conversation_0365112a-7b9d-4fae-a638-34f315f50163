﻿using System;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Models;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.HotelFrontOffice.Rooms;

public interface IRoomAppService : IApplicationService
{
    Task<RoomDto> CreateAsync(CreateUpdateRoomDto input);

    /// <summary>
    /// Get a room by ID
    /// </summary>
    Task<RoomDto> GetAsync(Guid id);

    /// <summary>
    /// Update a room
    /// </summary>
    Task<RoomDto> UpdateAsync(Guid id, CreateUpdateRoomDto input);
}