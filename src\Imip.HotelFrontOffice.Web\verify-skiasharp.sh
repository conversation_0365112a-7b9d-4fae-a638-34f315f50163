﻿#!/bin/bash
set -e

echo "SkiaSharp Verification Script"
echo "============================"
echo

# Check for required libraries
echo "Checking for required libraries..."
for lib in libSkiaSharp.so libfontconfig.so.1 libfreetype.so.6; do
    if ldconfig -p | grep -q $lib; then
        echo "✅ $lib found in library cache"
    else
        echo "❌ $lib NOT found in library cache"
        
        # Try to find the library in the filesystem
        echo "   Searching for $lib in filesystem..."
        find / -name "$lib" 2>/dev/null || echo "   Not found in filesystem"
    fi
done

# Check environment variables
echo
echo "Environment variables:"
echo "LD_LIBRARY_PATH: $LD_LIBRARY_PATH"
echo "FONTCONFIG_PATH: $FONTCONFIG_PATH"
echo "SKIASHARP_DEBUG: $SKIASHARP_DEBUG"
echo "SKIASHARP_PRELOAD_NATIVE: $SKIASHARP_PRELOAD_NATIVE"

# Check for SkiaSharp native libraries
echo
echo "Searching for SkiaSharp native libraries..."
find /app -name "libSkiaSharp.so" -type f

# Check for fontconfig
echo
echo "Checking fontconfig installation..."
if command -v fc-list &> /dev/null; then
    echo "✅ fontconfig is installed"
    echo "Available fonts:"
    fc-list | head -5
    echo "... (more fonts may be available)"
else
    echo "❌ fontconfig is NOT installed"
fi

# Check for runtime directories
echo
echo "Runtime directories:"
find /app -name "runtimes" -type d | xargs ls -la 2>/dev/null || echo "No runtime directories found"

echo
echo "Verification complete"
