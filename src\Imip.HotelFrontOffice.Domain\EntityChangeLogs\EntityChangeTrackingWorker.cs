using System;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Volo.Abp.BackgroundWorkers;
using Volo.Abp.Threading;

namespace Imip.HotelFrontOffice.EntityChangeLogs
{
    /// <summary>
    /// Background worker for processing audit logs and creating entity change logs
    /// </summary>
    public class EntityChangeTrackingWorker : AsyncPeriodicBackgroundWorkerBase
    {
        public EntityChangeTrackingWorker(
            AbpAsyncTimer timer,
            IServiceScopeFactory serviceScopeFactory)
            : base(timer, serviceScopeFactory)
        {
            // Run every 5 minutes
            Timer.Period = 5 * 60 * 1000;
        }

        protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
        {
            var logger = workerContext.ServiceProvider.GetRequiredService<ILogger<EntityChangeTrackingWorker>>();
            var entityChangeTrackingService = workerContext.ServiceProvider.GetRequiredService<EntityChangeTrackingService>();

            try
            {
                // Process audit logs from the last 10 minutes
                var endTime = DateTime.UtcNow;
                var startTime = endTime.AddMinutes(-10);

                var processedCount = await entityChangeTrackingService.ProcessAuditLogsAsync(startTime, endTime);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error occurred during EntityChangeTrackingWorker execution");
            }
        }
    }
}
