﻿using System;
using System.Collections.Generic;
using Imip.HotelFrontOffice.Attachments;
using Imip.HotelFrontOffice.DiningOptions;
using Imip.HotelFrontOffice.Master.Company;
using Imip.HotelFrontOffice.MasterStatuses;
using Imip.HotelFrontOffice.PaymentMethods;
using Imip.HotelFrontOffice.ReservationDetails;
using Imip.HotelFrontOffice.ReservationTypes;
using Volo.Abp.Application.Dtos;

namespace Imip.HotelFrontOffice.Reservations;

public class ReservationsDto : AuditedEntityDto<Guid>
{
    public required string ReservationCode { get; set; }
    public string? GroupCode { get; set; }
    public string? BookerName { get; set; }
    public string? BookerIdentityNumber { get; set; }
    public string? BookerPhoneNumber { get; set; }
    public string? BookerEmail { get; set; }
    public DateTime ArrivalDate { get; set; }
    public int Days { get; set; }

    /// <summary>
    /// Legacy attachment field (for backward compatibility)
    /// </summary>
    public string? Attachment { get; set; }

    /// <summary>
    /// List of attachments associated with this reservation
    /// </summary>
    public List<ReservationAttachmentInfoDto>? Attachments { get; set; }

    //public Guid ReservationTypeId { get; set; }
    //public string ReservationTypeName { get; set; }
    // IDs and expanded objects for related entities
    public Guid? CompanyId { get; set; }
    public CompanyDto? Company { get; set; }

    public Guid? DiningOptionsId { get; set; }
    public DiningOptionsDto? DiningOptions { get; set; }

    public Guid? PaymentMethodId { get; set; }
    public PaymentMethodDto? PaymentMethod { get; set; }

    public Guid StatusId { get; set; }
    public MasterStatusDto Status { get; set; } = default!;

    public Guid ReservationTypeId { get; set; }
    public ReservationTypesDto ReservationTypes { get; set; } = default!;
    public List<ReservationDetailsDto>? ReservationDetails { get; set; }
}