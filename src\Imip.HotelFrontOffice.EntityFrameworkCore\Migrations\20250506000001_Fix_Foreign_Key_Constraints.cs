using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.HotelFrontOffice.Migrations
{
    /// <inheritdoc />
    public partial class Fix_Foreign_Key_Constraints : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Fix the foreign key constraint issue by updating problematic records
            
            // Set StatusId to NULL for ReservationDetails records where the referenced MasterStatus doesn't exist
            migrationBuilder.Sql(@"
                UPDATE AppReservationDetails
                SET StatusId = NULL
                FROM AppReservationDetails rd
                LEFT JOIN MasterStatus ms ON rd.StatusId = ms.Id
                WHERE rd.StatusId IS NOT NULL AND ms.Id IS NULL;
            ");

            // Set CompanyId to NULL for Reservations records where the referenced Company doesn't exist
            migrationBuilder.Sql(@"
                UPDATE AppReservations
                SET CompanyId = NULL
                FROM AppReservations r
                LEFT JOIN MasterCompany c ON r.CompanyId = c.Id
                WHERE r.CompanyId IS NOT NULL AND c.Id IS NULL;
            ");

            // Set DiningOptionsId to NULL for Reservations records where the referenced DiningOptions doesn't exist
            migrationBuilder.Sql(@"
                UPDATE AppReservations
                SET DiningOptionsId = NULL
                FROM AppReservations r
                LEFT JOIN MasterDiningOptions d ON r.DiningOptionsId = d.Id
                WHERE r.DiningOptionsId IS NOT NULL AND d.Id IS NULL;
            ");

            // Set PaymentMethodId to NULL for Reservations records where the referenced PaymentMethod doesn't exist
            migrationBuilder.Sql(@"
                UPDATE AppReservations
                SET PaymentMethodId = NULL
                FROM AppReservations r
                LEFT JOIN MasterPaymentMethod p ON r.PaymentMethodId = p.Id
                WHERE r.PaymentMethodId IS NOT NULL AND p.Id IS NULL;
            ");

            // Set StatusId to NULL for Reservations records where the referenced Status doesn't exist
            migrationBuilder.Sql(@"
                UPDATE AppReservations
                SET StatusId = NULL
                FROM AppReservations r
                LEFT JOIN MasterStatus s ON r.StatusId = s.Id
                WHERE r.StatusId IS NOT NULL AND s.Id IS NULL;
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // No down migration needed as we're just fixing data issues
        }
    }
}
