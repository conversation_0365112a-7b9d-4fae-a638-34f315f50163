#!/bin/bash

# Apply the PV and PVC
echo "Applying PersistentVolume..."
kubectl apply -f k8s/dev/pv.yaml

echo "Applying PersistentVolumeClaim..."
kubectl apply -f k8s/dev/pvc.yaml

# Check if the PVC is bound
echo "Checking PVC status..."
kubectl get pvc -n imip-wisma-dev-new

# Delete the existing deployment if it exists
echo "Deleting existing deployment if it exists..."
kubectl delete deployment imip-wisma-web-new -n imip-wisma-dev-new --ignore-not-found=true

# Apply the updated deployment
echo "Applying the updated deployment..."
kubectl apply -f k8s/dev/web-deployment.yaml

# Check the status of the deployment
echo "Checking deployment status..."
kubectl rollout status deployment/imip-wisma-web -n imip-wisma-dev-new --timeout=300s

# Show the pods
echo "Current pod status:"
kubectl get pods -n imip-wisma-dev-new

# Show recent events
echo "Recent events:"
kubectl get events -n imip-wisma-dev-new --sort-by='.lastTimestamp' | tail -n 20
