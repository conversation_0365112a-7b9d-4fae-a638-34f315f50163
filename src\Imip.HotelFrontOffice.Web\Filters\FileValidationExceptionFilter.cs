using System;
using System.Net;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Logging;
using Volo.Abp;

namespace Imip.HotelFrontOffice.Web.Filters;

public class FileValidationExceptionFilter : IAsyncExceptionFilter
{
    private readonly ILogger<FileValidationExceptionFilter> _logger;

    public FileValidationExceptionFilter(ILogger<FileValidationExceptionFilter> logger)
    {
        _logger = logger;
    }

    public async Task OnExceptionAsync(ExceptionContext context)
    {
        // Check if the exception is related to file validation
        if (IsFileValidationException(context.Exception))
        {
            // Log asynchronously
            await Task.Run(() => _logger.LogWarning("File validation error: {Message}", context.Exception.Message));

            // Create a proper error response following the required format
            var errorResponse = new
            {
                error = new
                {
                    code = "UnsupportedMediaType",
                    message = "The file type is not supported. Only PDF and image files are allowed.",
                    details = context.Exception.Message,
                    data = new
                    {
                        statusCode = 415,
                        errorType = "FileValidationError",
                        allowedTypes = "PDF and image files (JPEG, PNG, GIF, etc.)"
                    },
                    validationErrors = new[]
                    {
                        new
                        {
                            message = "Invalid file type",
                            members = new[] { "AttachmentFile" }
                        }
                    }
                }
            };

            // Set the response
            context.Result = new ObjectResult(errorResponse)
            {
                StatusCode = (int)HttpStatusCode.UnsupportedMediaType
            };

            // Mark the exception as handled
            context.ExceptionHandled = true;
        }
    }

    private bool IsFileValidationException(Exception exception)
    {
        // Log the exception for debugging
        _logger.LogDebug("Checking if exception is a file validation exception: {ExceptionType}, {Message}",
            exception.GetType().Name, exception.Message);

        // Check if it's a UserFriendlyException with a specific error code
        if (exception is UserFriendlyException userFriendlyException)
        {
            _logger.LogDebug("UserFriendlyException detected with code: {Code}", userFriendlyException.Code);

            if (userFriendlyException.Code == "HotelFrontOffice:InvalidFileType" ||
                userFriendlyException.Code == "Error.Reservation.InvalidFileType")
            {
                return true;
            }

            // Check message content for file validation related terms
            if (userFriendlyException.Message.Contains("file type", StringComparison.OrdinalIgnoreCase) ||
                userFriendlyException.Message.Contains("PDF", StringComparison.OrdinalIgnoreCase) ||
                userFriendlyException.Message.Contains("image", StringComparison.OrdinalIgnoreCase))
            {
                return true;
            }
        }

        // Check if it's a 415 Unsupported Media Type exception
        if (exception.Message.Contains("415") ||
            exception.Message.Contains("Unsupported Media Type", StringComparison.OrdinalIgnoreCase) ||
            exception.Message.Contains("content type", StringComparison.OrdinalIgnoreCase) ||
            exception.Message.Contains("file type", StringComparison.OrdinalIgnoreCase) ||
            exception.Message.Contains("invalid file", StringComparison.OrdinalIgnoreCase) ||
            exception.Message.Contains("PDF", StringComparison.OrdinalIgnoreCase) ||
            exception.Message.Contains("image", StringComparison.OrdinalIgnoreCase))
        {
            return true;
        }

        return false;
    }
}