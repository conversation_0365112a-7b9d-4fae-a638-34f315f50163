<svg aria-roledescription="sequence" role="graphics-document document" viewBox="-50 -10 758 647" style="max-width: 758px;" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4"><g><rect class="actor actor-bottom" ry="3" rx="3" name="IdServer" height="65" width="150" stroke="#666" fill="#eaeaea" y="561" x="507"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="593.5" x="582"><tspan dy="0" x="582">Identity Server</tspan></text></g><g><rect class="actor actor-bottom" ry="3" rx="3" name="ClientApp" height="65" width="150" stroke="#666" fill="#eaeaea" y="561" x="265"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="593.5" x="340"><tspan dy="0" x="340">Client Application</tspan></text></g><g><rect class="actor actor-bottom" ry="3" rx="3" name="User" height="65" width="150" stroke="#666" fill="#eaeaea" y="561" x="0"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="593.5" x="75"><tspan dy="0" x="75">User</tspan></text></g><g><line name="IdServer" stroke="#999" stroke-width="0.5px" class="actor-line 200" y2="561" x2="582" y1="65" x1="582" id="actor8"></line><g id="root-8"><rect class="actor actor-top" ry="3" rx="3" name="IdServer" height="65" width="150" stroke="#666" fill="#eaeaea" y="0" x="507"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="582"><tspan dy="0" x="582">Identity Server</tspan></text></g></g><g><line name="ClientApp" stroke="#999" stroke-width="0.5px" class="actor-line 200" y2="561" x2="340" y1="65" x1="340" id="actor7"></line><g id="root-7"><rect class="actor actor-top" ry="3" rx="3" name="ClientApp" height="65" width="150" stroke="#666" fill="#eaeaea" y="0" x="265"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="340"><tspan dy="0" x="340">Client Application</tspan></text></g></g><g><line name="User" stroke="#999" stroke-width="0.5px" class="actor-line 200" y2="561" x2="75" y1="65" x1="75" id="actor6"></line><g id="root-6"><rect class="actor actor-top" ry="3" rx="3" name="User" height="65" width="150" stroke="#666" fill="#eaeaea" y="0" x="0"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="75"><tspan dy="0" x="75">User</tspan></text></g></g><style>#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 .error-icon{fill:#a44141;}#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 .edge-thickness-normal{stroke-width:1px;}#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 .marker.cross{stroke:lightgrey;}#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 p{margin:0;}#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 .actor{stroke:#ccc;fill:#1f2020;}#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 text.actor&gt;tspan{fill:lightgrey;stroke:none;}#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 .actor-line{stroke:#ccc;}#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 .messageLine0{stroke-width:1.5;stroke-dasharray:none;stroke:lightgrey;}#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 .messageLine1{stroke-width:1.5;stroke-dasharray:2,2;stroke:lightgrey;}#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 #arrowhead path{fill:lightgrey;stroke:lightgrey;}#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 .sequenceNumber{fill:black;}#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 #sequencenumber{fill:lightgrey;}#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 #crosshead path{fill:lightgrey;stroke:lightgrey;}#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 .messageText{fill:lightgrey;stroke:none;}#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 .labelBox{stroke:#ccc;fill:#1f2020;}#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 .labelText,#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 .labelText&gt;tspan{fill:lightgrey;stroke:none;}#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 .loopText,#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 .loopText&gt;tspan{fill:lightgrey;stroke:none;}#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 .loopLine{stroke-width:2px;stroke-dasharray:2,2;stroke:#ccc;fill:#ccc;}#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 .note{stroke:hsl(180, 0%, 18.3529411765%);fill:hsl(180, 1.5873015873%, 28.3529411765%);}#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 .noteText,#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 .noteText&gt;tspan{fill:rgb(183.8476190475, 181.5523809523, 181.5523809523);stroke:none;}#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 .activation0{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:#ccc;}#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 .activation1{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:#ccc;}#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 .activation2{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:#ccc;}#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 .actorPopupMenu{position:absolute;}#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 .actorPopupMenuPanel{position:absolute;fill:#1f2020;box-shadow:0px 8px 16px 0px rgba(0,0,0,0.2);filter:drop-shadow(3px 5px 2px rgb(0 0 0 / 0.4));}#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 .actor-man line{stroke:#ccc;fill:#1f2020;}#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 .actor-man circle,#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 line{stroke:#ccc;fill:#1f2020;stroke-width:2px;}#mermaid-2361cb7f-ba3d-461d-a52c-0123ff9928b4 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g></g><defs><symbol height="24" width="24" id="computer"><path d="M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z" transform="scale(.5)"></path></symbol></defs><defs><symbol clip-rule="evenodd" fill-rule="evenodd" id="database"><path d="M12.258.001l.256.004.255.005.253.008.251.01.249.012.247.015.246.016.242.019.241.02.239.023.236.024.233.027.231.028.229.031.225.032.223.034.22.036.217.038.214.04.211.041.208.043.205.045.201.046.198.048.194.05.191.051.187.053.183.054.18.056.175.057.172.059.168.06.163.061.16.063.155.064.15.066.074.033.073.033.071.034.07.034.069.035.068.035.067.035.066.035.064.036.064.036.062.036.06.036.06.037.058.037.058.037.055.038.055.038.053.038.052.038.051.039.05.039.048.039.047.039.045.04.044.04.043.04.041.04.04.041.039.041.037.041.036.041.034.041.033.042.032.042.03.042.029.042.027.042.026.043.024.043.023.043.021.043.02.043.018.044.017.043.015.044.013.044.012.044.011.045.009.044.007.045.006.045.004.045.002.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z" transform="scale(.5)"></path></symbol></defs><defs><symbol height="24" width="24" id="clock"><path d="M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z" transform="scale(.5)"></path></symbol></defs><defs><marker orient="auto-start-reverse" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="5" refX="7.9" id="arrowhead"><path d="M -1 0 L 10 5 L 0 10 z"></path></marker></defs><defs><marker refY="4.5" refX="4" orient="auto" markerHeight="8" markerWidth="15" id="crosshead"><path style="stroke-dasharray: 0, 0;" d="M 1,2 L 6,7 M 6,2 L 1,7" stroke-width="1pt" stroke="#000000" fill="none"></path></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="15.5" id="filled-head"><path d="M 18,7 L9,13 L14,7 L9,1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="40" markerWidth="60" refY="15" refX="15" id="sequencenumber"><circle r="6" cy="15" cx="15"></circle></marker></defs><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="80" x="206">Request protected resource</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="117" x2="336" y1="117" x1="76"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="132" x="460">Authenticate user</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="169" x2="578" y1="169" x1="341"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="184" x="463">Issue JWT token</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="221" x2="344" y1="221" x1="581"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="236" x="341">Validate token</text><path style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" d="M 341,273 C 401,263 401,303 341,293"></path><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="318" x="460">Check permission</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="355" x2="578" y1="355" x1="341"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="370" x="583">Validate permission</text><path style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" d="M 583,407 C 643,397 643,437 583,427"></path><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="452" x="463">Return permission result</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="489" x2="344" y1="489" x1="581"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="504" x="209">Grant/deny access</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="541" x2="79" y1="541" x1="339"></line></svg>


# Integration with ABP Framework Applications

ABP Framework provides built-in support for authorization and can be easily integrated with identity server.

### 1. Setting Up Identity Server

1.1 Create Permission Endpoints

```csharp
[Route("api/permission-check")]
[Authorize]
public class PermissionCheckController : ControllerBase
{
    private readonly IPermissionChecker _permissionChecker;

    public PermissionCheckController(IPermissionChecker permissionChecker)
    {
        _permissionChecker = permissionChecker;
    }

    [HttpGet]
    public async Task<IActionResult> CheckPermission(string name)
    {
        if (string.IsNullOrEmpty(name))
        {
            return BadRequest("Permission name is required");
        }

        var isGranted = await _permissionChecker.IsGrantedAsync(name);
        return Ok(new { IsGranted = isGranted });
    }

    [HttpGet("multiple")]
    public async Task<IActionResult> CheckMultiplePermissions([FromQuery] string[] names)
    {
        if (names == null || names.Length == 0)
        {
            return BadRequest("At least one permission name is required");
        }

        var result = await _permissionChecker.IsGrantedAsync(names);
        var response = new Dictionary<string, bool>();

        foreach (var item in result.Result)
        {
            response[item.Key] = item.Value == PermissionGrantResult.Granted;
        }

        return Ok(new { Permissions = response });
    }
}
```
1.2. Define Permissions:

```csharp
public class YourPermissionDefinitionProvider : PermissionDefinitionProvider
{
    public override void Define(IPermissionDefinitionContext context)
    {
        var myGroup = context.AddGroup("YourApp");
        
        // Define permissions
        var userPermission = myGroup.AddPermission("User");
        userPermission.AddChild("User.View");
        userPermission.AddChild("User.Create");
        userPermission.AddChild("User.Edit");
        userPermission.AddChild("User.Delete");
        
        // Add more permissions as needed
    }
}
```

### 2. Integration Client Application with Identity Server

2.1. Create a Custom permission checker

```csharp
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Security.Claims;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.DependencyInjection;

namespace YourApp.Permissions
{
    [Dependency(ReplaceServices = true)]
    [ExposeServices(typeof(IPermissionChecker))]
    public class CentralizedPermissionChecker : IPermissionChecker, ITransientDependency
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IConfiguration _configuration;
        private readonly ILogger<CentralizedPermissionChecker> _logger;
        private readonly IHttpClientFactory _httpClientFactory;

        public CentralizedPermissionChecker(
            IHttpContextAccessor httpContextAccessor,
            IConfiguration configuration,
            ILogger<CentralizedPermissionChecker> logger,
            IHttpClientFactory httpClientFactory)
        {
            _httpContextAccessor = httpContextAccessor;
            _configuration = configuration;
            _logger = logger;
            _httpClientFactory = httpClientFactory;
        }

        public async Task<bool> IsGrantedAsync(string name)
        {
            return await IsGrantedAsync(_httpContextAccessor.HttpContext?.User, name);
        }

        public async Task<bool> IsGrantedAsync(ClaimsPrincipal? principal, string name)
        {
            try
            {
                if (principal == null || !principal.Identity?.IsAuthenticated ?? true)
                {
                    return false;
                }

                // Check if permission is in claims
                if (principal.HasClaim(c => c.Type == "permission" && c.Value == name))
                {
                    return true;
                }

                // Call Identity Server
                var accessToken = await _httpContextAccessor.HttpContext.GetTokenAsync("access_token");
                if (string.IsNullOrEmpty(accessToken))
                {
                    return false;
                }

                var identityServerUrl = _configuration["AuthServer:Authority"];
                var permissionCheckEndpoint = $"{identityServerUrl}/api/permission-check";

                var client = _httpClientFactory.CreateClient("IdentityServer");
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                var response = await client.GetAsync($"{permissionCheckEndpoint}?name={Uri.EscapeDataString(name)}");
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<PermissionCheckResult>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    
                    return result?.IsGranted ?? false;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking permission {Permission}", name);
                return false;
            }
        }

        public async Task<bool> IsGrantedAsync(Guid userId, string name)
        {
            // Implementation for checking permission for a specific user
            // Similar to above but with userId parameter
            // ...
        }

        public async Task<MultiplePermissionGrantResult> IsGrantedAsync(string[] names)
        {
            return await IsGrantedAsync(_httpContextAccessor.HttpContext?.User, names);
        }

        public async Task<MultiplePermissionGrantResult> IsGrantedAsync(ClaimsPrincipal? principal, string[] names)
        {
            // Implementation for checking multiple permissions
            // ...
        }

        private class PermissionCheckResult
        {
            public bool IsGranted { get; set; }
        }
    }
}
```
2.2 Create a Module to Register the permission checker

```csharp
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Modularity;

namespace YourApp.Permissions
{
    public class PermissionCheckerModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            // Register HTTP client for Identity Server communication
            context.Services.AddHttpClient("IdentityServer");
            
            // Ensure HttpContextAccessor is registered
            context.Services.TryAddSingleton<IHttpContextAccessor, HttpContextAccessor>();
            
            // Register our custom permission checker
            context.Services.Replace(ServiceDescriptor.Transient<IPermissionChecker, CentralizedPermissionChecker>());
        }
    }
}
```

2.3. Update your application module
```csharp
[DependsOn(
    // Other dependencies
    typeof(PermissionCheckerModule)
)]
public class YourAppModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        // Existing configuration
        
        // Configure authentication
        context.Services.AddAuthentication()
            .AddJwtBearer(options =>
            {
                options.Authority = configuration["AuthServer:Authority"];
                options.RequireHttpsMetadata = configuration.GetValue<bool>("AuthServer:RequireHttpsMetadata");
                options.Audience = "YourApp";
                
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidateLifetime = true,
                    ValidateIssuerSigningKey = true,
                    ValidIssuer = configuration["AuthServer:Authority"],
                    ValidAudience = "YourApp",
                    ClockSkew = TimeSpan.FromMinutes(5),
                    NameClaimType = "name",
                    RoleClaimType = "role"
                };
            });
    }
}
```
2.4. Configure appsettings.json

```json
{
  "AuthServer": {
    "Authority": "https://your-identity-server.com",
    "RequireHttpsMetadata": true,
    "ClientId": "YourAppClient",
    "ClientSecret": "YourClientSecret"
  }
}
```

2.5. Use Authorization in Controllers/Services
```csharp
[Authorize("YourApp.User.View")]
public class UserController : AbpController
{
    // Controller implementation
}
```

## Integration with Standard .NET Applications

For standard .NET applications without ABP Framework, you'll need to implement the permission checking logic manually.

### 1. Configure Authentication

```csharp
public void ConfigureServices(IServiceCollection services)
{
    // Add authentication
    services.AddAuthentication(options =>
    {
        options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
        options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
    })
    .AddJwtBearer(options =>
    {
        options.Authority = Configuration["AuthServer:Authority"];
        options.RequireHttpsMetadata = Configuration.GetValue<bool>("AuthServer:RequireHttpsMetadata");
        options.Audience = "YourApp";
        
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = Configuration["AuthServer:Authority"],
            ValidAudience = "YourApp",
            ClockSkew = TimeSpan.FromMinutes(5),
            NameClaimType = "name",
            RoleClaimType = "role"
        };
    });

    // Add HTTP client
    services.AddHttpClient("IdentityServer");
    
    // Add HttpContextAccessor
    services.AddHttpContextAccessor();
    
    // Add permission checker
    services.AddScoped<IPermissionChecker, IdentityServerPermissionChecker>();
    
    // Add authorization
    services.AddAuthorization(options =>
    {
        options.AddPolicy("Permission", policy =>
            policy.Requirements.Add(new PermissionRequirement()));
    });
    
    // Add authorization handler
    services.AddScoped<IAuthorizationHandler, PermissionAuthorizationHandler>();
}
```
### 2. Implement Permission Checker
```csharp
public interface IPermissionChecker
{
    Task<bool> IsGrantedAsync(string permissionName);
    Task<bool> IsGrantedAsync(ClaimsPrincipal principal, string permissionName);
}

public class IdentityServerPermissionChecker : IPermissionChecker
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IConfiguration _configuration;
    private readonly ILogger<IdentityServerPermissionChecker> _logger;

    public IdentityServerPermissionChecker(
        IHttpContextAccessor httpContextAccessor,
        IHttpClientFactory httpClientFactory,
        IConfiguration configuration,
        ILogger<IdentityServerPermissionChecker> logger)
    {
        _httpContextAccessor = httpContextAccessor;
        _httpClientFactory = httpClientFactory;
        _configuration = configuration;
        _logger = logger;
    }

    public async Task<bool> IsGrantedAsync(string permissionName)
    {
        return await IsGrantedAsync(_httpContextAccessor.HttpContext?.User, permissionName);
    }

    public async Task<bool> IsGrantedAsync(ClaimsPrincipal principal, string permissionName)
    {
        try
        {
            if (principal == null || !principal.Identity?.IsAuthenticated ?? true)
            {
                return false;
            }

            // Check if permission is in claims
            if (principal.HasClaim(c => c.Type == "permission" && c.Value == permissionName))
            {
                return true;
            }

            // Call Identity Server
            var accessToken = await _httpContextAccessor.HttpContext.GetTokenAsync("access_token");
            if (string.IsNullOrEmpty(accessToken))
            {
                return false;
            }

            var identityServerUrl = _configuration["AuthServer:Authority"];
            var permissionCheckEndpoint = $"{identityServerUrl}/api/permission-check";

            var client = _httpClientFactory.CreateClient("IdentityServer");
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

            var response = await client.GetAsync($"{permissionCheckEndpoint}?name={Uri.EscapeDataString(permissionName)}");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<PermissionCheckResult>(content, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                
                return result?.IsGranted ?? false;
            }
            
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking permission {Permission}", permissionName);
            return false;
        }
    }

    private class PermissionCheckResult
    {
        public bool IsGranted { get; set; }
    }
}
```
### 3. Implement Authorization Handler

```csharp
public class PermissionRequirement : IAuthorizationRequirement
{
    public string PermissionName { get; }

    public PermissionRequirement(string permissionName = null)
    {
        PermissionName = permissionName;
    }
}

public class PermissionAuthorizationHandler : AuthorizationHandler<PermissionRequirement>
{
    private readonly IPermissionChecker _permissionChecker;

    public PermissionAuthorizationHandler(IPermissionChecker permissionChecker)
    {
        _permissionChecker = permissionChecker;
    }

    protected override async Task HandleRequirementAsync(
        AuthorizationHandlerContext context,
        PermissionRequirement requirement)
    {
        if (context.Resource is HttpContext httpContext)
        {
            var endpoint = httpContext.GetEndpoint();
            var authorizeAttributes = endpoint?.Metadata.GetOrderedMetadata<AuthorizeAttribute>() ?? Array.Empty<AuthorizeAttribute>();
            
            foreach (var attribute in authorizeAttributes)
            {
                var permissionName = attribute.Policy;
                if (!string.IsNullOrEmpty(permissionName) && await _permissionChecker.IsGrantedAsync(context.User, permissionName))
                {
                    context.Succeed(requirement);
                    return;
                }
            }
        }
        else if (!string.IsNullOrEmpty(requirement.PermissionName))
        {
            if (await _permissionChecker.IsGrantedAsync(context.User, requirement.PermissionName))
            {
                context.Succeed(requirement);
                return;
            }
        }
    }
}
```

### 4. Use Authorization in Controller

```csharp
[Authorize("YourApp.User.View")]
public class UserController : ControllerBase
{
    // Controller implementation
}
```


## Integration With Laravel
### 1. Install Packages

```shell
composer require laravel/passport firebase/php-jwt guzzlehttp/guzzle
```

### 2. Create JWT Middleware

```shell
<?php

namespace App\Http\Middleware;

use Closure;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ValidateJwtToken
{
    protected $identityServerUrl;

    public function __construct()
    {
        $this->identityServerUrl = config('auth.identity_server.url');
    }

    public function handle(Request $request, Closure $next)
    {
        $token = $request->bearerToken();

        if (!$token) {
            return response()->json(['message' => 'Unauthorized'], 401);
        }

        try {
            // Get JWKS from Identity Server (with caching)
            $jwks = Cache::remember('identity_server_jwks', 3600, function () {
                $client = new Client();
                $response = $client->get("{$this->identityServerUrl}/.well-known/openid-configuration/jwks");
                return json_decode($response->getBody(), true);
            });

            // Parse token header
            $tokenParts = explode('.', $token);
            $header = json_decode(base64_decode($tokenParts[0]), true);
            $kid = $header['kid'];

            // Find the key with matching kid
            $key = null;
            foreach ($jwks['keys'] as $jwk) {
                if ($jwk['kid'] === $kid) {
                    $key = $jwk;
                    break;
                }
            }

            if (!$key) {
                return response()->json(['message' => 'Invalid token'], 401);
            }

            // Convert JWK to PEM format
            $pem = $this->jwkToPem($key);

            // Verify and decode token
            $decoded = JWT::decode($token, new Key($pem, 'RS256'));

            // Check if token is valid for this application
            if (!isset($decoded->aud) || $decoded->aud !== config('auth.identity_server.audience')) {
                return response()->json(['message' => 'Invalid audience'], 401);
            }

            // Add decoded token to request
            $request->attributes->add(['jwt_payload' => $decoded]);

            return $next($request);
        } catch (\Exception $e) {
            Log::error('JWT validation error: ' . $e->getMessage());
            return response()->json(['message' => 'Unauthorized'], 401);
        }
    }

    protected function jwkToPem($jwk)
    {
        // Implementation of JWK to PEM conversion
        // This is a simplified version, you might want to use a library for this
        $modulus = base64_decode(strtr($jwk['n'], '-_', '+/'));
        $exponent = base64_decode(strtr($jwk['e'], '-_', '+/'));
        
        $modulus = $this->urlsafeB64Decode($jwk['n']);
        $exponent = $this->urlsafeB64Decode($jwk['e']);
        
        $rsa = [
            'modulus' => pack('Ca*a*', 2, $this->encodeLength(strlen($modulus)), $modulus),
            'publicExponent' => pack('Ca*a*', 2, $this->encodeLength(strlen($exponent)), $exponent),
        ];
        
        $rsaPublicKey = pack(
            'Ca*a*a*',
            48,
            $this->encodeLength(strlen($rsa['modulus']) + strlen($rsa['publicExponent'])),
            $rsa['modulus'],
            $rsa['publicExponent']
        );
        
        $der = pack(
            'Ca*a*',
            48,
            $this->encodeLength(strlen($rsaPublicKey) + 5),
            pack(
                'Ca*a*',
                48,
                $this->encodeLength(9),
                pack('Ca*', 6, $this->encodeLength(5), "\x2A\x86\x48\x86\xF7\x0D\x01\x01\x01") .
                pack('Ca*', 5, $this->encodeLength(0))
            ) .
            pack('Ca*a*', 3, $this->encodeLength(strlen($rsaPublicKey) + 1), chr(0) . $rsaPublicKey)
        );
        
        $pem = "-----BEGIN PUBLIC KEY-----\r\n" .
            chunk_split(base64_encode($der), 64, "\r\n") .
            "-----END PUBLIC KEY-----";
        
        return $pem;
    }
    
    protected function urlsafeB64Decode($input)
    {
        $remainder = strlen($input) % 4;
        if ($remainder) {
            $padlen = 4 - $remainder;
            $input .= str_repeat('=', $padlen);
        }
        return base64_decode(strtr($input, '-_', '+/'));
    }
    
    protected function encodeLength($length)
    {
        if ($length <= 0x7F) {
            return chr($length);
        }
        
        $temp = ltrim(pack('N', $length), chr(0));
        return pack('Ca*', 0x80 | strlen($temp), $temp);
    }
}
```

### 3. Create Permission Checker Service

```shell
<?php

namespace App\Services;

use GuzzleHttp\Client;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class PermissionChecker
{
    protected $identityServerUrl;
    protected $httpClient;

    public function __construct()
    {
        $this->identityServerUrl = config('auth.identity_server.url');
        $this->httpClient = new Client();
    }

    public function isGranted($permissionName, $token)
    {
        try {
            // Check if permission is cached
            $cacheKey = "permission_{$token}_{$permissionName}";
            if (Cache::has($cacheKey)) {
                return Cache::get($cacheKey);
            }

            // Check with Identity Server
            $response = $this->httpClient->get(
                "{$this->identityServerUrl}/api/permission-check",
                [
                    'query' => ['name' => $permissionName],
                    'headers' => [
                        'Authorization' => "Bearer {$token}"
                    ]
                ]
            );

            $result = json_decode($response->getBody(), true);
            $isGranted = $result['isGranted'] ?? false;

            // Cache the result for 5 minutes
            Cache::put($cacheKey, $isGranted, 300);

            return $isGranted;
        } catch (\Exception $e) {
            Log::error("Permission check error: {$e->getMessage()}");
            return false;
        }
    }
}
```

### 4. Create Permission Middleware

```shell
<?php

namespace App\Http\Middleware;

use App\Services\PermissionChecker;
use Closure;
use Illuminate\Http\Request;

class CheckPermission
{
    protected $permissionChecker;

    public function __construct(PermissionChecker $permissionChecker)
    {
        $this->permissionChecker = $permissionChecker;
    }

    public function handle(Request $request, Closure $next, $permissionName)
    {
        $token = $request->bearerToken();

        if (!$token) {
            return response()->json(['message' => 'Unauthorized'], 401);
        }

        if (!$this->permissionChecker->isGranted($permissionName, $token)) {
            return response()->json(['message' => 'Forbidden'], 403);
        }

        return $next($request);
    }
}
```


### 5. Register Middleware in kernel.php

```shell
protected $routeMiddleware = [
    // Other middlewares...
    'jwt' => \App\Http\Middleware\ValidateJwtToken::class,
    'permission' => \App\Http\Middleware\CheckPermission::class,
];
```

### 6. Configure Identity Server Settings

update config/auth.php

```shell
'identity_server' => [
    'url' => env('IDENTITY_SERVER_URL', 'https://your-identity-server.com'),
    'audience' => env('IDENTITY_SERVER_AUDIENCE', 'your-app'),
],
```

### 7. Use Middleware in Routes

```shell
Route::middleware(['jwt', 'permission:YourApp.User.View'])->get('/users', 'UserController@index');
```

## Codeigniter

### 1. Install required packages
update composer.json
```json
{
    "require": {
        "firebase/php-jwt": "^6.0",
        "guzzlehttp/guzzle": "^7.0"
    }
}
```

```shell
composer install
```

### 2.Create JWT Helper
Create a new helper file app/Helpers/jwt_helper.php
```shell
<?php defined('BASEPATH') OR exit('No direct script access allowed');

use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use GuzzleHttp\Client;

if (!function_exists('validate_jwt')) {
    function validate_jwt($token)
    {
        $CI =& get_instance();
        $CI->load->config('identity_server');
        $identityServerUrl = $CI->config->item('identity_server_url');
        
        try {
            // Get JWKS from Identity Server
            $client = new Client();
            $response = $client->get("{$identityServerUrl}/.well-known/openid-configuration/jwks");
            $jwks = json_decode($response->getBody(), true);
            
            // Parse token header
            $tokenParts = explode('.', $token);
            $header = json_decode(base64_decode($tokenParts[0]), true);
            $kid = $header['kid'];
            
            // Find the key with matching kid
            $key = null;
            foreach ($jwks['keys'] as $jwk) {
                if ($jwk['kid'] === $kid) {
                    $key = $jwk;
                    break;
                }
            }
            
            if (!$key) {
                return null;
            }
            
            // Convert JWK to PEM format
            $pem = jwk_to_pem($key);
            
            // Verify and decode token
            $decoded = JWT::decode($token, new Key($pem, 'RS256'));
            
            // Check if token is valid for this application
            if (!isset($decoded->aud) || $decoded->aud !== $CI->config->item('identity_server_audience')) {
                return null;
            }
            
            return $decoded;
        } catch (Exception $e) {
            log_message('error', 'JWT validation error: ' . $e->getMessage());
            return null;
        }
    }
}

if (!function_exists('jwk_to_pem')) {
    function jwk_to_pem($jwk)
    {
        // Implementation of JWK to PEM conversion
        $modulus = urlsafe_b64_decode($jwk['n']);
        $exponent = urlsafe_b64_decode($jwk['e']);
        
        $modulus_hex = bin2hex($modulus);
        $exponent_hex = bin2hex($exponent);
        
        $modulus_base10 = bc_hexdec($modulus_hex);
        $exponent_base10 = bc_hexdec($exponent_hex);
        
        $components = array(
            'modulus' => $modulus_base10,
            'publicExponent' => $exponent_base10
        );
        
        $rsa = new phpseclib\Crypt\RSA();
        $rsa->loadKey($components, phpseclib\Crypt\RSA::PUBLIC_FORMAT_RAW);
        
        return $rsa->getPublicKey();
    }
}

if (!function_exists('urlsafe_b64_decode')) {
    function urlsafe_b64_decode($input)
    {
        $remainder = strlen($input) % 4;
        if ($remainder) {
            $padlen = 4 - $remainder;
            $input .= str_repeat('=', $padlen);
        }
        return base64_decode(strtr($input, '-_', '+/'));
    }
}

if (!function_exists('bc_hexdec')) {
    function bc_hexdec($hex)
    {
        $dec = '0';
        $len = strlen($hex);
        for ($i = 0; $i < $len; $i++) {
            $dec = bcadd($dec, bcmul(strval(hexdec($hex[$i])), bcpow('16', strval($len - $i - 1))));
        }
        return $dec;
    }
}

if (!function_exists('get_bearer_token')) {
    function get_bearer_token()
    {
        $CI =& get_instance();
        $headers = $CI->input->request_headers();
        
        // Check for Authorization header
        if (isset($headers['Authorization'])) {
            if (preg_match('/Bearer\s(\S+)/', $headers['Authorization'], $matches)) {
                return $matches[1];
            }
        }
        
        return null;
    }
}
```

### 3. Create Permission Library
Create a new service file application/libraries/Permission_checker.php
```shell
<?php defined('BASEPATH') OR exit('No direct script access allowed');

use GuzzleHttp\Client;

class Permission_checker {
    
    protected $CI;
    protected $identityServerUrl;
    protected $httpClient;
    
    public function __construct()
    {
        $this->CI =& get_instance();
        $this->CI->load->config('identity_server');
        $this->identityServerUrl = $this->CI->config->item('identity_server_url');
        $this->httpClient = new Client();
    }
    
    public function is_granted($permissionName, $token)
    {
        try {
            // Check if permission is cached
            $this->CI->load->driver('cache', array('adapter' => 'file'));
            $cacheKey = "permission_{$token}_{$permissionName}";
            
            if ($cachedResult = $this->CI->cache->get($cacheKey)) {
                return $cachedResult === 'true';
            }
            
            // Check with Identity Server
            $response = $this->httpClient->get(
                "{$this->identityServerUrl}/api/permission-check",
                [
                    'query' => ['name' => $permissionName],
                    'headers' => [
                        'Authorization' => "Bearer {$token}"
                    ]
                ]
            );
            
            $result = json_decode($response->getBody(), true);
            $isGranted = $result['isGranted'] ?? false;
            
            // Cache the result for 5 minutes
            $this->CI->cache->save($cacheKey, $isGranted ? 'true' : 'false', 300);
            
            return $isGranted;
        } catch (Exception $e) {
            log_message('error', "Permission check error: {$e->getMessage()}");
            return false;
        }
    }
    
    public function check_multiple_permissions($permissionNames, $token)
    {
        try {
            // Check with Identity Server
            $response = $this->httpClient->get(
                "{$this->identityServerUrl}/api/permission-check/multiple",
                [
                    'query' => ['names' => $permissionNames],
                    'headers' => [
                        'Authorization' => "Bearer {$token}"
                    ]
                ]
            );
            
            $result = json_decode($response->getBody(), true);
            return $result['permissions'] ?? [];
        } catch (Exception $e) {
            log_message('error', "Multiple permissions check error: {$e->getMessage()}");
            return [];
        }
    }
}
```

### 4. Create JWT Authentication Library
create a new library file application/libraries/jwt_auth.php
```shell
<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Jwt_auth {
    
    protected $CI;
    
    public function __construct()
    {
        $this->CI =& get_instance();
        $this->CI->load->helper('jwt');
    }
    
    public function authenticate()
    {
        $token = get_bearer_token();
        
        if (!$token) {
            $this->_respond_unauthorized('No token provided');
            return false;
        }
        
        $payload = validate_jwt($token);
        
        if (!$payload) {
            $this->_respond_unauthorized('Invalid token');
            return false;
        }
        
        // Store payload in CI instance for later use
        $this->CI->jwt_payload = $payload;
        
        return true;
    }
    
    public function check_permission($permissionName)
    {
        $token = get_bearer_token();
        
        if (!$token) {
            $this->_respond_unauthorized('No token provided');
            return false;
        }
        
        $this->CI->load->library('permission_checker');
        
        if (!$this->CI->permission_checker->is_granted($permissionName, $token)) {
            $this->_respond_forbidden();
            return false;
        }
        
        return true;
    }
    
    private function _respond_unauthorized($message = 'Unauthorized')
    {
        $this->CI->output
            ->set_status_header(401)
            ->set_content_type('application/json')
            ->set_output(json_encode(['message' => $message]));
            
        exit;
    }
    
    private function _respond_forbidden($message = 'Forbidden')
    {
        $this->CI->output
            ->set_status_header(403)
            ->set_content_type('application/json')
            ->set_output(json_encode(['message' => $message]));
            
        exit;
    }
}
```

### 5. Create Identity Server Configuration
create a new config file application/config/identity_server.php
```shell
<?php defined('BASEPATH') OR exit('No direct script access allowed');

$config['identity_server_url'] = 'https://your-identity-server.com';
$config['identity_server_audience'] = 'your-app';
```

### 6. Create Authentication Hook
Create a new hook file application/hooks/Auth_hook.php

```shell
<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Auth_hook {
    
    private $CI;
    private $routes_requiring_auth = [
        'api/users' => 'YourApp.User.View',
        'api/users/create' => 'YourApp.User.Create',
        // Add more routes and their required permissions
    ];
    
    public function __construct()
    {
        $this->CI =& get_instance();
    }
    
    public function check_auth()
    {
        // Skip authentication for certain routes
        if ($this->_should_skip_auth()) {
            return;
        }
        
        // Get current route
        $uri = $this->CI->uri->uri_string();
        
        // Check if route requires authentication
        if (array_key_exists($uri, $this->routes_requiring_auth)) {
            $this->CI->load->library('jwt_auth');
            
            // Authenticate user
            if (!$this->CI->jwt_auth->authenticate()) {
                return;
            }
            
            // Check permission
            $requiredPermission = $this->routes_requiring_auth[$uri];
            $this->CI->jwt_auth->check_permission($requiredPermission);
        }
    }
    
    private function _should_skip_auth()
    {
        // Skip authentication for these routes
        $skip_routes = [
            'auth/login',
            'auth/refresh',
            'public'
        ];
        
        $uri = $this->CI->uri->uri_string();
        
        foreach ($skip_routes as $route) {
            if (strpos($uri, $route) === 0) {
                return true;
            }
        }
        
        return false;
    }
}
```

### 7. Register Hook
Update application/config/hooks.php

```shell
$hook['post_controller_constructor'] = array(
    'class'    => 'Auth_hook',
    'function' => 'check_auth',
    'filename' => 'Auth_hook.php',
    'filepath' => 'hooks',
    'params'   => array()
);
```

### 8. Enable Hooks
Make Sure hooks are enabled in application/config/config.php

```shell
$config['enable_hooks'] = TRUE;
```

### 9. Create Authentication Controller
Create a new controller file application/controllers/Auth.php

```shell
<?php defined('BASEPATH') OR exit('No direct script access allowed');

use GuzzleHttp\Client;

class Auth extends CI_Controller {
    
    public function __construct()
    {
        parent::__construct();
        $this->load->config('identity_server');
    }
    
    public function login()
    {
        // Get username and password from request
        $username = $this->input->post('username');
        $password = $this->input->post('password');
        
        if (!$username || !$password) {
            $this->_respond_error('Username and password are required', 400);
            return;
        }
        
        try {
            // Get token from Identity Server
            $client = new Client();
            $response = $client->post(
                $this->config->item('identity_server_url') . '/connect/token',
                [
                    'form_params' => [
                        'grant_type' => 'password',
                        'client_id' => $this->config->item('identity_server_client_id'),
                        'client_secret' => $this->config->item('identity_server_client_secret'),
                        'username' => $username,
                        'password' => $password,
                        'scope' => 'openid profile YourApp'
                    ]
                ]
            );
            
            $result = json_decode($response->getBody(), true);
            
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($result));
        } catch (Exception $e) {
            log_message('error', 'Login error: ' . $e->getMessage());
            $this->_respond_error('Authentication failed', 401);
        }
    }
    
    public function refresh()
    {
        // Get refresh token from request
        $refreshToken = $this->input->post('refresh_token');
        
        if (!$refreshToken) {
            $this->_respond_error('Refresh token is required', 400);
            return;
        }
        
        try {
            // Get new token from Identity Server
            $client = new Client();
            $response = $client->post(
                $this->config->item('identity_server_url') . '/connect/token',
                [
                    'form_params' => [
                        'grant_type' => 'refresh_token',
                        'client_id' => $this->config->item('identity_server_client_id'),
                        'client_secret' => $this->config->item('identity_server_client_secret'),
                        'refresh_token' => $refreshToken
                    ]
                ]
            );
            
            $result = json_decode($response->getBody(), true);
            
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode($result));
        } catch (Exception $e) {
            log_message('error', 'Token refresh error: ' . $e->getMessage());
            $this->_respond_error('Token refresh failed', 401);
        }
    }
    
    private function _respond_error($message, $status = 500)
    {
        $this->output
            ->set_status_header($status)
            ->set_content_type('application/json')
            ->set_output(json_encode(['error' => $message]));
    }
}
```


### 10. Update identity server configuration
update application/config/identity_server.php with client credentials

```shell
<?php defined('BASEPATH') OR exit('No direct script access allowed');

$config['identity_server_url'] = 'https://your-identity-server.com';
$config['identity_server_audience'] = 'your-app';
$config['identity_server_client_id'] = 'your-client-id';
$config['identity_server_client_secret'] = 'your-client-secret';
```

### 11. Create an example API Controller
Create a new controller file application/controllers/api/Users.php

```shell
<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Users extends CI_Controller {
    
    public function __construct()
    {
        parent::__construct();
        // Authentication is handled by the Auth_hook
    }
    
    public function index()
    {
        // This endpoint requires 'YourApp.User.View' permission
        // as defined in the Auth_hook
        
        // Example data
        $users = [
            ['id' => 1, 'name' => 'John Doe', 'email' => '<EMAIL>'],
            ['id' => 2, 'name' => 'Jane Smith', 'email' => '<EMAIL>']
        ];
        
        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode([
                'success' => true,
                'data' => $users
            ]));
    }
    
    public function create()
    {
        // This endpoint requires 'YourApp.User.Create' permission
        // as defined in the Auth_hook
        
        // Get user data from request
        $name = $this->input->post('name');
        $email = $this->input->post('email');
        
        if (!$name || !$email) {
            $this->output
                ->set_status_header(400)
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'success' => false,
                    'message' => 'Name and email are required'
                ]));
            return;
        }
        
        // Create user logic here...
        
        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode([
                'success' => true,
                'message' => 'User created successfully',
                'data' => [
                    'id' => 3,
                    'name' => $name,
                    'email' => $email
                ]
            ]));
    }
}
```

### 12 Manual Permission-Check-In Controllers
if you need to check permissions manually in a controller

```shell
<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Users extends CI_Controller {
    
    public function __construct()
    {
        parent::__construct();
        // Authentication is handled by the Auth_hook
    }
    
    public function index()
    {
        // This endpoint requires 'YourApp.User.View' permission
        // as defined in the Auth_hook
        
        // Example data
        $users = [
            ['id' => 1, 'name' => 'John Doe', 'email' => '<EMAIL>'],
            ['id' => 2, 'name' => 'Jane Smith', 'email' => '<EMAIL>']
        ];
        
        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode([
                'success' => true,
                'data' => $users
            ]));
    }
    
    public function create()
    {
        // This endpoint requires 'YourApp.User.Create' permission
        // as defined in the Auth_hook
        
        // Get user data from request
        $name = $this->input->post('name');
        $email = $this->input->post('email');
        
        if (!$name || !$email) {
            $this->output
                ->set_status_header(400)
                ->set_content_type('application/json')
                ->set_output(json_encode([
                    'success' => false,
                    'message' => 'Name and email are required'
                ]));
            return;
        }
        
        // Create user logic here...
        
        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode([
                'success' => true,
                'message' => 'User created successfully',
                'data' => [
                    'id' => 3,
                    'name' => $name,
                    'email' => $email
                ]
            ]));
    }
}
```

### 13. Configure Routes
update application/config/routes.php

```shell
$route['api/users'] = 'api/users/index';
$route['api/users/create'] = 'api/users/create';
$route['auth/login'] = 'auth/login';
$route['auth/refresh'] = 'auth/refresh';
```