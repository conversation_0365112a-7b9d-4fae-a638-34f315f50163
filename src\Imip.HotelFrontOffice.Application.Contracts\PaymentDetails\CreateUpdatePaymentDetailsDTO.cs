﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Imip.HotelFrontOffice.PaymentDetails;

public class CreateUpdatePaymentDetailsDto
{
    /// <summary>
    /// The ID of the payment detail (only used for updates)
    /// </summary>
    public Guid Id { get; set; }

    public Guid PaymentId { get; set; } = default!;
    public Guid ReservationDetailsId { get; set; } = default!;

    [Required]
    [EnumDataType(typeof(PaymentSourceType), ErrorMessage = "Invalid source type. Must be one of the valid payment source types.")]
    public PaymentSourceType SourceType { get; set; } = default!;

    [Required]
    public Guid SourceId { get; set; } = default!;

    [Required]
    public decimal Amount { get; set; } = default!;

    [Required]
    public decimal Qty { get; set; } = default!;
    [Required]
    public decimal UnitPrice { get; set; } = default!;
}