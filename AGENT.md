# AGENT.md for imip.hotelfrontoffice

## Build/Test Commands
- Build solution: `dotnet build Imip.HotelFrontOffice.sln`
- Run project: `dotnet run --project src/Imip.HotelFrontOffice.Web/Imip.HotelFrontOffice.Web.csproj`
- Run single test: `dotnet test --filter "FullyQualifiedName=Imip.HotelFrontOffice.{TestNamespace}.{TestClassName}.{TestMethodName}"` 
- Run all tests: `dotnet test`
- Database migration: via DbMigrator project

## Code Style Guidelines
- C# naming: PascalCase for classes, methods, properties; camelCase for variables
- Use DTOs for data transfer across layers
- Include [Required] and validation attributes on DTO properties
- Interface names prefixed with 'I' (e.g., ICustomService)
- DTO class names should include 'Dto' suffix
- Follow ABP framework conventions for service implementation
- Asynchronous methods should end with 'Async' and return Task
- Objects requiring disposal should use `using` statement
- Error handling: utilize ABP exception handling framework
- Use null-forgiving operator (!) when required (see `default!` in property initializers)
- Format code with 2-space indentation for .csproj files (per .editorconfig)
- Keep methods small, focused on a single responsibility