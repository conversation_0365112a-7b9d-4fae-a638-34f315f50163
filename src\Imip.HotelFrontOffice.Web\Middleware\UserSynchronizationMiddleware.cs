using System;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Users;
using Imip.HotelFrontOffice.Web.Extensions;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Volo.Abp;
using Volo.Abp.DependencyInjection;

namespace Imip.HotelFrontOffice.Web.Middleware;

/// <summary>
/// Middleware for synchronizing users from JWT tokens to internal database
/// </summary>
public class UserSynchronizationMiddleware : ITransientDependency
{
    private readonly RequestDelegate _next;
    private readonly ILogger<UserSynchronizationMiddleware> _logger;

    public UserSynchronizationMiddleware(RequestDelegate next, ILogger<UserSynchronizationMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            // Skip synchronization for certain paths
            if (ShouldSkipSynchronization(context.Request.Path))
            {
                await _next(context);
                return;
            }

            // Get the access token
            var accessToken = await context.GetAccessTokenWithFallbackAsync();

            if (!string.IsNullOrEmpty(accessToken))
            {
                await SynchronizeUserFromTokenAsync(context, accessToken);
            }

            await _next(context);
        }
        catch (Exception ex)
        {
            // Log the error but don't break the request pipeline
            _logger.LogError(ex, "Error in UserSynchronizationMiddleware");

            // Continue with the pipeline - user synchronization failure shouldn't break the request
            await _next(context);
        }
    }

    /// <summary>
    /// Synchronizes user from JWT token to internal database
    /// </summary>
    private async Task SynchronizeUserFromTokenAsync(HttpContext context, string token)
    {
        try
        {
            // Validate token format first
            var jwtHandler = new JwtSecurityTokenHandler();
            if (!jwtHandler.CanReadToken(token))
            {
                _logger.LogDebug("Invalid JWT token format, skipping user synchronization");
                return;
            }

            var jwtToken = jwtHandler.ReadJwtToken(token);

            // Extract user ID from token
            var userIdClaim = jwtToken.Claims.FirstOrDefault(c =>
                c.Type == "sub" ||
                c.Type == "user_id" ||
                c.Type == System.Security.Claims.ClaimTypes.NameIdentifier);

            if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
            {
                _logger.LogDebug("User ID not found in token, skipping user synchronization");
                return;
            }

            // Get the user synchronization service
            using var scope = context.RequestServices.CreateScope();
            var userSyncService = scope.ServiceProvider.GetRequiredService<IUserSynchronizationService>();

            // Check if user already exists in internal database
            var userExists = await userSyncService.UserExistsAsync(userId);

            if (!userExists)
            {
                _logger.LogInformation("User {UserId} not found in internal database, synchronizing from token", userId);

                // Synchronize user from token
                var synchronizedUser = await userSyncService.SynchronizeUserFromTokenAsync(token);

                _logger.LogInformation("Successfully synchronized user {UserId} ({UserName}) to internal database",
                    synchronizedUser.Id, synchronizedUser.UserName);
            }
            else
            {
                _logger.LogDebug("User {UserId} already exists in internal database", userId);

                // Optionally update user information if needed
                // This can be controlled by configuration or made conditional
                if (ShouldUpdateExistingUser())
                {
                    var synchronizedUser = await userSyncService.SynchronizeUserFromTokenAsync(token);
                    _logger.LogDebug("Updated user {UserId} ({UserName}) in internal database",
                        synchronizedUser.Id, synchronizedUser.UserName);
                }
            }
        }
        catch (BusinessException ex)
        {
            _logger.LogWarning(ex, "Business error during user synchronization: {Message}", ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during user synchronization");
        }
    }

    /// <summary>
    /// Determines if user synchronization should be skipped for the given path
    /// </summary>
    private static bool ShouldSkipSynchronization(PathString path)
    {
        var pathValue = path.Value?.ToLowerInvariant();

        if (string.IsNullOrEmpty(pathValue))
            return true;

        // Skip for static files and non-API endpoints
        var skipPaths = new[]
        {
            "/favicon.ico",
            "/robots.txt",
            "/_framework",
            "/css/",
            "/js/",
            "/images/",
            "/fonts/",
            "/lib/",
            "/swagger",
            "/health",
            "/metrics",
            "/connect/token", // Skip for token endpoint itself
            "/connect/userinfo",
            "/connect/authorize",
            "/.well-known"
        };

        return skipPaths.Any(skipPath => pathValue.StartsWith(skipPath));
    }

    /// <summary>
    /// Determines if existing users should be updated during synchronization
    /// This can be made configurable based on requirements
    /// </summary>
    private static bool ShouldUpdateExistingUser()
    {
        // For now, always update to keep user data fresh
        // This can be made configurable via configuration
        return true;
    }
}

/// <summary>
/// Extension methods for registering the user synchronization middleware
/// </summary>
public static class UserSynchronizationMiddlewareExtensions
{
    /// <summary>
    /// Adds the user synchronization middleware to the pipeline
    /// </summary>
    public static IApplicationBuilder UseUserSynchronization(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<UserSynchronizationMiddleware>();
    }
}
