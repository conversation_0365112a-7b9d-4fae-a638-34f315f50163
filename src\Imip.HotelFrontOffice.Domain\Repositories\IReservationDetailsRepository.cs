﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.Repositories
{
    public interface IReservationDetailsRepository : IRepository<ReservationDetails.ReservationDetail, Guid>
    {
        Task<ReservationDetails.ReservationDetail?> FindByNameAsync(string name);
    }
}