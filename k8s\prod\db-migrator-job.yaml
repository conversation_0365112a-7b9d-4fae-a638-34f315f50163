﻿apiVersion: batch/v1
kind: Job
metadata:
  name: imip-wisma-db-migrator-${CI_COMMIT_SHA}
  namespace: imip-wisma-prod
  labels:
    app: imip-wisma-db-migrator
    environment: production
spec:
  # Increased backoffLimit to allow for retries
  backoffLimit: 3
  # Increased activeDeadlineSeconds to give more time for completion
  activeDeadlineSeconds: 900
  template:
    metadata:
      labels:
        app: imip-wisma-db-migrator
        environment: production
        commit: "${CI_COMMIT_SHA}"
    spec:
      # Use nodeSelector to target the new production nodes
      nodeSelector:
        node-role.kubernetes.io/worker: "worker"
      # Add tolerations for the production nodes
      tolerations:
        - key: "node-role.kubernetes.io/worker"
          operator: "Exists"
          effect: "NoSchedule"
      # Add host aliases to resolve DNS names to specific IP addresses
      hostAliases:
        - ip: "**********"
          hostnames:
            - "api-identity.imip.co.id"
      imagePullSecrets:
        - name: gitlab-registry-credentials
      # Add securityContext at pod level
      securityContext:
        fsGroup: 1000
        runAsNonRoot: true
        seccompProfile:
          type: RuntimeDefault
      volumes:
        - name: certificate-volume
          secret:
            secretName: imip-wisma-certificate
      containers:
        - name: imip-wisma-db-migrator
          image: ${CI_REGISTRY_IMAGE}/db-migrator:${CI_COMMIT_SHA}
          resources:
            requests:
              memory: "1Gi"
              cpu: "500m"
            limits:
              memory: "2Gi"
              cpu: "1000m"
          volumeMounts:
            - name: certificate-volume
              mountPath: /app/certs
              readOnly: true
          envFrom:
            - configMapRef:
                name: imip-wisma-config
            - secretRef:
                name: imip-wisma-secrets
          env:
            - name: ASPNETCORE_ENVIRONMENT
              value: "Production"
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
          securityContext:
            runAsNonRoot: true
            runAsUser: 1000
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - ALL
            readOnlyRootFilesystem: true
      restartPolicy: Never
