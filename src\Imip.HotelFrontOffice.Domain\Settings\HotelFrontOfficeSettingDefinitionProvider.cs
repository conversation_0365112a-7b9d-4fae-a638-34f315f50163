﻿using Volo.Abp.Settings;

namespace Imip.HotelFrontOffice.Settings;

public class HotelFrontOfficeSettingDefinitionProvider : SettingDefinitionProvider
{
    public override void Define(ISettingDefinitionContext context)
    {
        // Define reservation status name settings
        context.Add(
            new SettingDefinition(
                HotelFrontOfficeSettings.ReservationStatus.CheckInStatusName,
                "Check IN",
                isVisibleToClients: true
            ),
            new SettingDefinition(
                HotelFrontOfficeSettings.ReservationStatus.CheckOutStatusName,
                "Check OUT",
                isVisibleToClients: true
            )
        );

        // Define room status name settings
        context.Add(
            new SettingDefinition(
                HotelFrontOfficeSettings.SettingRoomStatus.OccupiedInStayStatusName,
                "OVC IN Stay",
                isVisibleToClients: true
            ),
            new SettingDefinition(
                HotelFrontOfficeSettings.SettingRoomStatus.DirtyStatusName,
                "Dirty",
                isVisibleToClients: true
            )
        );
    }
}
