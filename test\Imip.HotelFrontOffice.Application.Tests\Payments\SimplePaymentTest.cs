using System;
using System.Threading.Tasks;
using Shouldly;
using Xunit;

namespace Imip.HotelFrontOffice.Payments
{
    public class SimplePaymentTest
    {
        [Fact]
        public void SimpleTest_BasicScenario_ShouldPass()
        {
            // Arrange
            var value = 1;
            
            // Act
            var result = value + 1;
            
            // Assert
            result.ShouldBe(2);
        }
        
        [Fact]
        public async Task AsyncTest_BasicScenario_ShouldPass()
        {
            // Arrange
            var value = 1;
            
            // Act
            var result = await Task.FromResult(value + 1);
            
            // Assert
            result.ShouldBe(2);
        }
    }
}
