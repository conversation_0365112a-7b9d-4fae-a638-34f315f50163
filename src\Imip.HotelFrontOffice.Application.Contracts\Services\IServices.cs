﻿using System;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Models;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.HotelFrontOffice.Services
{
    public interface IServicesAppService : IApplicationService
    {
        Task<ServicesDto> CreateAsync(CreateUpdateServicesDto input);

        /// <summary>
        /// Get a service by ID
        /// </summary>
        Task<ServicesDto> GetAsync(Guid id);
    }
}