﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.HotelFrontOffice.ReservationFoodAndBeverages
{
    public interface IReservationFoodAndBeveragesAppService : ICrudAppService<
        ReservationFoodAndBeveragesDto,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdateReservationFoodAndBeveragesDto,
        CreateUpdateReservationFoodAndBeveragesDto>
    {
        // The base ICrudAppService already includes:
        // Task<ReservationFoodAndBeveragesDto> CreateAsync(CreateUpdateReservationFoodAndBeveragesDto input);
        // Task<ReservationFoodAndBeveragesDto> UpdateAsync(Guid id, CreateUpdateReservationFoodAndBeveragesDto input);

        // Add bulk operation methods
        /// <summary>
        /// Creates or updates multiple reservation food and beverage items in a single operation.
        /// If an item has a non-empty Id and the entity exists, it will be updated.
        /// If an item has a non-empty Id but the entity doesn't exist, or if the Id is empty, a new entity will be created.
        /// </summary>
        /// <param name="items">List of items to create or update</param>
        /// <returns>A sample of the created/updated entities</returns>
        Task<ReservationFoodAndBeveragesDto> CreateManyAsync(List<CreateUpdateReservationFoodAndBeveragesDto> items);

        /// <summary>
        /// Updates multiple existing reservation food and beverage items in a single operation.
        /// All items must have a valid Id that corresponds to an existing entity.
        /// </summary>
        /// <param name="items">List of items to update</param>
        /// <returns>A sample of the updated entities</returns>
        Task<ReservationFoodAndBeveragesDto> UpdateManyAsync(List<CreateUpdateReservationFoodAndBeveragesDto> items);
    }
}