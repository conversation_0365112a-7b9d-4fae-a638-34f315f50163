﻿using System;
using System.Collections.Generic;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.HotelFrontOffice.Companies;

public class Company: FullAuditedAggregateRoot<Guid>
{
    public string Name { get; set; } = default!;
    public string? Address { get; set; }
    public string Alias { get; set; } = default!;
    
    public virtual ICollection<Reservations.Reservation>? Reservations { get; set; }

    protected Company()
    {
        Reservations = new HashSet<Reservations.Reservation>();
    }
    
    public Company(Guid id, string name, string alias, string? address = null)
    {
        Id = id;
        Name = name;
        Alias = alias;
        Address = address;
    }
}