﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;

namespace Imip.HotelFrontOffice.RoomStatusLogs
{
    public class RoomStatusLogsDto : AuditedEntityDto<Guid>
    {
        public Guid RoomId { get; set; } = default!;
        public Guid RoomStatusId { get; set; } = default!;
        public string StatusSource { get; set; } = default!;
        public string RoomStatusName { get; set; } = default!;
        public string RoomNumber { get; set; } = default!;
        public string RoomCode { get; set; } = default!;
        
    }
}