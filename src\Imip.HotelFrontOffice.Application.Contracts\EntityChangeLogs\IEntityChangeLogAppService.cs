using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.EntityChangeLogs.Dtos;
using Imip.HotelFrontOffice.Models;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.HotelFrontOffice.EntityChangeLogs;

/// <summary>
/// Service interface for entity change logs
/// </summary>
public interface IEntityChangeLogAppService : IApplicationService
{
    /// <summary>
    /// Gets entity change logs for a specific entity
    /// </summary>
    /// <param name="input">Input parameters</param>
    /// <returns>Paged list of entity change logs</returns>
    Task<PagedResultDto<EntityChangeLogDto>> GetEntityChangeLogsAsync(GetEntityChangeLogInput input);

    /// <summary>
    /// Gets entity change logs with dynamic filtering and sorting
    /// </summary>
    /// <param name="input">Query parameters including filtering and sorting</param>
    /// <returns>Paged list of entity change logs</returns>
    Task<PagedResultDto<EntityChangeLogDto>> GetEntityChangeLogsWithDynamicQueryAsync(QueryParameters input);

    /// <summary>
    /// Gets the latest entity change log for a specific entity
    /// </summary>
    /// <param name="entityId">The entity ID</param>
    /// <param name="entityTypeFullName">The full type name of the entity</param>
    /// <returns>The latest entity change log or null</returns>
    Task<EntityChangeLogDto> GetLatestEntityChangeLogAsync(string entityId, string entityTypeFullName);

    /// <summary>
    /// Gets entity change logs for a specific entity type
    /// </summary>
    /// <param name="entityTypeFullName">The full type name of the entity</param>
    /// <param name="maxResultCount">Maximum number of results to return</param>
    /// <param name="skipCount">Number of results to skip</param>
    /// <returns>List of entity change logs</returns>
    Task<List<EntityChangeLogDto>> GetEntityTypeChangeLogsAsync(
        string entityTypeFullName,
        int maxResultCount = 10,
        int skipCount = 0);

    /// <summary>
    /// Gets entity change logs within a specific time range
    /// </summary>
    /// <param name="startTime">Start time</param>
    /// <param name="endTime">End time</param>
    /// <param name="maxResultCount">Maximum number of results to return</param>
    /// <param name="skipCount">Number of results to skip</param>
    /// <returns>List of entity change logs</returns>
    Task<List<EntityChangeLogDto>> GetTimeRangeChangeLogsAsync(
        DateTime startTime,
        DateTime endTime,
        int maxResultCount = 10,
        int skipCount = 0);

    /// <summary>
    /// Gets entity change logs for a specific entity property
    /// </summary>
    /// <param name="entityId">The entity ID</param>
    /// <param name="entityTypeFullName">The full type name of the entity</param>
    /// <param name="propertyName">The property name</param>
    /// <param name="maxResultCount">Maximum number of results to return</param>
    /// <param name="skipCount">Number of results to skip</param>
    /// <returns>List of entity change logs</returns>
    Task<List<EntityPropertyChangeLogDto>> GetPropertyChangeHistoryAsync(
        string entityId,
        string entityTypeFullName,
        string propertyName,
        int maxResultCount = 10,
        int skipCount = 0);

    /// <summary>
    /// Manually processes audit logs for testing purposes
    /// </summary>
    /// <param name="startTime">Start time for processing audit logs</param>
    /// <param name="endTime">End time for processing audit logs</param>
    /// <returns>Number of processed audit logs</returns>
    Task<int> ProcessAuditLogsManuallyAsync(DateTime startTime, DateTime endTime);

    /// <summary>
    /// Gets the raw ABP audit logs for debugging purposes
    /// </summary>
    /// <param name="startTime">Start time for filtering audit logs</param>
    /// <param name="endTime">End time for filtering audit logs</param>
    /// <param name="maxResultCount">Maximum number of results to return</param>
    /// <returns>List of audit logs</returns>
    Task<List<object>> GetRawAuditLogsAsync(DateTime startTime, DateTime endTime, int maxResultCount = 10);

    /// <summary>
    /// Gets the ABP audit logs with all related details (AuditLogActions, EntityChanges, EntityPropertyChanges)
    /// </summary>
    /// <param name="startTime">Start time for filtering audit logs</param>
    /// <param name="endTime">End time for filtering audit logs</param>
    /// <param name="maxResultCount">Maximum number of results to return</param>
    /// <param name="skipCount">Number of results to skip</param>
    /// <returns>List of audit logs with all details</returns>
    Task<PagedResultDto<AuditLogWithDetailsDto>> GetAuditLogsWithDetailsAsync(
        DateTime startTime,
        DateTime endTime,
        int maxResultCount = 10,
        int skipCount = 0);

    /// <summary>
    /// Gets entity changes with property changes by entity ID with pagination
    /// </summary>
    /// <param name="input">Input parameters including entityId and pagination</param>
    /// <returns>Paged list of entity changes with property changes</returns>
    Task<PagedResultDto<EntityChangeLogDto>> GetEntityChangesByIdAsync(GetEntityChangesByIdInput input);
}