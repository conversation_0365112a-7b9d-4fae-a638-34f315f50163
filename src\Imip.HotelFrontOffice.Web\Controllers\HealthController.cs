using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace Imip.HotelFrontOffice.Web.Controllers
{
    [Route("api/health")]
    [ApiController]
    public class HealthController : ControllerBase
    {
        [HttpGet]
        [Route("kubernetes")]
        [AllowAnonymous]
        public IActionResult KubernetesHealthCheck()
        {
            // Simple health check endpoint for Kubernetes probes
            return Ok(new { status = "healthy" });
        }
    }
}
