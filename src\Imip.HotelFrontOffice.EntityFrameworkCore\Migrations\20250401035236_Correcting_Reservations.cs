﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.HotelFrontOffice.Migrations
{
    /// <inheritdoc />
    public partial class Correcting_Reservations : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AppReservationTypes",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Status = table.Column<short>(type: "smallint", nullable: false, defaultValue: (short)1),
                    ExtraProperties = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ConcurrencyStamp = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AppReservationTypes", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "AppReservations",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ReservationCode = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    GroupCode = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    BookerName = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    BookerIdentityNumber = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    BookerPhoneNumber = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    BookerEmail = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    CompanyName = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    ArrivalDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    PaymentMethod = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    Days = table.Column<int>(type: "int", nullable: false),
                    DiningOptions = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    Attachment = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    Status = table.Column<short>(type: "smallint", nullable: false, defaultValue: (short)1),
                    ReservationTypeId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ExtraProperties = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ConcurrencyStamp = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AppReservations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AppReservations_AppReservationTypes_ReservationTypeId",
                        column: x => x.ReservationTypeId,
                        principalTable: "AppReservationTypes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "AppReservationDetails",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CheckInDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CheckOutDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Rfid = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    Price = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    ReservationId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    RoomId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    GuestId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ExtraProperties = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ConcurrencyStamp = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AppReservationDetails", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AppReservationDetails_AppGuests_GuestId",
                        column: x => x.GuestId,
                        principalTable: "AppGuests",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AppReservationDetails_AppReservations_ReservationId",
                        column: x => x.ReservationId,
                        principalTable: "AppReservations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_AppReservationDetails_AppRoom_RoomId",
                        column: x => x.RoomId,
                        principalTable: "AppRoom",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationDetails_GuestId",
                table: "AppReservationDetails",
                column: "GuestId");

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationDetails_Id",
                table: "AppReservationDetails",
                column: "Id");

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationDetails_ReservationId",
                table: "AppReservationDetails",
                column: "ReservationId");

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationDetails_RoomId",
                table: "AppReservationDetails",
                column: "RoomId");

            migrationBuilder.CreateIndex(
                name: "IX_AppReservations_ReservationCode",
                table: "AppReservations",
                column: "ReservationCode");

            migrationBuilder.CreateIndex(
                name: "IX_AppReservations_ReservationTypeId",
                table: "AppReservations",
                column: "ReservationTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationTypes_Name",
                table: "AppReservationTypes",
                column: "Name");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AppReservationDetails");

            migrationBuilder.DropTable(
                name: "AppReservations");

            migrationBuilder.DropTable(
                name: "AppReservationTypes");
        }
    }
}
