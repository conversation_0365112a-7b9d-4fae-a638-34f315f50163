# SFTP Blob Storage Provider for ABP Framework

This is a custom blob storage provider for ABP Framework that uses SFTP to store files.

## Installation

The provider is already included in the project. It uses the SSH.NET library for SFTP operations.

## Configuration

The SFTP provider is configured using the `appsettings.json` file. Add the following configuration to your `appsettings.json`:

```json
"BlobStoring": {
  "Default": {
    "Type": "SFTP",
    "SFTP": {
      "Host": "your-sftp-server.com",
      "Port": 22,
      "UserName": "your-username",
      "Password": "your-password",
      "PrivateKeyPath": "",
      "PrivateKeyPassphrase": "",
      "BaseDirectory": "/path/to/your/directory",
      "ConnectionTimeout": 30000,
      "OperationTimeout": 60000,
      "BufferSize": 4096,
      "CreateDirectoryIfNotExists": true
    }
  }
}
```

Then, in your module's `ConfigureServices` method, register the SFTP provider:

```csharp
Configure<AbpBlobStoringOptions>(options =>
{
    // Configure the default container to use SFTP provider
    options.Containers.ConfigureDefault(container =>
    {
        container.ProviderType = typeof(SftpBlobProvider);
    });
});
```

## Configuration Options

The SFTP provider supports the following configuration options in `appsettings.json`:

- `Host`: SFTP server host name or IP address.
- `Port`: SFTP server port. Default is 22.
- `UserName`: Username for SFTP authentication.
- `Password`: Password for SFTP authentication.
- `PrivateKeyPath`: Private key file path for SFTP authentication.
- `PrivateKeyPassphrase`: Passphrase for the private key if it's encrypted.
- `BaseDirectory`: Base directory on the SFTP server where files will be stored. Default is "/".
- `ConnectionTimeout`: Connection timeout in milliseconds. Default is 30000 (30 seconds).
- `OperationTimeout`: Operation timeout in milliseconds. Default is 60000 (60 seconds).
- `BufferSize`: Buffer size for file operations in bytes. Default is 4096 bytes.
- `CreateDirectoryIfNotExists`: Whether to create directories automatically if they don't exist. Default is true.

## Usage

Inject `IBlobContainer` or `IBlobContainerFactory` into your service and use it to save, get, check existence, or delete files:

```csharp
public class MyService
{
    private readonly IBlobContainer _blobContainer;

    public MyService(IBlobContainerFactory blobContainerFactory)
    {
        // Get the default container
        _blobContainer = blobContainerFactory.Create();

        // Or get a named container
        // _blobContainer = blobContainerFactory.Create("YourContainerName");
    }

    public async Task SaveFileAsync(string fileName, byte[] content)
    {
        using var stream = new MemoryStream(content);
        await _blobContainer.SaveAsync(fileName, stream);
    }

    public async Task<byte[]> GetFileAsync(string fileName)
    {
        var stream = await _blobContainer.GetAsync(fileName);
        using var memoryStream = new MemoryStream();
        await stream.CopyToAsync(memoryStream);
        return memoryStream.ToArray();
    }

    public async Task<bool> FileExistsAsync(string fileName)
    {
        return await _blobContainer.ExistsAsync(fileName);
    }

    public async Task DeleteFileAsync(string fileName)
    {
        await _blobContainer.DeleteAsync(fileName);
    }
}
```

See the `SftpBlobStorageExampleService` class for a complete example.

## Notes

- The provider creates directories automatically if they don't exist (configurable).
- Files are stored in the format `{BaseDirectory}/{ContainerName}/{BlobName}`.
- Authentication can be done using either username/password or private key.
- All operations are asynchronous.

## Dependencies

- [SSH.NET](https://github.com/sshnet/SSH.NET): A Secure Shell (SSH) library for .NET.
- [ABP Framework](https://abp.io): A complete infrastructure for building modern web applications.
