using System;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Permissions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.Guests;

[Authorize(WismaAppPermissions.PolicyGuest.Default)]
public class GuestAppService : PermissionCheckedCrudAppService<
    Guest,
    GuestDto,
    Guid,
    PagedAndSortedResultRequestDto,
    CreateUpdateGuestDto,
    CreateUpdateGuestDto
>, IGuestAppService
{
    private readonly IRepository<Guest, Guid> _repository;

    public GuestAppService(
        IRepository<Guest, Guid> repository,
        IPermissionChecker permissionChecker)
        : base(repository, permissionChecker)
    {
        _repository = repository;

        GetPolicyName = WismaAppPermissions.PolicyGuest.View;
        GetListPolicyName = WismaAppPermissions.PolicyGuest.View;
        CreatePolicyName = WismaAppPermissions.PolicyGuest.Create;
        UpdatePolicyName = WismaAppPermissions.PolicyGuest.Edit;
        DeletePolicyName = WismaAppPermissions.PolicyGuest.Delete;
    }

    protected override async Task<IQueryable<Guest>> CreateFilteredQueryAsync(
        PagedAndSortedResultRequestDto input)
    {
        return (await base.CreateFilteredQueryAsync(input));
    }

    public override async Task<GuestDto> GetAsync(Guid id)
    {
        var query = await Repository.GetQueryableAsync();
        var guest = await query
            .FirstOrDefaultAsync(x => x.Id == id);

        if (guest == null)
        {
            throw new EntityNotFoundException(typeof(Guest), id);
        }

        return ObjectMapper.Map<Guest, GuestDto>(guest);
    }
}