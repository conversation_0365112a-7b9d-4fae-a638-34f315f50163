using Microsoft.Extensions.DependencyInjection;
using System;
using Volo.Abp.AuditLogging;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;

namespace Imip.HotelFrontOffice.EntityChangeLogs
{
    [DependsOn(
        typeof(AbpAuditLoggingDomainModule)
    )]
    public class EntityChangeLogModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            // No need to register repositories explicitly as they are already registered by ABP
            // The EntityChange and EntityPropertyChange repositories are automatically registered
            // by the AbpAuditLoggingDomainModule
        }
    }
}
