using System;
using System.Collections.Generic;
using Imip.HotelFrontOffice.Guests;

namespace Imip.HotelFrontOffice.Reservations;

public class CreateReservationWithDetailsDto
{
    public string GroupCode { get; set; } = default!;
    public string BookerName { get; set; } = default!;
    public string BookerIdentityNumber { get; set; } = default!;
    public string BookerPhoneNumber { get; set; } = default!;
    public string? BookerEmail { get; set; }
    public DateTime ArrivalDate { get; set; }
    public int Days { get; set; }
    public string Attachment { get; set; } = default!;
    public Guid? CompanyId { get; set; } = default!;
    public Guid? StatusId { get; set; } = default!;
    public Guid? PaymentMethodId { get; set; } = default!;
    public Guid? DiningOptionsId { get; set; } = default!;
    public Guid ReservationTypeId { get; set; }
    public List<CreateReservationDetailDto> ReservationDetails { get; set; } = new();
}

public class CreateReservationDetailDto
{
    public string Rfid { get; set; } = default!;
    public decimal? Price { get; set; }
    public DateTime? CheckInDate { get; set; }
    public DateTime? CheckOutDate { get; set; }
    public Guid RoomId { get; set; }
    public Guid? GuestId { get; set; }
    public Guid? CriteriaId { get; set; }

    public Guid StatusId { get; set; }
    public CreateReservationGuestDto? GuestData { get; set; }
}

public class CreateReservationGuestDto
{
    public string Fullname { get; set; } = default!;
    public string IdentityNumber { get; set; } = default!;
    public string PhoneNumber { get; set; } = default!;
    public string Email { get; set; } = default!;
    public string Nationality { get; set; } = default!;
    public string CompanyName { get; set; } = default!;

    /// <summary>
    /// Legacy attachment field (for backward compatibility)
    /// </summary>
    public string Attachment { get; set; } = string.Empty;

    /// <summary>
    /// Array of attachments for the guest (e.g., identification documents, photos)
    /// </summary>
    public List<GuestAttachmentDto>? Attachments { get; set; }

    public Guid? StatusId { get; set; }
}