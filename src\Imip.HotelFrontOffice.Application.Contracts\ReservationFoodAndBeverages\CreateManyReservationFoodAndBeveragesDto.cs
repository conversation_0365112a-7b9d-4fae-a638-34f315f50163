using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Imip.HotelFrontOffice.ReservationFoodAndBeverages
{
    /// <summary>
    /// DTO for creating multiple reservation food and beverages in a single request
    /// </summary>
    public class CreateManyReservationFoodAndBeveragesDto
    {
        /// <summary>
        /// List of food and beverage items to create
        /// </summary>
        [Required]
        public List<CreateUpdateReservationFoodAndBeveragesDto> Items { get; set; } = new List<CreateUpdateReservationFoodAndBeveragesDto>();
    }
}
