﻿using System;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Models;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.HotelFrontOffice.Payments;

public interface IPaymentsAppService : IApplicationService
{
    Task<PaymentsDto> CreateAsync(CreateUpdatePaymentsDto input);

    /// <summary>
    /// Cleans up all attachments for a payment, keeping only the latest one for each reference type
    /// </summary>
    /// <param name="paymentId">The payment ID</param>
    /// <returns>True if the cleanup was successful</returns>
    Task<bool> CleanupPaymentAttachmentsAsync(Guid paymentId);
}