using System;
using Volo.Abp.Application.Dtos;

namespace Imip.HotelFrontOffice.EntityChangeLogs.Dtos
{
    /// <summary>
    /// DTO for entity property change logs
    /// </summary>
    public class EntityPropertyChangeLogDto : EntityDto<Guid>
    {
        /// <summary>
        /// ID of the related entity change log
        /// </summary>
        public Guid EntityChangeLogId { get; set; }

        /// <summary>
        /// Name of the property that was changed
        /// </summary>
        public string? PropertyName { get; set; }

        /// <summary>
        /// User-friendly display name of the property
        /// </summary>
        public string? PropertyDisplayName { get; set; }

        /// <summary>
        /// Original value of the property before the change
        /// </summary>
        public string? OriginalValue { get; set; }

        /// <summary>
        /// New value of the property after the change
        /// </summary>
        public string? NewValue { get; set; }

        /// <summary>
        /// Full type name of the property
        /// </summary>
        public string? PropertyTypeFullName { get; set; }

        /// <summary>
        /// Original value display name (for reference properties like StatusId)
        /// </summary>
        public string? OriginalValueDisplay { get; set; }

        /// <summary>
        /// New value display name (for reference properties like StatusId)
        /// </summary>
        public string? NewValueDisplay { get; set; }

        /// <summary>
        /// Indicates if this property is a reference to another entity
        /// </summary>
        public bool IsReference { get; set; }
    }
}
