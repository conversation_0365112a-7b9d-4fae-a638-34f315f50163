﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.RoomStatuses;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.Repositories
{
    public interface IRoomStatusesRepository : IRepository<RoomStatus, Guid>
    {
        Task<RoomStatus?> FindByNameAsync(string name);

        Task<List<RoomStatus>> GetActiveAsync();
    }
}