﻿using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.HotelFrontOffice.ReservationTypes
{
    public interface IReservationTypesAppService : ICrudAppService<
        ReservationTypesDto,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdateReservationTypesDto,
        CreateUpdateReservationTypesDto
    >
    {
        new Task<ReservationTypesDto> CreateAsync(CreateUpdateReservationTypesDto input);
        new Task<ReservationTypesDto> UpdateAsync(Guid id, CreateUpdateReservationTypesDto input);
    }
}