@page
@model Imip.HotelFrontOffice.Web.Pages.FileUploadModel
@{
    Layout = null;
}

<!DOCTYPE html>
<html>

<head>
    <meta name="viewport" content="width=device-width" />
    <title>File Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        h1 {
            color: #333;
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        input[type="text"],
        input[type="file"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }

        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        button:hover {
            background-color: #45a049;
        }

        .result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
            display: none;
        }

        pre {
            white-space: pre-wrap;
            word-wrap: break-word;
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }

        .preview {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
            display: none;
        }

        .preview-content {
            max-width: 100%;
            max-height: 500px;
            margin-top: 10px;
        }

        .preview-frame {
            width: 100%;
            height: 500px;
            border: 1px solid #ddd;
        }

        .file-list {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
            display: none;
        }

        .file-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            padding: 10px;
            border: 1px solid #eee;
            border-radius: 4px;
        }

        .file-item input[type="checkbox"] {
            margin-right: 10px;
        }

        .file-info {
            flex-grow: 1;
        }

        .file-name {
            font-weight: bold;
        }

        .file-meta {
            font-size: 0.8em;
            color: #666;
        }

        .file-actions {
            display: flex;
            gap: 10px;
        }

        .bulk-actions {
            margin-top: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .select-all {
            display: flex;
            align-items: center;
        }

        .select-all input {
            margin-right: 5px;
        }

        .download-zip {
            background-color: #4CAF50;
            color: white;
            padding: 8px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .download-zip:hover {
            background-color: #45a049;
        }

        .download-zip:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>File Upload Test</h1>

        <form id="uploadForm" enctype="multipart/form-data">
            <div class="form-group">
                <label for="file">Select File:</label>
                <input type="file" id="file" name="file" required />
            </div>

            <div class="form-group">
                <label for="description">Description:</label>
                <input type="text" id="description" name="description" />
            </div>

            <div class="form-group">
                <label for="referenceId">Reference ID (optional):</label>
                <input type="text" id="referenceId" name="referenceId" placeholder="Enter a valid GUID" />
            </div>

            <div class="form-group">
                <label for="referenceType">Reference Type (optional):</label>
                <input type="text" id="referenceType" name="referenceType" placeholder="e.g., Reservation, Guest" />
            </div>

            <button type="submit">Upload</button>
        </form>

        <div id="result" class="result">
            <h3>Upload Result:</h3>
            <pre id="resultContent"></pre>
        </div>

        <div id="preview" class="preview">
            <h3>File Preview:</h3>
            <div id="previewContent" class="preview-content"></div>
        </div>

        <div id="fileList" class="file-list">
            <h3>Uploaded Files:</h3>
            <div id="fileListContent"></div>
            <div class="bulk-actions">
                <div class="select-all">
                    <input type="checkbox" id="selectAll" />
                    <label for="selectAll">Select All</label>
                </div>
                <button id="downloadZip" class="download-zip" disabled>Download Selected as ZIP</button>
            </div>
        </div>
    </div>

    <script>
        // Store uploaded files
        const uploadedFiles = [];

        // Handle form submission for file upload
        document.getElementById('uploadForm').addEventListener('submit', async function (e) {
            e.preventDefault();

            const formData = new FormData(this);
            const file = formData.get('file');

            // Read the file as base64
            const reader = new FileReader();

            reader.onload = async function () {
                // Convert base64 to byte array
                const base64 = reader.result.split(',')[1];
                const binaryString = window.atob(base64);
                const bytes = new Uint8Array(binaryString.length);
                for (let i = 0; i < binaryString.length; i++) {
                    bytes[i] = binaryString.charCodeAt(i);
                }

                // Create the input object
                const input = {
                    description: formData.get('description'),
                    referenceId: formData.get('referenceId') ? formData.get('referenceId') : null,
                    referenceType: formData.get('referenceType'),
                    fileName: file.name,
                    contentType: file.type,
                    fileContent: Array.from(bytes)
                };

                try {
                    const response = await fetch('/api/document/attachment/upload', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(input)
                    });

                    const result = await response.json();

                    document.getElementById('resultContent').textContent = JSON.stringify(result, null, 2);
                    document.getElementById('result').style.display = 'block';

                    // Show preview if available
                    if (result && result.streamUrl) {
                        const previewContent = document.getElementById('previewContent');
                        const previewContainer = document.getElementById('preview');

                        // Clear previous preview
                        previewContent.innerHTML = '';

                        // Check file type for appropriate preview
                        const contentType = result.contentType.toLowerCase();

                        if (contentType.startsWith('image/')) {
                            // Image preview
                            const img = document.createElement('img');
                            img.src = result.streamUrl;
                            img.alt = result.fileName;
                            img.style.maxWidth = '100%';
                            previewContent.appendChild(img);
                        } else if (contentType === 'application/pdf') {
                            // PDF preview
                            const iframe = document.createElement('iframe');
                            iframe.src = result.streamUrl;
                            iframe.className = 'preview-frame';
                            previewContent.appendChild(iframe);
                        } else if (contentType.startsWith('text/')) {
                            // Text preview
                            const iframe = document.createElement('iframe');
                            iframe.src = result.streamUrl;
                            iframe.className = 'preview-frame';
                            previewContent.appendChild(iframe);
                        } else if (contentType.startsWith('video/')) {
                            // Video preview
                            const video = document.createElement('video');
                            video.src = result.streamUrl;
                            video.controls = true;
                            video.style.maxWidth = '100%';
                            previewContent.appendChild(video);
                        } else if (contentType.startsWith('audio/')) {
                            // Audio preview
                            const audio = document.createElement('audio');
                            audio.src = result.streamUrl;
                            audio.controls = true;
                            audio.style.width = '100%';
                            previewContent.appendChild(audio);
                        } else {
                            // For other file types, provide a link
                            const link = document.createElement('a');
                            link.href = result.streamUrl;
                            link.textContent = `View ${result.fileName}`;
                            link.target = '_blank';
                            previewContent.appendChild(link);
                        }

                        previewContainer.style.display = 'block';
                    }

                    // Add file to the list
                    uploadedFiles.push(result);
                    updateFileList();

                } catch (error) {
                    document.getElementById('resultContent').textContent = 'Error: ' + error.message;
                    document.getElementById('result').style.display = 'block';
                }
            };

            // Start reading the file
            reader.readAsDataURL(file);

        } catch (error) {
            document.getElementById('resultContent').textContent = 'Error: ' + error.message;
            document.getElementById('result').style.display = 'block';
        }

        // Update the file list display
        function updateFileList() {
            const fileListContent = document.getElementById('fileListContent');
            const fileListContainer = document.getElementById('fileList');
            const downloadZipButton = document.getElementById('downloadZip');

            // Clear the list
            fileListContent.innerHTML = '';

            if (uploadedFiles.length === 0) {
                fileListContainer.style.display = 'none';
                return;
            }

            // Show the file list container
            fileListContainer.style.display = 'block';

            // Add each file to the list
            uploadedFiles.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';

                // Checkbox for selection
                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.id = `file-${index}`;
                checkbox.dataset.fileId = file.id;
                checkbox.addEventListener('change', updateDownloadButton);

                // File info
                const fileInfo = document.createElement('div');
                fileInfo.className = 'file-info';

                const fileName = document.createElement('div');
                fileName.className = 'file-name';
                fileName.textContent = file.fileName;

                const fileMeta = document.createElement('div');
                fileMeta.className = 'file-meta';
                fileMeta.textContent = `${formatFileSize(file.size)} • ${new Date(file.uploadTime).toLocaleString()}`;

                fileInfo.appendChild(fileName);
                fileInfo.appendChild(fileMeta);

                // File actions
                const fileActions = document.createElement('div');
                fileActions.className = 'file-actions';

                // Download link
                const downloadLink = document.createElement('a');
                downloadLink.href = file.url;
                downloadLink.textContent = 'Download';
                downloadLink.target = '_blank';

                // View link
                const viewLink = document.createElement('a');
                viewLink.href = file.streamUrl;
                viewLink.textContent = 'View';
                viewLink.target = '_blank';

                fileActions.appendChild(downloadLink);
                fileActions.appendChild(viewLink);

                // Add all elements to the file item
                fileItem.appendChild(checkbox);
                fileItem.appendChild(fileInfo);
                fileItem.appendChild(fileActions);

                // Add the file item to the list
                fileListContent.appendChild(fileItem);
            });

            // Update the download button state
            updateDownloadButton();
        }

        // Format file size in a human-readable format
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';

            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));

            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Handle select all checkbox
        document.getElementById('selectAll').addEventListener('change', function () {
            const checkboxes = document.querySelectorAll('#fileListContent input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });

            updateDownloadButton();
        });

        // Update download button state
        function updateDownloadButton() {
            const checkboxes = document.querySelectorAll('#fileListContent input[type="checkbox"]:checked');
            const downloadZipButton = document.getElementById('downloadZip');

            downloadZipButton.disabled = checkboxes.length === 0;
        }

        // Handle download as zip button
        document.getElementById('downloadZip').addEventListener('click', async function () {
            const checkboxes = document.querySelectorAll('#fileListContent input[type="checkbox"]:checked');

            if (checkboxes.length === 0) {
                return;
            }

            // Get selected file IDs
            const fileIds = Array.from(checkboxes).map(checkbox => checkbox.dataset.fileId);

            try {
                // Create the request payload
                const payload = {
                    fileIds: fileIds,
                    zipFileName: 'selected_files'
                };

                // Make a fetch request to the bulk download endpoint
                const response = await fetch('/api/document/attachment/bulk-download', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(payload)
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                // Get the blob from the response
                const blob = await response.blob();

                // Create a URL for the blob
                const url = window.URL.createObjectURL(blob);

                // Create a link to download the file
                const a = document.createElement('a');
                a.href = url;
                a.download = response.headers.get('Content-Disposition')?.split('filename=')[1]?.replace(/"/g, '') || 'attachments.zip';
                document.body.appendChild(a);

                // Click the link to download the file
                a.click();

                // Clean up
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
            } catch (error) {
                console.error('Error downloading files:', error);
                alert('Error downloading files: ' + error.message);
            }
        });
    </script>
</body>

</html>
