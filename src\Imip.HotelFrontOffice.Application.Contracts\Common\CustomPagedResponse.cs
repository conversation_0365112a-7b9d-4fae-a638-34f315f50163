using System.Collections.Generic;

namespace Imip.HotelFrontOffice.Common
{
    public class CustomPagedResponse<T>
    {
        public MetaData? Meta { get; set; }
        public bool Success { get; set; }
        public string? Message { get; set; }
        public T? Data { get; set; }
    }

    public class MetaData
    {
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
        public int TotalItems { get; set; }
    }
}