﻿using System;
using System.Collections.Generic;
using Imip.HotelFrontOffice.MasterStatuses;
using Volo.Abp.Application.Dtos;

namespace Imip.HotelFrontOffice.Guests;

public class GuestDto : AuditedEntityDto<Guid>
{
    public string Fullname { get; set; } = default!;
    public string? Email { get; set; }
    public string? PhoneNumber { get; set; }
    public string? IdentityNumber { get; set; }
    public string? CompanyName { get; set; }
    public string? Nationality { get; set; }
    public Guid? StatusId { get; set; }
    /// <summary>
    /// Legacy attachment field (for backward compatibility)
    /// </summary>
    public string? Attachment { get; set; }

    /// <summary>
    /// List of attachments associated with this guest
    /// </summary>
    public List<GuestAttachmentInfoDto>? Attachments { get; set; }

    public MasterStatusDto Status { get; set; } = default!;
    // Add other guest properties you need
}