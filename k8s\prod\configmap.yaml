﻿apiVersion: v1
kind: ConfigMap
metadata:
  name: imip-wisma-config
  namespace: imip-wisma-prod
data:
  ASPNETCORE_ENVIRONMENT: "Production"
  App__SelfUrl: "${PROD_APP_URL}"
  App__ServerRootAddress: "${PROD_APP_URL}"
  App__ClientUrl: "${PROD_CLIENT_URL}"
  App__CorsOrigins: "${PROD_CORS_ORIGINS}"
  App__HealthCheckUrl: "/api/health/kubernetes"
  Seq__ServerUrl: "${SEQ_SERVER_URL}"
  AuthServer__Authority: "${PROD_AUTH_APP_URL}"
  AuthServer__RequireHttpsMetadata: "true"
  ExternalAuth__ApiUrl: "${PROD_EXTERNAL_AUTH_URL}"
  ExternalAuth__Enabled: "true"
  AuthServer__CertificatePath: "/app/certs/identity-server.pfx"
  Redis__IsEnabled: "true"
  DocumentConversion__MaxConcurrentConversions: "4"
  PdfOptimization__EmbedFonts: "false"
  PdfOptimization__OptimizeIdenticalImages: "true"
  PdfOptimization__CompressionLevel: "Best"
  PdfOptimization__MaxFileSizeWarningMB: "5.0"
  PdfOptimization__MaxFileSizeForPostProcessingMB: "2.0"
  PdfOptimization__UseMinimalFonts: "true"
  PdfOptimization__DefaultFontFamily: "Arial"
  Redis__Configuration: "**********:6379,abortConnect=false,connectTimeout=30000,syncTimeout=30000,connectRetry=10,keepAlive=60,allowAdmin=true,responseTimeout=30000"
  # User Synchronization Configuration for Production
  UserSynchronization__IsEnabled: "${PROD_USER_SYNC_ENABLED}"
  UserSynchronization__UpdateExistingUsers: "${PROD_USER_SYNC_UPDATE_EXISTING}"
  UserSynchronization__SynchronizeRoles: "${PROD_USER_SYNC_ROLES}"
  UserSynchronization__SynchronizeClaims: "${PROD_USER_SYNC_CLAIMS}"
  UserSynchronization__EnableLogging: "${PROD_USER_SYNC_LOGGING}"
