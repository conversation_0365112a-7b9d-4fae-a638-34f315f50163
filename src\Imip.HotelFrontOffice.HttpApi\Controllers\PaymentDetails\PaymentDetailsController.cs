using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Models;
using Imip.HotelFrontOffice.PaymentDetails;
using Imip.HotelFrontOffice.Permissions;
using Imip.HotelFrontOffice.ReservationFoodAndBeverages;
using Imip.HotelFrontOffice.ReservationRooms;
using Imip.HotelFrontOffice.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.Controllers.PaymentDetails;

[Route("api/app/payment-details")]
[RemoteService]
public class PaymentDetailsController : HotelFrontOfficeController
{
    private readonly IPaymentDetailsAppService _paymentDetailsAppService;
    private readonly IRepository<PaymentDetail> _repository;
    private readonly IRepository<ReservationFoodAndBeverage> _reservationFoodAndBeverageRepository;
    private readonly IRepository<ReservationRoom> _reservationRoomRepository;
    private readonly ILogger<PaymentDetailsController> _logger;

    public PaymentDetailsController(
        IPaymentDetailsAppService paymentDetailsAppService,
        IRepository<PaymentDetail> repository,
        IRepository<ReservationFoodAndBeverage> reservationFoodAndBeverageRepository,
        IRepository<ReservationRoom> reservationRoomRepository,
        ILogger<PaymentDetailsController> logger)
    {
        _paymentDetailsAppService = paymentDetailsAppService;
        _repository = repository;
        _reservationFoodAndBeverageRepository = reservationFoodAndBeverageRepository;
        _reservationRoomRepository = reservationRoomRepository;
        _logger = logger;
    }

    /// <summary>
    /// Get a paged list of reports with dynamic filtering and sorting
    /// </summary>
    /// <param name="input">Query parameters including filtering and sorting</param>
    /// <returns>Paged list of reports in standard ABP format</returns>
    [HttpPost("list")]
    [Authorize(WismaAppPermissions.PolicyPaymentDetails.View)]
    [ProducesResponseType(typeof(PagedResultDto<PaymentDetailsDto>), StatusCodes.Status200OK)]
    public virtual async Task<PagedResultDto<PaymentDetailsDto>> GetPagedListAsync(QueryParameters input)
    {
        try
        {
            // Get the base query with includes
            var query = await ExecuteDynamicQueryAsync(input);

            // Get total count before paging
            var totalCount = await query.CountAsync();

            var items = await query
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount)
                .ToListAsync();

            // Map to DTOs
            var dtos = ObjectMapper.Map<List<PaymentDetail>, List<PaymentDetailsDto>>(items);
            // Manually populate related entities based on SourceId and SourceType
            await PopulateRelatedEntitiesAsync(dtos);

            // Return a standard ABP paged result
            return new PagedResultDto<PaymentDetailsDto>
            {
                TotalCount = totalCount,
                Items = dtos
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting paged list of reports: {Message}", ex.Message);
            throw new UserFriendlyException(
                "Could not retrieve reports list",
                "Error.ReportsList.Failed",
                ex.Message);
        }
    }

    /// <summary>
    /// Execute dynamic query with filtering and sorting
    /// </summary>
    protected virtual async Task<IQueryable<PaymentDetail>> ExecuteDynamicQueryAsync(QueryParameters parameters)
    {
        // Get base query with selective includes to prevent circular references
        var query = await _repository.GetQueryableAsync();

        // Use AsNoTracking for better performance and to avoid tracking conflicts
        query = query.AsNoTracking()
            .Include(x => x.Payments)
            .Include(x => x.ReservationDetails)
                .ThenInclude(rd => rd!.Room)
            .Include(x => x.ReservationDetails)
                .ThenInclude(rd => rd!.Guest)
            .Include(x => x.ReservationDetails)
                .ThenInclude(rd => rd!.Status)
            .Include(x => x.ReservationDetails)
                .ThenInclude(rd => rd!.Reservation);

        // Note: ReservationFoodAndBeverages and ReservationRoom includes removed
        // due to polymorphic relationship - these will be populated manually

        // Add selective includes based on what fields are being filtered or sorted
        var fieldsToCheck = new List<string>();

        // Add filter fields
        if (parameters.FilterGroup?.Conditions != null)
        {
            fieldsToCheck.AddRange(parameters.FilterGroup.Conditions
                .Where(c => c.FieldName.Contains('.'))
                .Select(c => c.FieldName));
        }

        // Add sort fields
        if (parameters.Sort?.Count > 0)
        {
            fieldsToCheck.AddRange(parameters.Sort
                .Where(s => s.Field.Contains('.'))
                .Select(s => s.Field));
        }

        // Apply filters
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<PaymentDetail>.ApplyFilters(query, parameters.FilterGroup);
        }

        // Apply sorting
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<PaymentDetail>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            // Use the default sorting if provided
            var sortDesc = parameters.Sorting.StartsWith('-');
            var sortField = sortDesc ? parameters.Sorting[1..] : parameters.Sorting;
            query = DynamicQueryBuilder<PaymentDetail>.ApplySorting(query, sortField, sortDesc);
        }
        else
        {
            // Default sorting by Id
            query = query.OrderBy(x => x.Id);
        }

        return query;
    }

    /// <summary>
    /// Manually populate related entities based on SourceId and SourceType
    /// </summary>
    private async Task PopulateRelatedEntitiesAsync(List<PaymentDetailsDto> dtos)
    {
        // Group DTOs by SourceType to minimize database queries
        var reservationRoomIds = dtos
            .Where(d => (d.SourceType == PaymentSourceType.ReservationRoomService) && d.SourceId != Guid.Empty)
            .Select(d => d.SourceId)
            .Distinct()
            .ToList();

        var reservationFoodAndBeverageIds = dtos
            .Where(d => d.SourceType == PaymentSourceType.ReservationRoomFoodAndBeverage && d.SourceId != Guid.Empty)
            .Select(d => d.SourceId)
            .Distinct()
            .ToList();

        // Fetch related entities in bulk
        var reservationRooms = new Dictionary<Guid, ReservationRoom>();
        if (reservationRoomIds.Any())
        {
            var rooms = await _reservationRoomRepository.GetQueryableAsync();
            var roomList = await rooms
                .AsNoTracking()
                .Include(r => r.Services)
                .Where(r => reservationRoomIds.Contains(r.Id))
                .ToListAsync();

            reservationRooms = roomList.ToDictionary(r => r.Id, r => r);
        }

        var reservationFoodAndBeverages = new Dictionary<Guid, ReservationFoodAndBeverage>();
        if (reservationFoodAndBeverageIds.Any())
        {
            var foodAndBeverages = await _reservationFoodAndBeverageRepository.GetQueryableAsync();
            var foodAndBeverageList = await foodAndBeverages
                .AsNoTracking()
                .Include(f => f.FoodAndBeverage)
                .Where(f => reservationFoodAndBeverageIds.Contains(f.Id))
                .ToListAsync();

            reservationFoodAndBeverages = foodAndBeverageList.ToDictionary(f => f.Id, f => f);
        }

        // Populate the DTOs with related entities
        foreach (var dto in dtos)
        {
            switch (dto.SourceType)
            {
                case PaymentSourceType.ReservationRoomService:
                    if (reservationRooms.TryGetValue(dto.SourceId, out var reservationRoom))
                    {
                        dto.ReservationRoom = ObjectMapper.Map<ReservationRoom, ReservationRoomsDto>(reservationRoom);
                    }
                    break;

                case PaymentSourceType.ReservationRoomFoodAndBeverage:
                    if (reservationFoodAndBeverages.TryGetValue(dto.SourceId, out var reservationFoodAndBeverage))
                    {
                        dto.ReservationFoodAndBeverages = ObjectMapper.Map<ReservationFoodAndBeverage, ReservationFoodAndBeveragesDto>(reservationFoodAndBeverage);
                    }
                    break;

                // Add other source types as needed
                default:
                    // Handle other source types or leave as null
                    break;
            }
        }
    }
}
