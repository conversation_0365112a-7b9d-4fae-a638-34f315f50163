﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Permissions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.ReservationTypes;

[Authorize(WismaAppPermissions.PolicyReservationType.Default)]
public class ReservationTypesAppService : PermissionCheckedCrudAppService<
        ReservationType,
        ReservationTypesDto,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdateReservationTypesDto,
        CreateUpdateReservationTypesDto
    >, IReservationTypesAppService
{
    private readonly IRepository<ReservationType, Guid> _repository;

    public ReservationTypesAppService(IRepository<ReservationType, Guid> repository, IPermissionChecker permissionChecker)
        : base(repository, permissionChecker)
    {
        _repository = repository;

        GetPolicyName = WismaAppPermissions.PolicyReservationType.View;
        GetListPolicyName = WismaAppPermissions.PolicyReservationType.View;
        CreatePolicyName = WismaAppPermissions.PolicyReservationType.Create;
        UpdatePolicyName = WismaAppPermissions.PolicyReservationType.Edit;
        DeletePolicyName = WismaAppPermissions.PolicyReservationType.Delete;
    }


    public override async Task<ReservationTypesDto> CreateAsync(CreateUpdateReservationTypesDto input)
    {
        var entity = new ReservationType(
            GuidGenerator.Create(),
            input.Name,
            input.StatusId
        );

        await _repository.InsertAsync(entity);
        return ObjectMapper.Map<ReservationType, ReservationTypesDto>(entity);
    }

    public override async Task<ReservationTypesDto> UpdateAsync(Guid id, CreateUpdateReservationTypesDto input)
    {
        var entity = await _repository.GetAsync(id);

        entity.Name = input.Name;
        entity.StatusId = input.StatusId;

        await _repository.UpdateAsync(entity);
        return ObjectMapper.Map<ReservationType, ReservationTypesDto>(entity);
    }
}