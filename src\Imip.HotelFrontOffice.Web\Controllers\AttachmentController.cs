using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Attachments;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.AspNetCore.Mvc;

namespace Imip.HotelFrontOffice.Web.Controllers
{
    [RemoteService]
    [Route("api/attachment")]
    public class AttachmentController : AbpController
    {
        private readonly IAttachmentAppService _attachmentAppService;

        public AttachmentController(IAttachmentAppService attachmentAppService)
        {
            _attachmentAppService = attachmentAppService;
        }

        [HttpGet]
        public async Task<ActionResult<PagedResultDto<FileUploadResultDto>>> GetAllAsync([FromQuery] GetAttachmentsInput input)
        {
            var result = await _attachmentAppService.GetAllAsync(input);
            return Ok(result);
        }

        [HttpGet]
        [Route("download/{id}")]
        public async Task<IActionResult> DownloadAsync(Guid id)
        {
            var fileDto = await _attachmentAppService.DownloadAsync(id);
            return File(fileDto.Content, fileDto.ContentType, fileDto.FileName);
        }

        [HttpGet]
        [Route("stream/{id}")]
        public async Task<IActionResult> StreamAsync(Guid id)
        {
            var fileDto = await _attachmentAppService.DownloadAsync(id);

            // Set content disposition too inline to display in the browser
            Response.Headers.Append("Content-Disposition", "inline");
            return File(fileDto.Content, fileDto.ContentType, enableRangeProcessing: true);
        }

        /// <summary>
        /// Uploads a file using JSON with base64 encoded content
        /// </summary>
        /// <param name="input">The file upload input DTO</param>
        /// <returns>The file upload result DTO</returns>
        /// <response code="200">Returns the uploaded file information</response>
        /// <response code="400">If the file is invalid or empty</response>
        /// <response code="415">If the file type is not allowed (only PDF, DOC/DOCX, and images are permitted)</response>
        [HttpPost]
        [Route("upload")]
        [ProducesResponseType(typeof(FileUploadResultDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status415UnsupportedMediaType)]
        public async Task<ActionResult<FileUploadResultDto>> UploadAsync([FromBody] FileUploadInputDto input)
        {
            if (input == null)
            {
                return BadRequest("No input was provided.");
            }

            if (input.FileContent == null || input.FileContent.Length == 0)
            {
                return BadRequest("No file content was provided.");
            }

            // Validate file type (only allow PDF, DOCX, and image files)
            string contentType = input.ContentType.ToLower();
            if (!IsAllowedTemplateFileType(contentType))
            {
                throw new UserFriendlyException(L["Status415UnsupportedMediaType"], "Only PDF, Word documents (DOC/DOCX), and image files (JPEG, PNG, GIF, etc.) are allowed.");
            }

            var result = await _attachmentAppService.UploadWithFileContentAsync(input);

            return Ok(result);
        }

        [HttpGet]
        [Route("by-reference")]
        public async Task<ActionResult<List<FileUploadResultDto>>> GetByReferenceAsync(Guid referenceId, string referenceType)
        {
            var result = await _attachmentAppService.GetByReferenceAsync(referenceId, referenceType);
            return Ok(result);
        }

        [HttpDelete]
        [Route("{id}")]
        public async Task<ActionResult<bool>> DeleteAsync(Guid id)
        {
            var result = await _attachmentAppService.DeleteAsync(id);
            return Ok(result);
        }

        [HttpPost]
        [Route("bulk-download")]
        public async Task<IActionResult> BulkDownloadAsync([FromBody] BulkDownloadDto input)
        {
            var zipBytes = await _attachmentAppService.BulkDownloadAsync(input);

            // Set the file name for the zip file
            string zipFileName = !string.IsNullOrWhiteSpace(input.ZipFileName)
                ? $"{input.ZipFileName}.zip"
                : $"attachments_{DateTime.Now:yyyyMMdd_HHmmss}.zip";

            return File(zipBytes, "application/zip", zipFileName);
        }

        /// <summary>
        /// Uploads a file using multipart/form-data
        /// </summary>
        /// <param name="model">The file upload form data</param>
        /// <returns>The file upload result DTO</returns>
        /// <response code="200">Returns the uploaded file information</response>
        /// <response code="400">If the file is invalid or empty</response>
        /// <response code="415">If the file type is not allowed (only PDF, DOC/DOCX, and images are permitted)</response>
        [HttpPost]
        [Route("upload-form")]
        [Consumes("multipart/form-data")]
        [ProducesResponseType(typeof(FileUploadResultDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status415UnsupportedMediaType)]
        public async Task<ActionResult<FileUploadResultDto>> UploadFormAsync([FromForm] FileUploadFormDto model)
        {
            if (model.File == null || model.File.Length == 0)
            {
                return BadRequest("No file was uploaded or the file is empty.");
            }

            // Validate file type (only allow PDF, DOCX, and image files)
            string contentType = model.File.ContentType.ToLower();
            if (!IsAllowedTemplateFileType(contentType))
            {
                throw new UserFriendlyException(L["Status415UnsupportedMediaType"], "Only PDF, Word documents (DOC/DOCX), and image files (JPEG, PNG, GIF, etc.) are allowed.");
            }

            try
            {
                // Read file content
                byte[] fileBytes;
                using (var memoryStream = new MemoryStream())
                {
                    await model.File.CopyToAsync(memoryStream);
                    fileBytes = memoryStream.ToArray();
                }

                // Create FileUploadDto with metadata
                var uploadDto = new FileUploadDto
                {
                    Description = model.Description,
                    ReferenceId = model.ReferenceId,
                    ReferenceType = model.ReferenceType
                };

                // Call the service method to upload the file
                var result = await _attachmentAppService.UploadFileAsync(
                    uploadDto,
                    model.File.FileName,
                    model.File.ContentType,
                    fileBytes);

                return Ok(result);
            }
            catch (UserFriendlyException ex)
            {
                return BadRequest(new { error = ex.Message, details = ex.Details });
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error uploading file: {Message}", ex.Message);
                return BadRequest(new { error = "Failed to upload file", details = ex.Message });
            }
        }

        /// <summary>
        /// Checks if the file type is allowed (DOCX)
        /// </summary>
        /// <param name="contentType">The content type to check</param>
        /// <returns>True if the file type is allowed, false otherwise</returns>
        private static bool IsAllowedTemplateFileType(string contentType)
        {
            // Allow DOCX files
            if (contentType == "application/vnd.openxmlformats-officedocument.wordprocessingml.document")
                return true;

            // Allow DOC files
            if (contentType == "application/msword")
                return true;

            return false;
        }
    }
}
