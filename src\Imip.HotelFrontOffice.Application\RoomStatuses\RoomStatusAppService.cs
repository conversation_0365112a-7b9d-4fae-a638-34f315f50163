﻿using System;
using Imip.HotelFrontOffice.Permissions;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.RoomStatuses;

[Authorize(WismaAppPermissions.PolicyRoomStatus.Default)]
public class RoomStatusAppService : PermissionCheckedCrudAppService<
        RoomStatuses.RoomStatus,
        RoomStatusDto,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdateRoomStatusDto,
        CreateUpdateRoomStatusDto
    >, IRoomStatusAppService
{
    private readonly IRepository<RoomStatuses.RoomStatus, Guid> _repository;

    public RoomStatusAppService(IRepository<RoomStatuses.RoomStatus, Guid> repository,
        IPermissionChecker permissionChecker)
        : base(repository, permissionChecker)
    {
        _repository = repository;

        GetPolicyName = WismaAppPermissions.PolicyRoomStatus.View;
        GetListPolicyName = WismaAppPermissions.PolicyRoomStatus.View;
        CreatePolicyName = WismaAppPermissions.PolicyRoomStatus.Create;
        UpdatePolicyName = WismaAppPermissions.PolicyRoomStatus.Edit;
        DeletePolicyName = WismaAppPermissions.PolicyRoomStatus.Delete;
    }
}