using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Attachments;
using Imip.HotelFrontOffice.Guests;
using Imip.HotelFrontOffice.ReservationDetails;
using Imip.HotelFrontOffice.Reservations;
using Imip.HotelFrontOffice.Rooms;
using Imip.HotelFrontOffice.RoomStatuses;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Shouldly;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Guids;
using Xunit;

namespace Imip.HotelFrontOffice.Application.Tests.Reservations
{
    public class ReservationDetailServiceTests : HotelFrontOfficeApplicationTestBase<HotelFrontOfficeApplicationTestModule>
    {
        private readonly IRepository<ReservationDetail, Guid> _mockReservationDetailRepository;
        private readonly IRepository<Guest, Guid> _mockGuestRepository;
        private readonly IRepository<Room, Guid> _mockRoomRepository;
        private readonly IRepository<RoomStatus, Guid> _mockRoomStatusRepository;
        private readonly ILogger<ReservationDetailService> _mockLogger;
        private readonly IGuidGenerator _mockGuidGenerator;
        private readonly IAttachmentAppService _mockAttachmentAppService;
        private readonly ReservationDetailService _reservationDetailService;

        private readonly Guid _testRoomId = Guid.NewGuid();
        private readonly Guid _testGuestId = Guid.NewGuid();
        private readonly Guid _testReservationId = Guid.NewGuid();
        private readonly Guid _testStatusId = Guid.NewGuid();
        private readonly Guid _testRoomStatusId = Guid.NewGuid();
        private readonly Guid _testVacantReadyStatusId = Guid.NewGuid();
        private readonly Guid _testOccupiedStatusId = Guid.NewGuid();

        public ReservationDetailServiceTests()
        {
            // Setup mock repositories
            _mockReservationDetailRepository = Substitute.For<IRepository<ReservationDetail, Guid>>();
            _mockGuestRepository = Substitute.For<IRepository<Guest, Guid>>();
            _mockRoomRepository = Substitute.For<IRepository<Room, Guid>>();
            _mockRoomStatusRepository = Substitute.For<IRepository<RoomStatus, Guid>>();

            // Setup mock services
            _mockLogger = Substitute.For<ILogger<ReservationDetailService>>();
            _mockGuidGenerator = Substitute.For<IGuidGenerator>();
            _mockAttachmentAppService = Substitute.For<IAttachmentAppService>();

            // Setup GuidGenerator to return predictable values
            _mockGuidGenerator.Create().Returns(Guid.NewGuid());

            // Create the service to test
            _reservationDetailService = new ReservationDetailService(
                _mockReservationDetailRepository,
                _mockGuestRepository,
                _mockRoomRepository,
                _mockLogger,
                _mockGuidGenerator,
                _mockAttachmentAppService);
        }

        [Fact]
        public async Task CreateReservationDetailAsync_WithVacantReadyRoom_ShouldCreateReservationDetail()
        {
            // Arrange
            var createDto = new CreateReservationDetailDto
            {
                RoomId = _testRoomId,
                GuestId = _testGuestId,
                StatusId = _testStatusId,
                CheckInDate = DateTime.Now.AddDays(1),
                CheckOutDate = DateTime.Now.AddDays(3),
                Rfid = "RFID-001",
                Price = 100.00m
            };

            // Create a room with "VR" status
            var vacantReadyStatus = new RoomStatus(_testVacantReadyStatusId, "Vacant Ready", "#00FF00", "VR");
            var room = new Room(
                _testRoomId,
                100.00m,
                "Test Room",
                Guid.NewGuid(),
                "20 sqm",
                "101",
                "R101",
                _testVacantReadyStatusId)
            {
                RoomStatus = vacantReadyStatus
            };

            // Setup mock repository responses
            _mockRoomRepository.GetAsync(_testRoomId, true).Returns(room);
            _mockGuestRepository.GetAsync(_testGuestId).Returns(new Guest(
                _testGuestId,
                "Test Guest",
                "ID123456",
                "ID123456",
                "1234567890",
                "<EMAIL>",
                "US",
                "Test Company",
                "",
                Guid.NewGuid()
            ));

            // Act
            var result = await _reservationDetailService.CreateReservationDetailAsync(createDto, _testReservationId);

            // Assert
            result.ShouldNotBe(Guid.Empty);
            await _mockReservationDetailRepository.Received(1).InsertAsync(Arg.Any<ReservationDetail>(), Arg.Is<bool>(x => x == true));
        }

        [Fact]
        public async Task CreateReservationDetailAsync_WithNonVacantReadyRoom_ShouldThrowException()
        {
            // Arrange
            var createDto = new CreateReservationDetailDto
            {
                RoomId = _testRoomId,
                GuestId = _testGuestId,
                StatusId = _testStatusId,
                CheckInDate = DateTime.Now.AddDays(1),
                CheckOutDate = DateTime.Now.AddDays(3),
                Rfid = "RFID-001",
                Price = 100.00m
            };

            // Create a room with "OCC" (Occupied) status
            var occupiedStatus = new RoomStatus(_testOccupiedStatusId, "Occupied", "#FF0000", "OCC");
            var room = new Room(
                _testRoomId,
                100.00m,
                "Test Room",
                Guid.NewGuid(),
                "20 sqm",
                "101",
                "R101",
                _testOccupiedStatusId)
            {
                RoomStatus = occupiedStatus
            };

            // Setup mock repository responses
            _mockRoomRepository.GetAsync(_testRoomId, true).Returns(room);

            // Act & Assert
            var exception = await Should.ThrowAsync<UserFriendlyException>(async () =>
                await _reservationDetailService.CreateReservationDetailAsync(createDto, _testReservationId));

            exception.Message.ShouldBe("Room cannot be reserved");
            exception.Code.ShouldBe("Error.ReservationDetail.RoomNotVacantReady");
            exception.Details.ShouldBe("Only rooms with 'Vacant Ready' status can be reserved");
        }

        [Fact]
        public async Task CreateReservationDetailAsync_WithNullRoomStatus_ShouldLoadRoomStatus()
        {
            // Arrange
            var createDto = new CreateReservationDetailDto
            {
                RoomId = _testRoomId,
                GuestId = _testGuestId,
                StatusId = _testStatusId,
                CheckInDate = DateTime.Now.AddDays(1),
                CheckOutDate = DateTime.Now.AddDays(3),
                Rfid = "RFID-001",
                Price = 100.00m
            };

            // Create a room with null RoomStatus but valid RoomStatusId
            var room = new Room(
                _testRoomId,
                100.00m,
                "Test Room",
                Guid.NewGuid(),
                "20 sqm",
                "101",
                "R101",
                _testVacantReadyStatusId);

            // Create a vacant ready status
            var vacantReadyStatus = new RoomStatus(_testVacantReadyStatusId, "Vacant Ready", "#00FF00", "VR");

            // Setup mock repository responses
            _mockRoomRepository.GetAsync(_testRoomId, true).Returns(room);
            _mockRoomStatusRepository.GetAsync(_testVacantReadyStatusId).Returns(vacantReadyStatus);
            _mockGuestRepository.GetAsync(_testGuestId).Returns(new Guest(
                _testGuestId,
                "Test Guest",
                "ID123456",
                "ID123456",
                "1234567890",
                "<EMAIL>",
                "US",
                "Test Company",
                "",
                Guid.NewGuid()
            ));

            // Act
            var result = await _reservationDetailService.CreateReservationDetailAsync(createDto, _testReservationId);

            // Assert
            result.ShouldNotBe(Guid.Empty);
            await _mockReservationDetailRepository.Received(1).InsertAsync(Arg.Any<ReservationDetail>(), Arg.Is<bool>(x => x == true));
        }
    }
}
