using System;
using System.Net;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Volo.Abp.DependencyInjection;

namespace Imip.HotelFrontOffice.Web.Middleware;

public class TokenExpirationMiddleware : IMiddleware, ITransientDependency
{
    private readonly ILogger<TokenExpirationMiddleware> _logger;

    public TokenExpirationMiddleware(ILogger<TokenExpirationMiddleware> logger)
    {
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, RequestDelegate next)
    {
        // Subscribe to the JWT Bearer authentication events
        context.Response.OnStarting(() =>
        {
            // Check if we have a 401 Unauthorized status code
            if (context.Response.StatusCode == (int)HttpStatusCode.Unauthorized)
            {

                // Check if the response has already been started
                if (!context.Response.HasStarted)
                {
                    // Set the content type to JSON
                    context.Response.ContentType = "application/json";

                    // Create a JSON error response
                    var errorResponse = new
                    {
                        error = new
                        {
                            code = "Unauthorized",
                            message = "You are not authenticated or your token has expired",
                            details = "Please provide a valid authentication token"
                        }
                    };

                    // Serialize the error response to JSON
                    var jsonResponse = JsonSerializer.Serialize(errorResponse);

                    // Write the JSON response to the response body
                    return context.Response.WriteAsync(jsonResponse);
                }
            }

            return Task.CompletedTask;
        });

        try
        {
            await next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in TokenExpirationMiddleware");
            throw;
        }
    }
}
