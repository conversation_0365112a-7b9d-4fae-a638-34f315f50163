{"App": {"SelfUrl": "https://localhost:44334", "DisablePII": false}, "ConnectionStrings": {"Default": "Server=(LocalDb)\\MSSQLLocalDB;Database=HotelFrontOffice_Test;Trusted_Connection=True;TrustServerCertificate=True"}, "AuthServer": {"Authority": "https://localhost:44334", "RequireHttpsMetadata": "false", "SwaggerClientId": "HotelFrontOffice_Swagger"}, "StringEncryption": {"DefaultPassPhrase": "gsKnGZ041HLL4IM8"}, "Settings": {"Abp.Mailing.Smtp.Host": "127.0.0.1", "Abp.Mailing.Smtp.Port": "25", "Abp.Mailing.Smtp.UserName": "", "Abp.Mailing.Smtp.Password": "", "Abp.Mailing.Smtp.Domain": "", "Abp.Mailing.Smtp.EnableSsl": "false", "Abp.Mailing.Smtp.UseDefaultCredentials": "true", "Abp.Mailing.DefaultFromAddress": "<EMAIL>", "Abp.Mailing.DefaultFromDisplayName": "ABP application"}, "IdentityServer": {"Clients": {"HotelFrontOffice_Web": {"ClientId": "HotelFrontOffice_Web", "ClientSecret": "1q2w3e*", "RootUrl": "https://localhost:44334"}, "HotelFrontOffice_Swagger": {"ClientId": "HotelFrontOffice_Swagger", "ClientSecret": "1q2w3e*", "RootUrl": "https://localhost:44334"}}}}