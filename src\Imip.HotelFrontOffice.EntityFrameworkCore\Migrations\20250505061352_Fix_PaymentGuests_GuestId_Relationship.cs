﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.HotelFrontOffice.Migrations
{
    /// <inheritdoc />
    public partial class Fix_PaymentGuests_GuestId_Relationship : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AppPaymentGuests_AppGuests_GuestId1",
                table: "AppPaymentGuests");

            migrationBuilder.DropIndex(
                name: "IX_AppPaymentGuests_GuestId1",
                table: "AppPaymentGuests");

            migrationBuilder.DropColumn(
                name: "GuestId1",
                table: "AppPaymentGuests");

            migrationBuilder.AddColumn<Guid>(
                name: "CompanyId1",
                table: "AppReservations",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "DiningOptionsId1",
                table: "AppReservations",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_AppReservations_CompanyId1",
                table: "AppReservations",
                column: "CompanyId1");

            migrationBuilder.CreateIndex(
                name: "IX_AppReservations_DiningOptionsId1",
                table: "AppReservations",
                column: "DiningOptionsId1");

            migrationBuilder.AddForeignKey(
                name: "FK_AppReservations_MasterCompany_CompanyId1",
                table: "AppReservations",
                column: "CompanyId1",
                principalTable: "MasterCompany",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AppReservations_MasterDiningOptions_DiningOptionsId1",
                table: "AppReservations",
                column: "DiningOptionsId1",
                principalTable: "MasterDiningOptions",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AppReservations_MasterCompany_CompanyId1",
                table: "AppReservations");

            migrationBuilder.DropForeignKey(
                name: "FK_AppReservations_MasterDiningOptions_DiningOptionsId1",
                table: "AppReservations");

            migrationBuilder.DropIndex(
                name: "IX_AppReservations_CompanyId1",
                table: "AppReservations");

            migrationBuilder.DropIndex(
                name: "IX_AppReservations_DiningOptionsId1",
                table: "AppReservations");

            migrationBuilder.DropColumn(
                name: "CompanyId1",
                table: "AppReservations");

            migrationBuilder.DropColumn(
                name: "DiningOptionsId1",
                table: "AppReservations");

            migrationBuilder.AddColumn<Guid>(
                name: "GuestId1",
                table: "AppPaymentGuests",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_AppPaymentGuests_GuestId1",
                table: "AppPaymentGuests",
                column: "GuestId1");

            migrationBuilder.AddForeignKey(
                name: "FK_AppPaymentGuests_AppGuests_GuestId1",
                table: "AppPaymentGuests",
                column: "GuestId1",
                principalTable: "AppGuests",
                principalColumn: "Id");
        }
    }
}
