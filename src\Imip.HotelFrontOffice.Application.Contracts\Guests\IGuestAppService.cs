﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.HotelFrontOffice.Guests
{
    public interface IGuestAppService : ICrudAppService<
        GuestDto,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdateGuestDto,
        CreateUpdateGuestDto
    >
    {
        /// <summary>
        /// Upload attachment for a guest
        /// </summary>
        /// <param name="guestId">The guest ID</param>
        /// <param name="attachments">List of attachments to upload</param>
        /// <returns>Task</returns>
        Task UploadAttachmentsAsync(Guid guestId, List<GuestAttachmentDto> attachments);
    }
}