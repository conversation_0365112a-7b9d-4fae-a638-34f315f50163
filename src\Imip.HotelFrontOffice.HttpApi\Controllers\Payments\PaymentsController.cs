using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Attachments;
using Imip.HotelFrontOffice.Models;
using Imip.HotelFrontOffice.Payments;
using Imip.HotelFrontOffice.Permissions;
using Imip.HotelFrontOffice.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.Controllers.Payments;

[Route("api/app/payments")]
[RemoteService]
public class PaymentsController : HotelFrontOfficeController
{
    private readonly IPaymentsAppService _paymentsAppService;
    private readonly IRepository<Payment> _repository;
    private readonly ILogger<PaymentsController> _logger;
    private readonly IAttachmentAppService _attachmentAppService;

    public PaymentsController(
        IPaymentsAppService paymentsAppService,
        IRepository<Payment> repository,
        ILogger<PaymentsController> logger,
        IAttachmentAppService attachmentAppService)
    {
        _paymentsAppService = paymentsAppService;
        _repository = repository;
        _logger = logger;
        _attachmentAppService = attachmentAppService;
    }

    /// <summary>
    /// Get a paged list of payments with dynamic filtering and sorting
    /// </summary>
    /// <param name="input">Query parameters including filtering and sorting</param>
    /// <returns>Paged list of payments in standard ABP format</returns>
    [HttpPost("list")]
    [Authorize(WismaAppPermissions.PolicyPayment.View)]
    [ProducesResponseType(typeof(PagedResultDto<PaymentsDto>), StatusCodes.Status200OK)]
    public virtual async Task<PagedResultDto<PaymentsDto>> GetPagedListAsync(QueryParameters input)
    {
        try
        {
            // Get the base query with includes
            var query = await ExecuteDynamicQueryAsync(input);

            // Get the total count before paging
            var totalCount = await query.CountAsync();

            // Apply paging
            var items = await query
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount)
                .ToListAsync();

            // Map to DTOs
            var dtos = ObjectMapper.Map<List<Payment>, List<PaymentsDto>>(items);

            // Populate payment attachments for each payment
            await PopulatePaymentAttachmentsAsync(dtos);

            // Return a standard ABP paged result
            return new PagedResultDto<PaymentsDto>
            {
                TotalCount = totalCount,
                Items = dtos
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting paged list of payments: {Message}", ex.Message);
            throw new UserFriendlyException(
                "Could not retrieve payments list",
                "Error.PaymentsList.Failed",
                ex.Message);
        }
    }

    /// <summary>
    /// Execute a dynamic query with filtering and sorting
    /// </summary>
    protected virtual async Task<IQueryable<Payment>> ExecuteDynamicQueryAsync(QueryParameters parameters)
    {
        // Get base query with includes
        var query = await _repository.GetQueryableAsync();

        // Add includes for related entities
        query = query
            .AsNoTracking()
            .Include(x => x.PaymentMethod)
            .Include(x => x.Status)
            .Include(x => x.Reservations)
            .Include(x => x.PaymentDetails);

        // Check if we need to include deeper relationships based on filter or sort fields
        var fieldsToCheck = new List<string>();

        // Add filter fields
        if (parameters.FilterGroup?.Conditions != null)
        {
            fieldsToCheck.AddRange(parameters.FilterGroup.Conditions
                .Where(c => c.FieldName.Contains('.'))
                .Select(c => c.FieldName));
        }

        // Check if we need to include PaymentDetails and its related entities
        if (fieldsToCheck.Any(f => f.StartsWith("paymentDetails.")))
        {
            query = query.Include(x => x.PaymentDetails);

            // Check if we need to include ReservationDetails
            if (fieldsToCheck.Any(f => f.Contains("paymentDetails.reservationDetails")))
            {
                query = query.Include(x => x.PaymentDetails);
                // .ThenInclude(pd => pd.ReservationDetails);
            }
        }

        // Add sort fields
        if (parameters.Sort?.Count > 0)
        {
            fieldsToCheck.AddRange(parameters.Sort
                .Where(s => s.Field.Contains('.'))
                .Select(s => s.Field));
        }

        // Payment details are already included if needed

        // Include payment guests if needed
        if (fieldsToCheck.Any(f => f.StartsWith("paymentGuests.")))
        {
            query = query.Include("PaymentGuests");

            if (fieldsToCheck.Any(f => f.Contains("paymentGuests.guest")))
            {
                query = query.Include("PaymentGuests.Guest");
            }
        }

        // Include reservation details if needed
        if (fieldsToCheck.Any(f => f.StartsWith("reservations.") || f.Contains("reservation")))
        {
            query = query.Include("Reservations.ReservationType");
            query = query.Include("Reservations.Company");
        }

        // Apply filters
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<Payment>.ApplyFilters(query, parameters.FilterGroup);
        }

        // Apply sorting
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<Payment>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            // Use the default sorting if provided using Dynamic LINQ
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            // Default sorting by creation time descending
            query = query.OrderByDescending(x => x.CreationTime);
        }

        return query;
    }

    /// <summary>
    /// Populates payment attachments for a list of payment DTOs
    /// </summary>
    /// <param name="paymentDtos">List of payment DTOs to populate attachments for</param>
    private async Task PopulatePaymentAttachmentsAsync(List<PaymentsDto> paymentDtos)
    {
        foreach (var paymentDto in paymentDtos)
        {
            try
            {
                // Get general payment attachments
                var paymentAttachments = await _attachmentAppService.GetByReferenceAsync(paymentDto.Id, "Payment");
                if (paymentAttachments != null && paymentAttachments.Count > 0)
                {
                    paymentDto.PaymentAttachments = paymentAttachments.Select(a => new PaymentAttachmentInfoDto
                    {
                        Id = a.Id,
                        FileName = a.FileName,
                        ContentType = a.ContentType,
                        Size = a.Size,
                        Url = a.Url,
                        StreamUrl = a.StreamUrl,
                        Description = null, // Description is not available in FileUploadResultDto
                        CreationTime = a.UploadTime
                    }).ToList();
                }

                // Get reservation-related payment attachments
                var reservationAttachments = await _attachmentAppService.GetByReferenceAsync(paymentDto.Id, "PaymentReservation");
                if (reservationAttachments != null && reservationAttachments.Count > 0)
                {
                    paymentDto.ReservationAttachments = reservationAttachments.Select(a => new PaymentReservationAttachmentInfoDto
                    {
                        Id = a.Id,
                        FileName = a.FileName,
                        ContentType = a.ContentType,
                        Size = a.Size,
                        Url = a.Url,
                        StreamUrl = a.StreamUrl,
                        Description = null, // Description is not available in FileUploadResultDto
                        CreationTime = a.UploadTime
                    }).ToList();
                }
            }
            catch (Exception ex)
            {
                // Log the error but don't fail the entire request
                _logger.LogError(ex, "Error retrieving attachments for payment {PaymentId}: {Message}",
                    paymentDto.Id, ex.Message);
            }
        }
    }
}
