﻿using System.Threading.Tasks;
using Imip.HotelFrontOffice.Permissions;
using Imip.HotelFrontOffice.Web.Extensions;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Volo.Abp.DependencyInjection;

namespace Imip.HotelFrontOffice.Web.Authorization;

public class PermissionAuthorizationHandler : AuthorizationHandler<PermissionRequirement>, ITransientDependency
{
    private readonly ILogger<PermissionAuthorizationHandler> _logger;
    private readonly ApplicationConfigurationService _applicationConfigurationService;

    public PermissionAuthorizationHandler(
        ILogger<PermissionAuthorizationHandler> logger,
        ApplicationConfigurationService applicationConfigurationService)
    {
        _logger = logger;
        _applicationConfigurationService = applicationConfigurationService;
    }

    protected override async Task HandleRequirementAsync(
        AuthorizationHandlerContext context,
        PermissionRequirement requirement)
    {
        // _logger.LogDebug("Handling permission requirement: {PermissionName}", requirement.PermissionName);

        if (context.User == null)
        {
            // _logger.LogWarning("User is null when checking permission: {PermissionName}", requirement.PermissionName);
            return;
        }

        // Get the access token from the HttpContext
        string? accessToken = null;

        if (context.Resource is HttpContext httpContext)
        {
            // Use our extension method to get the token with fallback to Authorization header
            accessToken = await httpContext.GetAccessTokenWithFallbackAsync();

            // Only log token length for security
            if (!string.IsNullOrEmpty(accessToken))
            {
                // _logger.LogDebug("Retrieved access token: {TokenLength} characters", accessToken.Length);
            }

            // Check authentication status
            var authResult = await httpContext.AuthenticateAsync();
            if (!authResult.Succeeded)
            {
                _logger.LogWarning("Authentication failed: {FailureMessage}", authResult.Failure?.Message);
            }
        }

        if (!string.IsNullOrEmpty(accessToken))
        {
            // Check if the permission is granted in the application configuration
            var grantedPolicies = await _applicationConfigurationService.GetGrantedPoliciesAsync(accessToken);

            // Try with the original permission name
            if (grantedPolicies.TryGetValue(requirement.PermissionName, out var isGranted) && isGranted)
            {
                // _logger.LogDebug("Permission {PermissionName} is granted in application configuration", requirement.PermissionName);
                context.Succeed(requirement);
                return;
            }

            // Try with the "IdentityServer." prefix if the permission doesn't have it
            if (!requirement.PermissionName.StartsWith("IdentityServer."))
            {
                var identityServerPermissionName = "IdentityServer." + requirement.PermissionName;
                if (grantedPolicies.TryGetValue(identityServerPermissionName, out isGranted) && isGranted)
                {
                    // _logger.LogDebug("Permission {PermissionName} is granted as {IdentityServerPermissionName}",
                    //     requirement.PermissionName, identityServerPermissionName);
                    context.Succeed(requirement);
                    return;
                }
            }

            // _logger.LogDebug("Permission {PermissionName} not found in application configuration", requirement.PermissionName);
        }

        // If we couldn't check with the application configuration, the permission is denied
        // _logger.LogWarning("Permission {PermissionName} is denied", requirement.PermissionName);

        // Store the denied permission in the HttpContext for our middleware to use
        if (context.Resource is HttpContext currentContext)
        {
            currentContext.Items["DeniedPermission"] = requirement.PermissionName;
        }
    }
}