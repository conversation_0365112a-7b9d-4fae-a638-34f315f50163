﻿using System;
using Imip.HotelFrontOffice.MasterStatuses;
using Volo.Abp.Application.Dtos;

namespace Imip.HotelFrontOffice.RoomTypes;

public class RoomTypeDto : AuditedEntityDto<Guid>
{
    public string Name { get; set; } = default!;
    public string Alias { get; set; } = default!;
    public Guid StatusId { get; set; } = default!;
    public virtual MasterStatusDto Status { get; set; } = default!;
}