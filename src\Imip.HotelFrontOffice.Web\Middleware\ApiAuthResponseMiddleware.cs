﻿using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Volo.Abp;
using Volo.Abp.Authorization;
using Volo.Abp.Domain.Entities;

namespace Imip.HotelFrontOffice.Web.Middleware;

public class ApiAuthResponseMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<ApiAuthResponseMiddleware> _logger;

    public ApiAuthResponseMiddleware(RequestDelegate next, ILogger<ApiAuthResponseMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            if (context.Response.StatusCode == 302 &&
                (context.Response.Headers.Location.ToString().Contains("/Account/AccessDenied")))
            {
                context.Response.Clear();
                context.Response.StatusCode = StatusCodes.Status403Forbidden;
                await HandleStatusCodeResponse(context);
                return;
            }

            switch (context.Response.StatusCode)
            {
                case StatusCodes.Status400BadRequest:
                case StatusCodes.Status401Unauthorized:
                case StatusCodes.Status403Forbidden:
                case StatusCodes.Status404NotFound:
                case StatusCodes.Status415UnsupportedMediaType:
                case StatusCodes.Status405MethodNotAllowed:
                    context.Response.Clear();
                    await HandleStatusCodeResponse(context);
                    return;
            }

            await _next(context);
        }
        catch (Exception ex)
        {
            await HandleExceptionAsync(context, ex);
        }
    }

    private async Task HandleStatusCodeResponse(HttpContext context)
    {
        var response = new
        {
            success = false,
            message = GetDetailForStatusCode(context.Response.StatusCode),
            data = (object?)null,
            details = GetDetailForStatusCode(context.Response.StatusCode),
            error = new
            {
                code = context.Response.StatusCode,
                message = GetTitleForStatusCode(context.Response.StatusCode),
                details = GetDetailForStatusCode(context.Response.StatusCode),
                data = new { }
            }
        };

        context.Response.ContentType = "application/json";
        var jsonResponse = JsonConvert.SerializeObject(response, new JsonSerializerSettings
        {
            NullValueHandling = NullValueHandling.Ignore,
            Formatting = Formatting.Indented,
            ContractResolver = new Newtonsoft.Json.Serialization.CamelCasePropertyNamesContractResolver()
        });
        await context.Response.WriteAsync(jsonResponse);
    }

    private string GetTitleForStatusCode(int statusCode)
    {
        return statusCode switch
        {
            StatusCodes.Status400BadRequest => "bad request",
            StatusCodes.Status401Unauthorized => "unauthorized",
            StatusCodes.Status403Forbidden => "forbidden",
            StatusCodes.Status404NotFound => "not found",
            StatusCodes.Status415UnsupportedMediaType => "Unsupported Media Type",
            StatusCodes.Status405MethodNotAllowed => "method not allowed",
            _ => "error"
        };
    }

    private string GetDetailForStatusCode(int statusCode)
    {
        return statusCode switch
        {
            StatusCodes.Status400BadRequest => "the request was invalid",
            StatusCodes.Status401Unauthorized => "authentication required to access this resource",
            StatusCodes.Status403Forbidden => "you don't have permission to access this resource",
            StatusCodes.Status404NotFound => "the requested resource was not found",
            StatusCodes.Status405MethodNotAllowed => "the HTTP method is not allowed for this endpoint",
            _ => "an error occurred while processing your request"
        };
    }

    private async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        if (context.Response.HasStarted)
        {
            _logger.LogWarning("Response has already started, cannot clear headers/status code.");
            return;
        }

        context.Response.Clear();
        context.Response.ContentType = "application/json";

        var response = new
        {
            success = false,
            message = GetExceptionMessage(exception),
            data = (object?)null,
            error = GetErrorDetails(context, exception)
        };

        var jsonResponse = JsonConvert.SerializeObject(response, new JsonSerializerSettings
        {
            NullValueHandling = NullValueHandling.Ignore,
            Formatting = Formatting.Indented,
            ContractResolver = new Newtonsoft.Json.Serialization.CamelCasePropertyNamesContractResolver()
        });

        await context.Response.WriteAsync(jsonResponse);
    }

    private string GetExceptionMessage(Exception exception)
    {
        return exception switch
        {
            BusinessException bex => bex.Message,
            EntityNotFoundException => "Resource not found",
            AbpAuthorizationException => "Authorization failed",
            _ => "An error occurred while processing your request"
        };
    }

    private object GetErrorDetails(HttpContext context, Exception exception)
    {
        switch (exception)
        {
            case EntityNotFoundException notFoundEx:
                context.Response.StatusCode = StatusCodes.Status404NotFound;
                return new
                {
                    code = "404",
                    message = "not found",
                    details = notFoundEx.Message.ToLower(),
                    data = new { }
                };

            case UserFriendlyException userEx when userEx.Code == "404":
                context.Response.StatusCode = StatusCodes.Status404NotFound;
                return new
                {
                    code = userEx.Code,
                    message = "not found",
                    details = userEx.Message.ToLower(),
                    data = new { }
                };

            case BusinessException businessException:
                context.Response.StatusCode = StatusCodes.Status400BadRequest;
                return new
                {
                    code = businessException.Code,
                    message = businessException.Message,
                    details = businessException.Data,
                    data = businessException.Data
                };

            case AbpAuthorizationException:
                context.Response.StatusCode = context.User.Identity?.IsAuthenticated == true
                    ? StatusCodes.Status403Forbidden
                    : StatusCodes.Status401Unauthorized;
                return new
                {
                    code = context.Response.StatusCode.ToString(),
                    message = context.Response.StatusCode == StatusCodes.Status401Unauthorized
                        ? "unauthorized"
                        : "forbidden",
                    details = context.Response.StatusCode == StatusCodes.Status401Unauthorized
                        ? "authentication required to access this resource"
                        : "you don't have permission to access this resource",
                    data = new { }
                };

            default:
                _logger.LogError(exception, "An unexpected error occurred");
                context.Response.StatusCode = StatusCodes.Status500InternalServerError;
                var errorDetails = new
                {
                    code = "500",
                    message = "internal server error",
                    details = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Development"
                        ? exception.ToString()
                        : "an unexpected error occurred",
                    data = new { }
                };
                return errorDetails;
        }
    }
}

public static class ApiAuthResponseMiddlewareExtensions
{
    public static IApplicationBuilder UseApiAuthResponseMiddleware(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<ApiAuthResponseMiddleware>();
    }
}