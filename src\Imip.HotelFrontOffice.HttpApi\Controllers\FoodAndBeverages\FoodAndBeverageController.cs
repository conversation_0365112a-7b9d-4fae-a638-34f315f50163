using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Controllers;
using Imip.HotelFrontOffice.FoodAndBeverages;
using Imip.HotelFrontOffice.Models;
using Imip.HotelFrontOffice.Permissions;
using Imip.HotelFrontOffice.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.Controllers.FoodAndBeverages;

[Route("api/app/food-and-beverage")]
[RemoteService]
public class FoodAndBeverageController : HotelFrontOfficeController
{
    private readonly IFoodAndBeverageAppService _foodAndBeverageAppService;
    private readonly IRepository<FoodAndBeverage> _repository;
    private readonly ILogger<FoodAndBeverageController> _logger;

    public FoodAndBeverageController(
        IFoodAndBeverageAppService foodAndBeverageAppService,
        IRepository<FoodAndBeverage> repository,
        ILogger<FoodAndBeverageController> logger)
    {
        _foodAndBeverageAppService = foodAndBeverageAppService;
        _repository = repository;
        _logger = logger;
    }

    /// <summary>
    /// Get a paged list of reports with dynamic filtering and sorting
    /// </summary>
    /// <param name="input">Query parameters including filtering and sorting</param>
    /// <returns>Paged list of reports in standard ABP format</returns>
    [HttpPost("list")]
    [Authorize(WismaAppPermissions.PolicyFoodAndBeverage.View)]
    [ProducesResponseType(typeof(PagedResultDto<FoodAndBeverageDto>), StatusCodes.Status200OK)]
    public virtual async Task<PagedResultDto<FoodAndBeverageDto>> GetPagedListAsync(QueryParameters input)
    {
        try
        {
            // Get the base query with includes
            var query = await ExecuteDynamicQueryAsync(input);

            // Get total count before paging
            var totalCount = await query.CountAsync();

            var items = await query
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount)
                .ToListAsync();

            // Map to DTOs
            var dtos = ObjectMapper.Map<List<FoodAndBeverage>, List<FoodAndBeverageDto>>(items);

            // Return a standard ABP paged result
            return new PagedResultDto<FoodAndBeverageDto>
            {
                TotalCount = totalCount,
                Items = dtos
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting paged list of reports: {Message}", ex.Message);
            throw new UserFriendlyException(
                "Could not retrieve reports list",
                "Error.ReportsList.Failed",
                ex.Message);
        }
    }

    /// <summary>
    /// Execute dynamic query with filtering and sorting
    /// </summary>
    protected virtual async Task<IQueryable<FoodAndBeverage>> ExecuteDynamicQueryAsync(QueryParameters parameters)
    {
        // Get base query with includes
        var query = await _repository.GetQueryableAsync();

        // Add includes for related entities if needed
        query = query.AsNoTracking();

        // Check if we need to include deeper relationships based on filter or sort fields
        var fieldsToCheck = new List<string>();

        // Add filter fields
        if (parameters.FilterGroup?.Conditions != null)
        {
            fieldsToCheck.AddRange(parameters.FilterGroup.Conditions
                .Where(c => c.FieldName.Contains('.'))
                .Select(c => c.FieldName));
        }

        // Add sort fields
        if (parameters.Sort?.Count > 0)
        {
            fieldsToCheck.AddRange(parameters.Sort
                .Where(s => s.Field.Contains('.'))
                .Select(s => s.Field));
        }

        // Apply filters
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<FoodAndBeverage>.ApplyFilters(query, parameters.FilterGroup);
        }

        // Apply sorting
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<FoodAndBeverage>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            // Use the default sorting if provided
            var sortDesc = parameters.Sorting.StartsWith('-');
            var sortField = sortDesc ? parameters.Sorting[1..] : parameters.Sorting;
            query = DynamicQueryBuilder<FoodAndBeverage>.ApplySorting(query, sortField, sortDesc);
        }
        else
        {
            // Default sorting by name
            query = query.OrderBy(x => x.Name);
        }

        return query;
    }
}
