using System;
using Volo.Abp.Application.Dtos;

namespace Imip.HotelFrontOffice.Taxes;

public class TaxDto : AuditedEntityDto<Guid>
{
    public string Name { get; set; } = default!;
    public string Code { get; set; } = default!;
    public decimal? Rate { get; set; }
    public DateOnly? StartDate { get; set; } = default!;
    public DateOnly? EndDate { get; set; } = default!;
    public bool IsActive { get; set; } = default!;
}
