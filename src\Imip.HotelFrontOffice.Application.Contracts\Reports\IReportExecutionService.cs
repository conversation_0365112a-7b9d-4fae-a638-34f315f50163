using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Imip.HotelFrontOffice.Reports;

public interface IReportExecutionService
{
    Task<ReportPreviewDto> ExecuteReportAsync(Guid reportId, Dictionary<string, object> parameters);
    Task<byte[]> ExportReportToCsvAsync(Guid reportId, Dictionary<string, object> parameters);
    Task<byte[]> ExportReportToExcelAsync(Guid reportId, Dictionary<string, object> parameters);
    Task<List<ReportParameterDto>> GetReportParametersAsync(Guid reportId);
}
