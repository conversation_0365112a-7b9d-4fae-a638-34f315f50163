﻿using System;
using Imip.HotelFrontOffice.Guests;
using Imip.HotelFrontOffice.Payments;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.HotelFrontOffice.PaymentGuests;

public class PaymentGuest : FullAuditedAggregateRoot<Guid>
{
    public Guid PaymentId { get; set; }
    public Guid GuestId { get; set; }
    public decimal? AmountPaid { get; set; }
    public virtual Payment? Payments { get; set; }
    public virtual Guest? Guest { get; set; }

    protected PaymentGuest()
    {
    }

    public PaymentGuest(Guid id, Guid paymentId, Guid guestId, decimal amountPaid)
    {
        Id = id;
        PaymentId = paymentId;
        GuestId = guestId;
        AmountPaid = amountPaid;
    }
}