using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.EntityChangeLogs
{
    /// <summary>
    /// Repository interface for EntityChangeLog
    /// </summary>
    public interface IEntityChangeLogRepository : IRepository<EntityChangeLog, Guid>
    {
        /// <summary>
        /// Gets entity change logs for a specific entity
        /// </summary>
        /// <param name="entityId">The entity ID</param>
        /// <param name="entityTypeFullName">The full type name of the entity</param>
        /// <param name="includeDetails">Whether to include property changes</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of entity change logs</returns>
        Task<List<EntityChangeLog>> GetEntityChangeLogsAsync(
            string entityId,
            string entityTypeFullName,
            bool includeDetails = true,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets the latest entity change log for a specific entity
        /// </summary>
        /// <param name="entityId">The entity ID</param>
        /// <param name="entityTypeFullName">The full type name of the entity</param>
        /// <param name="includeDetails">Whether to include property changes</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The latest entity change log or null</returns>
        Task<EntityChangeLog?> GetLatestEntityChangeLogAsync(
            string entityId,
            string entityTypeFullName,
            bool includeDetails = true,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets entity change logs for a specific entity type
        /// </summary>
        /// <param name="entityTypeFullName">The full type name of the entity</param>
        /// <param name="maxResultCount">Maximum number of results to return</param>
        /// <param name="skipCount">Number of results to skip</param>
        /// <param name="includeDetails">Whether to include property changes</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of entity change logs</returns>
        Task<List<EntityChangeLog>> GetEntityTypeChangeLogsAsync(
            string entityTypeFullName,
            int maxResultCount = 10,
            int skipCount = 0,
            bool includeDetails = true,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets entity change logs within a specific time range
        /// </summary>
        /// <param name="startTime">Start time</param>
        /// <param name="endTime">End time</param>
        /// <param name="maxResultCount">Maximum number of results to return</param>
        /// <param name="skipCount">Number of results to skip</param>
        /// <param name="includeDetails">Whether to include property changes</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of entity change logs</returns>
        Task<List<EntityChangeLog>> GetTimeRangeChangeLogsAsync(
            DateTime startTime,
            DateTime endTime,
            int maxResultCount = 10,
            int skipCount = 0,
            bool includeDetails = true,
            CancellationToken cancellationToken = default);
    }
}
