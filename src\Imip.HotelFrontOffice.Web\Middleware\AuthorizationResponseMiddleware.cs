using System;
using System.IO;
using System.Net;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Volo.Abp.DependencyInjection;

namespace Imip.HotelFrontOffice.Web.Middleware;

public class AuthorizationResponseMiddleware : IMiddleware, ITransientDependency
{
    private readonly ILogger<AuthorizationResponseMiddleware> _logger;

    public AuthorizationResponseMiddleware(ILogger<AuthorizationResponseMiddleware> logger)
    {
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, RequestDelegate next)
    {
        // Create a wrapper for the original response body stream
        var originalBodyStream = context.Response.Body;

        try
        {
            // Create a new memory stream to capture the response
            using var responseBody = new MemoryStream();
            context.Response.Body = responseBody;

            // Continue down the pipeline
            await next(context);

            // Check if we have a 403 status code
            if (context.Response.StatusCode == (int)HttpStatusCode.Forbidden)
            {
                // Reset the stream position to the beginning
                responseBody.Seek(0, SeekOrigin.Begin);

                // Read the response body to check if it's empty
                string responseText = await new StreamReader(responseBody).ReadToEndAsync();

                // If the response is empty or not a valid JSON, replace it with our custom JSON
                if (string.IsNullOrWhiteSpace(responseText) || !IsValidJson(responseText))
                {
                    // Reset the response to add our custom JSON
                    responseBody.SetLength(0);

                    // Check if we have information about the denied permission
                    string deniedPermission = "unknown";
                    if (context.Items.ContainsKey("DeniedPermission"))
                    {
                        deniedPermission = context.Items["DeniedPermission"]?.ToString() ?? "unknown";
                    }

                    var errorResponse = new
                    {
                        error = new
                        {
                            code = "Forbidden",
                            message = "You do not have permission to access this resource",
                            details = $"The required permission '{deniedPermission}' is not granted for this operation"
                        }
                    };

                    var jsonResponse = JsonSerializer.Serialize(errorResponse);

                    // Write the JSON response to the memory stream
                    var bytes = Encoding.UTF8.GetBytes(jsonResponse);
                    await responseBody.WriteAsync(bytes, 0, bytes.Length);

                    // Set the content type and length
                    context.Response.ContentType = "application/json";
                    context.Response.ContentLength = bytes.Length;
                }
            }

            // Copy the contents of the new memory stream to the original stream
            responseBody.Seek(0, SeekOrigin.Begin);
            await responseBody.CopyToAsync(originalBodyStream);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in AuthorizationResponseMiddleware");
            throw;
        }
        finally
        {
            // Restore the original response body stream
            context.Response.Body = originalBodyStream;
        }
    }

    private bool IsValidJson(string text)
    {
        if (string.IsNullOrWhiteSpace(text))
            return false;

        try
        {
            using (JsonDocument.Parse(text))
            {
                return true;
            }
        }
        catch
        {
            return false;
        }
    }
}


public static class AuthorizationResponseMiddlewareExtensions
{
    public static IApplicationBuilder UseAuthorizationResponseMiddleware(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<AuthorizationResponseMiddleware>();
    }

    public static IApplicationBuilder UseTokenExpirationMiddleware(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<TokenExpirationMiddleware>();
    }
}