using System;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.HotelFrontOffice.EntityChangeLogs
{
    /// <summary>
    /// Represents a log entry for a property change within an entity
    /// </summary>
    public class EntityPropertyChangeLog : FullAuditedEntity<Guid>
    {
        /// <summary>
        /// ID of the related entity change log
        /// </summary>
        public Guid EntityChangeLogId { get; set; }

        /// <summary>
        /// Name of the property that was changed
        /// </summary>
        public string? PropertyName { get; set; }

        /// <summary>
        /// User-friendly display name of the property
        /// </summary>
        public string? PropertyDisplayName { get; set; }

        /// <summary>
        /// Original value of the property before the change
        /// </summary>
        public string? OriginalValue { get; set; }

        /// <summary>
        /// New value of the property after the change
        /// </summary>
        public string? NewValue { get; set; }

        /// <summary>
        /// Full type name of the property
        /// </summary>
        public string? PropertyTypeFullName { get; set; }

        /// <summary>
        /// Navigation property to the parent entity change log
        /// </summary>
        public virtual EntityChangeLog? EntityChangeLog { get; set; }

        protected EntityPropertyChangeLog()
        {
            // Initialize required properties with empty strings
            PropertyName = string.Empty;
            PropertyTypeFullName = string.Empty;
            PropertyDisplayName = string.Empty;
        }

        public EntityPropertyChangeLog(
            Guid id,
            Guid entityChangeLogId,
            string? propertyName,
            string? propertyTypeFullName,
            string? originalValue = null,
            string? newValue = null,
            string? propertyDisplayName = null) : base(id)
        {
            EntityChangeLogId = entityChangeLogId;
            PropertyName = propertyName;
            PropertyTypeFullName = propertyTypeFullName;
            OriginalValue = originalValue;
            NewValue = newValue;
            PropertyDisplayName = propertyDisplayName ?? propertyName;
        }
    }
}
