using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Models;
using Imip.HotelFrontOffice.Permissions;
using Imip.HotelFrontOffice.Reservations;
using Imip.HotelFrontOffice.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.Controllers.Reservations;

[Route("api/app/reservations")]
[RemoteService]
public class ReservationsController : HotelFrontOfficeController
{
    private readonly IReservationsAppService _reservationAppService;
    private readonly IRepository<Reservation> _repository;
    private readonly ILogger<ReservationsController> _logger;

    public ReservationsController(
        IReservationsAppService reportAppService,
        IRepository<Reservation> repository,
        ILogger<ReservationsController> logger)
    {
        _reservationAppService = reportAppService;
        _repository = repository;
        _logger = logger;
    }

    /// <summary>
    /// Get a paged list of reservations with dynamic filtering and sorting
    /// </summary>
    /// <param name="input">Query parameters including filtering and sorting</param>
    /// <returns>Paged list of reservations in standard ABP format</returns>
    [HttpPost("list")]
    [Authorize(WismaAppPermissions.PolicyReservation.View)]
    [ProducesResponseType(typeof(PagedResultDto<ReservationsDto>), StatusCodes.Status200OK)]
    public virtual async Task<PagedResultDto<ReservationsDto>> GetPagedListAsync(QueryParameters input)
    {
        try
        {
            // Get the base query with includes
            var query = await ExecuteDynamicQueryAsync(input);

            // Get total count before paging
            var totalCount = await query.CountAsync();

            // Apply paging
            var items = await query
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount)
                .ToListAsync();

            // Map to DTOs
            var dtos = ObjectMapper.Map<List<Reservation>, List<ReservationsDto>>(items);

            // Add additional properties
            foreach (var dto in dtos)
            {
                if (dto.ReservationDetails != null)
                {
                    foreach (var detail in dto.ReservationDetails)
                    {
                        if (detail.Room != null)
                        {
                            detail.RoomNumber = detail.Room.RoomNumber;
                        }

                        if (detail.Guest != null)
                        {
                            detail.GuestName = detail.Guest.Fullname;
                        }
                    }
                }
            }

            // Return standard ABP paged result
            return new PagedResultDto<ReservationsDto>
            {
                TotalCount = totalCount,
                Items = dtos
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting paged list of reservations: {Message}", ex.Message);
            throw new UserFriendlyException(
                "Could not retrieve reservations list",
                "Error.ReservationsList.Failed",
                ex.Message);
        }
    }

    /// <summary>
    /// Execute dynamic query with filtering and sorting
    /// </summary>
    protected virtual async Task<IQueryable<Reservation>> ExecuteDynamicQueryAsync(QueryParameters parameters)
    {
        // Get base query with includes
        var query = await _repository.GetQueryableAsync();

        // Add includes for related entities
        query = query
            .AsNoTracking()
            .Include(x => x.PaymentMethod)
            .Include(x => x.Status)
            .Include(x => x.DiningOptions)
            .Include(x => x.Company)
            .Include(x => x.ReservationType);

        // Check if we need to include deeper relationships based on filter or sort fields
        var fieldsToCheck = new List<string>();

        // Add filter fields
        if (parameters.FilterGroup?.Conditions != null)
        {
            fieldsToCheck.AddRange(parameters.FilterGroup.Conditions
                .Where(c => c.FieldName.Contains('.'))
                .Select(c => c.FieldName));
        }

        // Add sort fields
        if (parameters.Sort?.Count > 0)
        {
            fieldsToCheck.AddRange(parameters.Sort
                .Where(s => s.Field.Contains('.'))
                .Select(s => s.Field));
        }

        // Include reservation details if needed
        if (fieldsToCheck.Any(f => f.StartsWith("reservationDetails.")))
        {
            // Include each relationship separately to avoid nullability issues
            query = query.Include(x => x.ReservationDetails);

            // Check if we need specific nested includes
            if (fieldsToCheck.Any(f => f.Contains("reservationDetails.room")))
            {
                query = query.Include("ReservationDetails.Room");
            }

            if (fieldsToCheck.Any(f => f.Contains("reservationDetails.guest")))
            {
                query = query.Include("ReservationDetails.Guest");
            }

            if (fieldsToCheck.Any(f => f.Contains("reservationDetails.status")))
            {
                query = query.Include("ReservationDetails.Status");
            }
        }

        // Apply filters
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<Reservation>.ApplyFilters(query, parameters.FilterGroup);
        }

        // Apply sorting
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<Reservation>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            // Use the default sorting if provided using Dynamic LINQ
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            // Default sorting by creation time descending
            query = query.OrderByDescending(x => x.CreationTime);
        }

        return query;
    }
}
