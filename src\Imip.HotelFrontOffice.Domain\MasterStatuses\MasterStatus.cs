﻿using System;
using System.Collections.Generic;
using Imip.HotelFrontOffice.Guests;
using Imip.HotelFrontOffice.ReservationDetails;
using Imip.HotelFrontOffice.ReservationFoodAndBeverages;
using Imip.HotelFrontOffice.ReservationRooms;
using Imip.HotelFrontOffice.Reservations;
using Imip.HotelFrontOffice.ReservationTypes;
using Imip.HotelFrontOffice.RoomTypes;
using Imip.HotelFrontOffice.ServiceTypes;
using Imip.HotelFrontOffice.TypeFoodAndBeverages;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.HotelFrontOffice.MasterStatuses;

public class MasterStatus : FullAuditedAggregateRoot<Guid>
{
    public string Name { get; set; } = default!;
    public string DocType { get; set; } = default!;

    public string? Color { get; set; }
    public string? Code { get; set; } = default!;

    public virtual ICollection<Reservation> Reservations { get; set; }
    public virtual ICollection<ReservationDetail> ReservationDetails { get; set; }
    public virtual ICollection<ReservationDetail> ReservationCriteria { get; set; }
    public virtual ICollection<ReservationDetail> ReservationDetailPaymentStatus { get; set; }
    public virtual ICollection<ReservationFoodAndBeverage> ReservationFoodAndBeveragePaymentStatus { get; set; }
    public virtual ICollection<ReservationRoom> ReservationRoomPaymentStatus { get; set; }
    public virtual ICollection<ReservationType> ReservationTypes { get; set; }
    public virtual ICollection<RoomType> RoomTypes { get; set; }
    public virtual ICollection<TypeFoodAndBeverage> TypeFoodAndBeverages { get; set; }
    public virtual ICollection<ServiceType> ServiceTypes { get; set; }
    public virtual ICollection<Guest> Guests { get; set; }
    public virtual ICollection<Payments.Payment> Payments { get; set; }

    protected MasterStatus()
    {
        Reservations = new HashSet<Reservation>();
        ReservationDetails = new HashSet<ReservationDetail>();
        ReservationTypes = new HashSet<ReservationType>();
        RoomTypes = new HashSet<RoomType>();
        TypeFoodAndBeverages = new HashSet<TypeFoodAndBeverage>();
        ServiceTypes = new HashSet<ServiceType>();
        Guests = new HashSet<Guest>();
        Payments = new HashSet<Payments.Payment>();
        ReservationDetailPaymentStatus = new HashSet<ReservationDetail>();
        ReservationFoodAndBeveragePaymentStatus = new HashSet<ReservationFoodAndBeverage>();
        ReservationRoomPaymentStatus = new HashSet<ReservationRoom>();
    }

    public MasterStatus(Guid id, string name, string docType, string? color, string? code) : this()
    {
        Id = id;
        Name = name;
        DocType = docType;
        Color = color;
        Code = code;
    }
}