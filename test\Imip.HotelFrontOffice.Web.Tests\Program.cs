using Microsoft.AspNetCore.Builder;
using Imip.HotelFrontOffice;
using Volo.Abp.AspNetCore.TestBase;

var builder = WebApplication.CreateBuilder();
builder.Environment.ContentRootPath = GetWebProjectContentRootPathHelper.Get("Imip.HotelFrontOffice.Web.csproj"); 
await builder.RunAbpModuleAsync<HotelFrontOfficeWebTestModule>(applicationName: "Imip.HotelFrontOffice.Web");

public partial class Program
{
}
