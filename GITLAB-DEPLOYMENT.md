﻿# Deploying to Kubernetes with GitLab CI/CD

This guide explains how to deploy the Imip.HotelFrontOffice application to Kubernetes using GitLab CI/CD.

## Overview

The deployment process consists of the following stages:

1. **Build**: Builds and pushes Docker images for the web application and database migrator
2. **Test**: Runs unit tests
3. **Migrate**: Creates Kubernetes ConfigMaps and Secrets, then runs the database migrator
4. **Deploy**: Deploys the web application to Kubernetes

## Prerequisites

- A GitLab.com account
- A Kubernetes cluster
- A container registry (GitLab's Container Registry is used by default)
- kubectl installed on your local machine (for initial setup)

## Setting Up GitLab CI/CD Variables

You need to set up the following variables in your GitLab project (Settings > CI/CD > Variables):

### Connection Variables

| Variable      | Description                                                          |
| ------------- | -------------------------------------------------------------------- |
| `KUBE_CONFIG` | Base64-encoded kubeconfig file for accessing your Kubernetes cluster |

### Database Variables

| Variable           | Description                                   |
| ------------------ | --------------------------------------------- |
| `DB_PASSWORD_DEV`  | Database password for development environment |
| `DB_PASSWORD_PROD` | Database password for production environment  |

### Application Variables

| Variable                | Description                        |
| ----------------------- | ---------------------------------- |
| `SEQ_SERVER_URL`        | URL for Seq logging server         |
| `SEQ_API_KEY`           | API key for Seq logging server     |
| `CERT_PASSPHRASE`       | Passphrase for the SSL certificate |
| `ENCRYPTION_PASSPHRASE` | Passphrase for string encryption   |

## Deployment Process

### Development Environment

The development environment is automatically deployed when changes are pushed to the `dev` branch:

1. Docker images are built and pushed to the GitLab Container Registry
2. Unit tests are run
3. Kubernetes ConfigMap and Secrets are created/updated
4. Database migrations are run
5. The web application is deployed

### Production Environment

The production deployment requires manual approval:

1. Push changes to the `main` branch
2. Docker images are built and pushed to the GitLab Container Registry
3. Unit tests are run
4. Manually trigger the `prepare_prod_config` job
5. Manually trigger the `migrate_prod` job
6. Manually trigger the `deploy_prod` job

## Customizing the Deployment

### Environment URLs

You can customize the application URLs by modifying the following variables in the `.gitlab-ci.yml` file:

```yaml
# Development environment variables
DEV_APP_URL: "https://identity-dev.yourdomain.com"
DEV_CLIENT_URL: "https://client-dev.yourdomain.com"
DEV_CORS_ORIGINS: "https://client-dev.yourdomain.com"

# Production environment variables
PROD_APP_URL: "https://identity.yourdomain.com"
PROD_CLIENT_URL: "https://client.yourdomain.com"
PROD_CORS_ORIGINS: "https://client.yourdomain.com"
```

### Database Connection

You can customize the database connection strings by modifying the following variables in the `.gitlab-ci.yml` file:

```yaml
# Development environment variables
DEV_DB_CONNECTION: "Server=dev-db-server;Database=IdentityServer;User ID=sa;Password=${DB_PASSWORD_DEV};TrustServerCertificate=true;Encrypt=true;MultipleActiveResultSets=false;"

# Production environment variables
PROD_DB_CONNECTION: "Server=prod-db-server;Database=IdentityServer;User ID=sa;Password=${DB_PASSWORD_PROD};TrustServerCertificate=true;Encrypt=true;MultipleActiveResultSets=false;"
```

### Kubernetes Resources

You can adjust the CPU and memory resources for the application by modifying the resource limits in the deployment sections of the `.gitlab-ci.yml` file.

## Troubleshooting

### Checking Logs

To check the logs of your application:

```bash
# For development environment
kubectl logs -n imip-wisma-dev-new deployment/imip-wisma-web

# For production environment
kubectl logs -n imip-wisma-prod deployment/imip-wisma-web
```

### Checking Migration Status

To check the status of the database migration job:

```bash
# For development environment
kubectl get jobs -n imip-wisma-dev-new

# For production environment
kubectl get jobs -n imip-wisma-prod
```

### Rerunning Migrations

If you need to rerun migrations, you can manually delete the job and then rerun the pipeline:

```bash
# For development environment
kubectl delete job imip-wisma-db-migrator-<commit-sha> -n imip-wisma-dev-new

# For production environment
kubectl delete job imip-wisma-db-migrator-<commit-sha> -n imip-wisma-prod
```
