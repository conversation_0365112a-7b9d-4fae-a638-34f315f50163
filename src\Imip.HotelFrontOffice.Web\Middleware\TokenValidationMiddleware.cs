using System;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Net;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Imip.HotelFrontOffice.Web.Extensions;
using Volo.Abp.DependencyInjection;
using Microsoft.AspNetCore.Builder;

namespace Imip.HotelFrontOffice.Web.Middleware;

public class TokenValidationMiddleware : IMiddleware, ITransientDependency
{
    private readonly ILogger<TokenValidationMiddleware> _logger;

    public TokenValidationMiddleware(ILogger<TokenValidationMiddleware> logger)
    {
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, RequestDelegate next)
    {
        // Skip token validation for non-API paths or paths that don't require authentication
        if (ShouldSkipValidation(context.Request.Path))
        {
            await next(context);
            return;
        }

        // Get the access token
        var accessToken = await context.GetAccessTokenWithFallbackAsync();

        if (string.IsNullOrEmpty(accessToken))
        {
            // No token found, let the authorization handlers handle this
            await next(context);
            return;
        }

        // Validate the token lifetime
        if (!IsTokenValid(accessToken, out var errorMessage))
        {
            // Return 401 Unauthorized with proper error response
            await SendUnauthorizedResponseAsync(context, errorMessage);
            return;
        }

        // Token is valid, continue with the request
        await next(context);
    }

    private static bool ShouldSkipValidation(PathString path)
    {
        return !path.StartsWithSegments("/api") ||
               path.StartsWithSegments("/api/abp") ||
               path.StartsWithSegments("/swagger");
    }

    private async Task SendUnauthorizedResponseAsync(HttpContext context, string errorMessage)
    {
        _logger.LogWarning("Token validation failed: {ErrorMessage}", errorMessage);

        // Return 401 Unauthorized
        context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
        context.Response.ContentType = "application/json";

        var response = new
        {
            error = "invalid_token",
            error_description = errorMessage
        };

        await context.Response.WriteAsync(JsonSerializer.Serialize(response));
    }

    private bool IsTokenValid(string token, out string errorMessage)
    {
        errorMessage = string.Empty;

        try
        {
            // Only log token length for security
            _logger.LogDebug("Validating token: {TokenLength} characters", token?.Length ?? 0);

            var jwtHandler = new JwtSecurityTokenHandler();

            // Read the token without validating signature
            if (!jwtHandler.CanReadToken(token))
            {
                errorMessage = "Invalid token format";
                _logger.LogWarning("Token format is invalid");
                return false;
            }

            var jwtToken = jwtHandler.ReadJwtToken(token);
            var currentTime = DateTime.UtcNow;

            // Check expiration
            var expClaim = jwtToken.Claims.FirstOrDefault(c => c.Type == "exp");
            if (expClaim == null)
            {
                errorMessage = "Token does not contain expiration claim";
                _logger.LogWarning("Token does not contain expiration claim");
                return false;
            }

            if (long.TryParse(expClaim.Value, out var expirationUnix))
            {
                var expirationDateTime = DateTimeOffset.FromUnixTimeSeconds(expirationUnix).UtcDateTime;

                if (expirationDateTime < currentTime)
                {
                    errorMessage = "Token has expired";
                    _logger.LogWarning("Token expired at {ExpirationTime}, current time is {CurrentTime}",
                        expirationDateTime, currentTime);
                    return false;
                }
            }
            else
            {
                errorMessage = "Invalid expiration claim format";
                _logger.LogWarning("Invalid expiration claim format");
                return false;
            }

            // Check not before (nbf) claim if present
            var nbfClaim = jwtToken.Claims.FirstOrDefault(c => c.Type == "nbf");
            if (nbfClaim != null && long.TryParse(nbfClaim.Value, out var notBeforeUnix))
            {
                var notBeforeDateTime = DateTimeOffset.FromUnixTimeSeconds(notBeforeUnix).UtcDateTime;

                if (notBeforeDateTime > currentTime)
                {
                    errorMessage = "Token is not yet valid";
                    _logger.LogWarning("Token is not valid until {NotBeforeTime}, current time is {CurrentTime}",
                        notBeforeDateTime, currentTime);
                    return false;
                }
            }

            return true;
        }
        catch (Exception ex)
        {
            errorMessage = $"Token validation error: {ex.Message}";
            _logger.LogError(ex, "Error validating token");
            return false;
        }
    }
}


public static class TokenValidationMiddlewareExtensions
{
    public static IApplicationBuilder UseTokenValidation(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<TokenValidationMiddleware>();
    }
}