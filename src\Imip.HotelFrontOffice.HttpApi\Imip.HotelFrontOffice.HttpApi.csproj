﻿<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>Imip.HotelFrontOffice</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Imip.HotelFrontOffice.Application.Contracts\Imip.HotelFrontOffice.Application.Contracts.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.PermissionManagement.HttpApi" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.FeatureManagement.HttpApi" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.SettingManagement.HttpApi" Version="9.0.4" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Identity.HttpApi" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.Account.HttpApi" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.TenantManagement.HttpApi" Version="9.0.4" />
  </ItemGroup>

</Project>
