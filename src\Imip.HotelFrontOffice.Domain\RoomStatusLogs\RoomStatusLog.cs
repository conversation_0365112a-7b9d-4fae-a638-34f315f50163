﻿using System;
using Imip.HotelFrontOffice.Rooms;
using Imip.HotelFrontOffice.RoomStatuses;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.HotelFrontOffice.RoomStatusLogs;

public class RoomStatusLog : FullAuditedAggregateRoot<Guid>
{
    public Guid RoomId { get; set; }
    public Guid RoomStatusId { get; set; }
    public string? StatusSource { get; set; }

    public virtual Room? Room { get; set; }
    public virtual RoomStatus? RoomStatus { get; set; }

    protected RoomStatusLog()
    {
    }

    public RoomStatusLog(
        Guid id,
        Guid roomId,
        Guid roomStatusId,
        string statusSource
    ) : base(id)
    {
        RoomId = roomId;
        RoomStatusId = roomStatusId;
        StatusSource = statusSource;
    }
}