﻿using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.DependencyInjection;
using NSubstitute;
using Volo.Abp.Identity;
using Volo.Abp.Modularity;
using Volo.Abp.PermissionManagement;

namespace Imip.HotelFrontOffice;

[DependsOn(
    typeof(HotelFrontOfficeApplicationModule),
    typeof(HotelFrontOfficeDomainTestModule)
)]
public class HotelFrontOfficeApplicationTestModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        // Configure identity module to use mocked repositories
        ConfigureIdentityMocks(context.Services);

        // Disable dynamic permission store
        Configure<PermissionManagementOptions>(options =>
        {
            options.SaveStaticPermissionsToDatabase = false;
            options.IsDynamicPermissionStoreEnabled = false;
        });
    }

    private void ConfigureIdentityMocks(IServiceCollection services)
    {
        // Create mock repositories for identity module
        var mockRoleRepository = Substitute.For<IIdentityRoleRepository>();
        var mockUserRepository = Substitute.For<IIdentityUserRepository>();

        // Register the mock repositories
        services.AddSingleton(mockRoleRepository);
        services.AddSingleton(mockUserRepository);

        // Configure identity options
        services.Configure<IdentityOptions>(options =>
        {
            options.ClaimsIdentity.UserIdClaimType = "sub";
        });
    }
}
