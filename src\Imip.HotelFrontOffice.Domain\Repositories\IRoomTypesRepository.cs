﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.Repositories
{
    public interface IRoomTypesRepository : IRepository<RoomTypes.RoomType, Guid>
    {
        Task<RoomTypes.RoomType?> FindByNameAsync(string name);

        Task<List<RoomTypes.RoomType>> GetActiveAsync();
    }
}