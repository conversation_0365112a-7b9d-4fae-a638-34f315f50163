﻿using System;
using System.Collections.Generic;
using Imip.HotelFrontOffice.TypeFoodAndBeverages;
using Volo.Abp.Data;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.HotelFrontOffice.FoodAndBeverages;

public class FoodAndBeverage : FullAuditedAggregateRoot<Guid>
{
    public string Name { get; set; } = default!;
    public decimal Price { get; set; }
    public string Information { get; set; } = default!;

    // Foreign key property
    public Guid TypeFoodAndBeverageId { get; set; }

    // Navigation property (one side)
    public virtual TypeFoodAndBeverage TypeFoodAndBeverage { get; set; } = default!;

    public virtual ICollection<ReservationFoodAndBeverages.ReservationFoodAndBeverage>? ReservationFoodAndBeverages
    {
        get;
        set;
    }

    protected FoodAndBeverage()
    {
    }

    public FoodAndBeverage(
        Guid id,
        string name,
        decimal price,
        string information,
        Guid typeFoodAndBeverageId
        )
    {
        Id = id;
        Name = name;
        Price = price;
        Information = information;
        TypeFoodAndBeverageId = typeFoodAndBeverageId;
    }

    // Helper methods for ExtraProperties

    /// <summary>
    /// Sets a value in the ExtraProperties dictionary
    /// </summary>
    public void SetExtraProperty<T>(string key, T value)
    {
        this.SetProperty(key, value);
    }

    /// <summary>
    /// Gets a value from the ExtraProperties dictionary
    /// </summary>
    public T GetExtraProperty<T>(string key)
    {
        return this.GetProperty<T>(key);
    }

    /// <summary>
    /// Removes a property from the ExtraProperties dictionary
    /// </summary>
    public void RemoveExtraProperty(string key)
    {
        this.RemoveProperty(key);
    }
}