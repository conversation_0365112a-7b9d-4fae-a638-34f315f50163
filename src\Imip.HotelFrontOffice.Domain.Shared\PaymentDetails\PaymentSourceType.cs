using System.ComponentModel.DataAnnotations;

namespace Imip.HotelFrontOffice.PaymentDetails;

/// <summary>
/// Enum for payment source types
/// </summary>
public enum PaymentSourceType
{
    [Display(Name = "Reservation Room")]
    ReservationRoom = 1,

    [Display(Name = "Reservation Room Food And Beverage")]
    ReservationRoomFoodAndBeverage = 2,

    [Display(Name = "Reservation Room Service")]
    ReservationRoomService = 3
}
