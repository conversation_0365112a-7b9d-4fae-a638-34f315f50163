using Imip.HotelFrontOffice.Localization;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Localization;
using Volo.Abp.MultiTenancy;

namespace Imip.HotelFrontOffice.Permissions;

public class WismaAppPermissionDefinitionProvider : PermissionDefinitionProvider
{
    public override void Define(IPermissionDefinitionContext context)
    {
        PermissionGroupDefinition wismaGroup;

        try
        {
            // Try to add the group - this might throw if it already exists
            wismaGroup = context.AddGroup(WismaAppPermissions.GroupName, L("WismaApp"));
        }
        catch (Volo.Abp.AbpException ex) when (ex.Message.Contains("There is already an existing permission group"))
        {
            // If the group already exists, get it
            wismaGroup = context.GetGroup(WismaAppPermissions.GroupName);
        }

        DefineWismaPermissions(wismaGroup);
    }

    private void DefineWismaPermissions(PermissionGroupDefinition group)
    {
        // Define permissions only if they don't already exist
        SafeDefinePermission(group, "RoomType", WismaAppPermissions.PolicyRoomType.Default);
        SafeDefinePermission(group, "RoomStatus", WismaAppPermissions.PolicyRoomStatus.Default);
        SafeDefinePermission(group, "Room", WismaAppPermissions.PolicyRoom.Default);
        SafeDefinePermission(group, "ReservationRoom", WismaAppPermissions.PolicyReservationRoom.Default);
        SafeDefinePermission(group, "Guest", WismaAppPermissions.PolicyGuest.Default);
        SafeDefinePermission(group, "ReservationType", WismaAppPermissions.PolicyReservationType.Default);
        SafeDefinePermission(group, "Reservation", WismaAppPermissions.PolicyReservation.Default);
        SafeDefinePermission(group, "Payment", WismaAppPermissions.PolicyPayment.Default);
        SafeDefinePermission(group, "FoodAndBeverageType", WismaAppPermissions.PolicyFoodAndBeverageType.Default);
        SafeDefinePermission(group, "FoodAndBeverage", WismaAppPermissions.PolicyFoodAndBeverage.Default);
        SafeDefinePermission(group, "ServiceType", WismaAppPermissions.PolicyServiceType.Default);
        SafeDefinePermission(group, "Service", WismaAppPermissions.PolicyService.Default);
        SafeDefinePermission(group, "Settings", WismaAppPermissions.PolicySettings.Default);
        SafeDefinePermission(group, "Tax", WismaAppPermissions.PolicyTax.Default);
        SafeDefinePermission(group, "Report", WismaAppPermissions.PolicyReport.Default);

        // Add special permissions for Reservation
        SafeAddPermission(group, WismaAppPermissions.PolicyReservation.Default + ".CheckIn", L("CheckIn"));
        SafeAddPermission(group, WismaAppPermissions.PolicyReservation.Default + ".CheckOut", L("CheckOut"));
        SafeAddPermission(group, WismaAppPermissions.PolicyReservation.Default + ".RoomService", L("RoomService"));
        SafeAddPermission(group, WismaAppPermissions.PolicyReservation.Default + ".RoomFoodAndBeverage", L("RoomFoodAndBeverage"));
    }

    private void DefinePermission(PermissionGroupDefinition group, string displayName, string defaultPermission)
    {
        var permission = group.AddPermission(defaultPermission, L(displayName));
        permission.AddChild(defaultPermission + ".View", L("View"));
        permission.AddChild(defaultPermission + ".Create", L("Create"));
        permission.AddChild(defaultPermission + ".Edit", L("Edit"));
        permission.AddChild(defaultPermission + ".Delete", L("Delete"));
    }

    private void SafeDefinePermission(PermissionGroupDefinition group, string displayName, string defaultPermission)
    {
        try
        {
            // Try to add the permission - this might throw if it already exists
            var permission = group.AddPermission(defaultPermission, L(displayName));

            try { permission.AddChild(defaultPermission + ".View", L("View")); } catch { }
            try { permission.AddChild(defaultPermission + ".Create", L("Create")); } catch { }
            try { permission.AddChild(defaultPermission + ".Edit", L("Edit")); } catch { }
            try { permission.AddChild(defaultPermission + ".Delete", L("Delete")); } catch { }
        }
        catch
        {
            // Permission already exists, ignore the error
        }
    }

    private void SafeAddPermission(PermissionGroupDefinition group, string name, LocalizableString displayName)
    {
        try
        {
            // Try to add the permission - this might throw if it already exists
            group.AddPermission(name, displayName);
        }
        catch
        {
            // Permission already exists, ignore the error
        }
    }

    private static LocalizableString L(string name)
    {
        return LocalizableString.Create<HotelFrontOfficeResource>(name);
    }
}
