﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.HotelFrontOffice.Migrations
{
    /// <inheritdoc />
    public partial class AddPaymentTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "PaymentStatusId",
                table: "AppReservationRooms",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "PaymentStatusId",
                table: "AppReservationFoodAndBeverages",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "PaymentStatusId",
                table: "AppReservationDetails",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationRooms_PaymentStatusId",
                table: "AppReservationRooms",
                column: "PaymentStatusId");

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationFoodAndBeverages_PaymentStatusId",
                table: "AppReservationFoodAndBeverages",
                column: "PaymentStatusId");

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationDetails_PaymentStatusId",
                table: "AppReservationDetails",
                column: "PaymentStatusId");

            migrationBuilder.AddForeignKey(
                name: "FK_AppReservationDetails_MasterStatus_PaymentStatusId",
                table: "AppReservationDetails",
                column: "PaymentStatusId",
                principalTable: "MasterStatus",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AppReservationFoodAndBeverages_MasterStatus_PaymentStatusId",
                table: "AppReservationFoodAndBeverages",
                column: "PaymentStatusId",
                principalTable: "MasterStatus",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AppReservationRooms_MasterStatus_PaymentStatusId",
                table: "AppReservationRooms",
                column: "PaymentStatusId",
                principalTable: "MasterStatus",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AppReservationDetails_MasterStatus_PaymentStatusId",
                table: "AppReservationDetails");

            migrationBuilder.DropForeignKey(
                name: "FK_AppReservationFoodAndBeverages_MasterStatus_PaymentStatusId",
                table: "AppReservationFoodAndBeverages");

            migrationBuilder.DropForeignKey(
                name: "FK_AppReservationRooms_MasterStatus_PaymentStatusId",
                table: "AppReservationRooms");

            migrationBuilder.DropIndex(
                name: "IX_AppReservationRooms_PaymentStatusId",
                table: "AppReservationRooms");

            migrationBuilder.DropIndex(
                name: "IX_AppReservationFoodAndBeverages_PaymentStatusId",
                table: "AppReservationFoodAndBeverages");

            migrationBuilder.DropIndex(
                name: "IX_AppReservationDetails_PaymentStatusId",
                table: "AppReservationDetails");

            migrationBuilder.DropColumn(
                name: "PaymentStatusId",
                table: "AppReservationRooms");

            migrationBuilder.DropColumn(
                name: "PaymentStatusId",
                table: "AppReservationFoodAndBeverages");

            migrationBuilder.DropColumn(
                name: "PaymentStatusId",
                table: "AppReservationDetails");
        }
    }
}
