using System;
using System.ComponentModel.DataAnnotations;
using Imip.HotelFrontOffice.Documents;

namespace Imip.HotelFrontOffice.Documents;

/// <summary>
/// DTO for creating a document template
/// </summary>
public class CreateDocumentTemplateDto
{
    /// <summary>
    /// The name of the template
    /// </summary>
    [Required]
    [StringLength(100)]
    public required string Name { get; set; }

    /// <summary>
    /// The document type
    /// </summary>
    [Required]
    public DocumentType DocumentType { get; set; }

    /// <summary>
    /// The attachment ID that contains the DOCX template
    /// </summary>
    [Required]
    public Guid AttachmentId { get; set; }

    /// <summary>
    /// Description of the template
    /// </summary>
    [StringLength(500)]
    public string? Description { get; set; }

    /// <summary>
    /// Whether this is the default template for the document type
    /// </summary>
    public bool IsDefault { get; set; }
}