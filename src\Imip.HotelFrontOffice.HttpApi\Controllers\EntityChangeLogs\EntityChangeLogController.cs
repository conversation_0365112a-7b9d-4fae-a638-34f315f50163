using System;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.EntityChangeLogs;
using Imip.HotelFrontOffice.EntityChangeLogs.Dtos;
using Imip.HotelFrontOffice.Models;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp.Application.Dtos;

namespace Imip.HotelFrontOffice.Controllers.EntityChangeLogs
{
    [Route("api/change-log")]
    public class EntityChangeLogController : HotelFrontOfficeController
    {
        private readonly IEntityChangeLogAppService _entityChangeLogAppService;

        public EntityChangeLogController(
            IEntityChangeLogAppService entityChangeLogAppService)
        {
            _entityChangeLogAppService = entityChangeLogAppService;
        }

        [HttpGet]
        public async Task<object> GetAuditLogsWithDetailsAsync(
            [FromQuery] DateTime startTime,
            [FromQuery] DateTime endTime,
            [FromQuery] int maxResultCount = 10,
            [FromQuery] int skipCount = 0)
        {
            // Get audit logs with proper pagination directly from the service
            var result = await _entityChangeLogAppService.GetAuditLogsWithDetailsAsync(
                startTime, endTime, maxResultCount, skipCount);

            // Return the result directly
            return new
            {
                totalCount = result.TotalCount,
                items = result.Items
            };
        }

        /// <summary>
        /// Get a paged list of entity change logs with dynamic filtering and sorting
        /// </summary>
        /// <param name="input">Query parameters including filtering and sorting</param>
        /// <returns>Paged list of entity change logs in standard ABP format</returns>
        [HttpPost("entity")]
        public async Task<object> GetEntityChangeLogsAsync(
            [FromBody] QueryParameters input)
        {
            // Use the new dynamic query method in the service
            var result = await _entityChangeLogAppService.GetEntityChangeLogsWithDynamicQueryAsync(input);

            // Return the result directly
            return new
            {
                totalCount = result.TotalCount,
                items = result.Items
            };
        }

        [HttpGet("entity/latest")]
        public async Task<EntityChangeLogDto> GetLatestEntityChangeLogAsync(
            [FromQuery] string entityId,
            [FromQuery] string entityTypeFullName)
        {
            return await _entityChangeLogAppService.GetLatestEntityChangeLogAsync(
                entityId, entityTypeFullName);
        }

        [HttpGet("entity-type")]
        public async Task<object> GetEntityTypeChangeLogsAsync(
            [FromQuery] string entityTypeFullName,
            [FromQuery] int maxResultCount = 10,
            [FromQuery] int skipCount = 0)
        {
            // Get logs for the entity type with proper pagination
            var logs = await _entityChangeLogAppService.GetEntityTypeChangeLogsAsync(
                entityTypeFullName, maxResultCount, skipCount);

            // Return a custom response with the correct pagination
            return new
            {
                totalCount = logs.Count,
                items = logs
            };
        }

        [HttpGet("time-range")]
        public async Task<object> GetTimeRangeChangeLogsAsync(
            [FromQuery] DateTime startTime,
            [FromQuery] DateTime endTime,
            [FromQuery] int maxResultCount = 10,
            [FromQuery] int skipCount = 0)
        {
            // Get logs for the time range with proper pagination
            var logs = await _entityChangeLogAppService.GetTimeRangeChangeLogsAsync(
                startTime, endTime, maxResultCount, skipCount);

            // Return a custom response with the correct pagination
            return new
            {
                totalCount = logs.Count,
                items = logs
            };
        }

        [HttpGet("property-history")]
        public async Task<object> GetPropertyChangeHistoryAsync(
            [FromQuery] string entityId,
            [FromQuery] string entityTypeFullName,
            [FromQuery] string propertyName,
            [FromQuery] int maxResultCount = 10,
            [FromQuery] int skipCount = 0)
        {
            // Get property changes with proper pagination
            var changes = await _entityChangeLogAppService.GetPropertyChangeHistoryAsync(
                entityId, entityTypeFullName, propertyName, maxResultCount, skipCount);

            // Return a custom response with the correct pagination
            return new
            {
                totalCount = changes.Count,
                items = changes
            };
        }

        /// <summary>
        /// Gets entity changes with property changes by entity ID with pagination
        /// </summary>
        [HttpGet("entity-changes")]
        public async Task<object> GetEntityChangesByIdAsync(
            [FromQuery] string entityId,
            [FromQuery] string? entityTypeFullName = null,
            [FromQuery] DateTime? startTime = null,
            [FromQuery] DateTime? endTime = null,
            [FromQuery] int maxResultCount = 10,
            [FromQuery] int skipCount = 0,
            [FromQuery] string? sorting = null)
        {
            // Create input object
            var input = new GetEntityChangesByIdInput
            {
                EntityId = entityId,
                EntityTypeFullName = entityTypeFullName,
                StartTime = startTime,
                EndTime = endTime,
                MaxResultCount = maxResultCount,
                SkipCount = skipCount,
                Sorting = sorting
            };

            // Get entity changes with proper pagination
            var result = await _entityChangeLogAppService.GetEntityChangesByIdAsync(input);

            // Return a custom response with the correct pagination
            return new
            {
                totalCount = result.TotalCount,
                items = result.Items
            };
        }
    }
}
