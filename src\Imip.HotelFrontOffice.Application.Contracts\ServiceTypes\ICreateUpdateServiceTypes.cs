﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.RoomTypes;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.HotelFrontOffice.ServiceTypes
{
    public interface ICreateUpdateServiceTypes : ICrudAppService<
        ServiceTypesDto,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdateServiceTypesDto,
        CreateUpdateServiceTypesDto
    >
    {
    }
}