﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.HotelFrontOffice.Migrations
{
    /// <inheritdoc />
    public partial class updatePaymentsAndRoomStatusLogs : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AppPayments",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    TotalAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    PaidAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    PaymentMethod = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    PaymentStatus = table.Column<short>(type: "smallint", nullable: false),
                    PaymentCode = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    TransactionDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ReservationsId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ReservationDetailsId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    GuestId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    ExtraProperties = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ConcurrencyStamp = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AppPayments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AppPayments_AppGuests_GuestId",
                        column: x => x.GuestId,
                        principalTable: "AppGuests",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_AppPayments_AppReservationDetails_ReservationDetailsId",
                        column: x => x.ReservationDetailsId,
                        principalTable: "AppReservationDetails",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_AppPayments_AppReservations_ReservationsId",
                        column: x => x.ReservationsId,
                        principalTable: "AppReservations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "AppRoomStatusLogs",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    RoomId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    RoomStatusId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    StatusSource = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    RoomStatusesId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ExtraProperties = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ConcurrencyStamp = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AppRoomStatusLogs", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AppRoomStatusLogs_AppRoomStatuses_RoomStatusesId",
                        column: x => x.RoomStatusesId,
                        principalTable: "AppRoomStatuses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AppRoomStatusLogs_AppRoom_RoomId",
                        column: x => x.RoomId,
                        principalTable: "AppRoom",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "AppPaymentDetails",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    PaymentId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    SourceType = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    SourceId = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Amount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    ExtraProperties = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ConcurrencyStamp = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AppPaymentDetails", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AppPaymentDetails_AppPayments_PaymentId",
                        column: x => x.PaymentId,
                        principalTable: "AppPayments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "AppPaymentGuests",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    PaymentId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    GuestId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    AmountPaid = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    ExtraProperties = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ConcurrencyStamp = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AppPaymentGuests", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AppPaymentGuests_AppGuests_GuestId",
                        column: x => x.GuestId,
                        principalTable: "AppGuests",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AppPaymentGuests_AppPayments_PaymentId",
                        column: x => x.PaymentId,
                        principalTable: "AppPayments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AppPaymentDetails_PaymentId",
                table: "AppPaymentDetails",
                column: "PaymentId");

            migrationBuilder.CreateIndex(
                name: "IX_AppPaymentGuests_GuestId",
                table: "AppPaymentGuests",
                column: "GuestId");

            migrationBuilder.CreateIndex(
                name: "IX_AppPaymentGuests_PaymentId",
                table: "AppPaymentGuests",
                column: "PaymentId");

            migrationBuilder.CreateIndex(
                name: "IX_AppPayments_GuestId",
                table: "AppPayments",
                column: "GuestId");

            migrationBuilder.CreateIndex(
                name: "IX_AppPayments_ReservationDetailsId",
                table: "AppPayments",
                column: "ReservationDetailsId");

            migrationBuilder.CreateIndex(
                name: "IX_AppPayments_ReservationsId",
                table: "AppPayments",
                column: "ReservationsId");

            migrationBuilder.CreateIndex(
                name: "IX_AppRoomStatusLogs_RoomId",
                table: "AppRoomStatusLogs",
                column: "RoomId");

            migrationBuilder.CreateIndex(
                name: "IX_AppRoomStatusLogs_RoomStatusesId",
                table: "AppRoomStatusLogs",
                column: "RoomStatusesId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AppPaymentDetails");

            migrationBuilder.DropTable(
                name: "AppPaymentGuests");

            migrationBuilder.DropTable(
                name: "AppRoomStatusLogs");

            migrationBuilder.DropTable(
                name: "AppPayments");
        }
    }
}
