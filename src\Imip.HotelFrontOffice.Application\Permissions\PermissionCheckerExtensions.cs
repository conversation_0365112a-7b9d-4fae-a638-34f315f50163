using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Volo.Abp.Authorization;
using Volo.Abp.Authorization.Permissions;

namespace Imip.HotelFrontOffice.Permissions;

/// <summary>
/// Extension methods for checking permissions in app services
/// </summary>
public static class PermissionCheckerExtensions
{
    /// <summary>
    /// Checks if the GetList policy is granted
    /// </summary>
    public static async Task CheckGetListPolicyAsync<TAppService, TEntity, TEntityDto, TKey>(
        IPermissionChecker permissionChecker,
        ILogger<TAppService> logger,
        string getListPolicyName)
        where TAppService : class
        where TEntity : class
        where TEntityDto : class
    {
        if (string.IsNullOrEmpty(getListPolicyName))
        {
            return;
        }

        var isGranted = await permissionChecker.IsGrantedAsync(getListPolicyName);
        if (!isGranted)
        {
            throw new AbpAuthorizationException(
                $"Authorization failed! Given policy has not granted: {getListPolicyName}");
        }
    }

    /// <summary>
    /// Checks if the Get policy is granted
    /// </summary>
    public static async Task CheckGetPolicyAsync<TAppService, TEntity, TEntityDto, TKey>(
        IPermissionChecker permissionChecker,
        ILogger<TAppService> logger,
        string getPolicyName)
        where TAppService : class
        where TEntity : class
        where TEntityDto : class
    {
        if (string.IsNullOrEmpty(getPolicyName))
        {
            return;
        }

        var isGranted = await permissionChecker.IsGrantedAsync(getPolicyName);
        if (!isGranted)
        {
            throw new AbpAuthorizationException($"Authorization failed! Given policy has not granted: {getPolicyName}");
        }
    }

    /// <summary>
    /// Checks if the Create policy is granted
    /// </summary>
    public static async Task CheckCreatePolicyAsync<TAppService, TEntity, TEntityDto, TKey>(
        IPermissionChecker permissionChecker,
        ILogger<TAppService> logger,
        string createPolicyName)
        where TAppService : class
        where TEntity : class
        where TEntityDto : class
    {
        if (string.IsNullOrEmpty(createPolicyName))
        {
            return;
        }

        var isGranted = await permissionChecker.IsGrantedAsync(createPolicyName);
        if (!isGranted)
        {
            throw new AbpAuthorizationException(
                $"Authorization failed! Given policy has not granted: {createPolicyName}");
        }
    }

    /// <summary>
    /// Checks if the Update policy is granted
    /// </summary>
    public static async Task CheckUpdatePolicyAsync<TAppService, TEntity, TEntityDto, TKey>(
        IPermissionChecker permissionChecker,
        ILogger<TAppService> logger,
        string updatePolicyName)
        where TAppService : class
        where TEntity : class
        where TEntityDto : class
    {
        if (string.IsNullOrEmpty(updatePolicyName))
        {
            return;
        }

        var isGranted = await permissionChecker.IsGrantedAsync(updatePolicyName);
        if (!isGranted)
        {
            throw new AbpAuthorizationException(
                $"Authorization failed! Given policy has not granted: {updatePolicyName}");
        }
    }

    /// <summary>
    /// Checks if the Delete policy is granted
    /// </summary>
    public static async Task CheckDeletePolicyAsync<TAppService, TEntity, TEntityDto, TKey>(
        IPermissionChecker permissionChecker,
        ILogger<TAppService> logger,
        string deletePolicyName)
        where TAppService : class
        where TEntity : class
        where TEntityDto : class
    {
        if (string.IsNullOrEmpty(deletePolicyName))
        {
            return;
        }

        var isGranted = await permissionChecker.IsGrantedAsync(deletePolicyName);
        if (!isGranted)
        {
            throw new AbpAuthorizationException(
                $"Authorization failed! Given policy has not granted: {deletePolicyName}");
        }
    }
}