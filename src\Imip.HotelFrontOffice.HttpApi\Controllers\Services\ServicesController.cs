using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Models;
using Imip.HotelFrontOffice.Permissions;
using Imip.HotelFrontOffice.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.Controllers.Services;

[Route("api/app/services")]
[RemoteService]
public class ServicesController : HotelFrontOfficeController
{
    private readonly IServicesAppService _servicesAppService;
    private readonly IRepository<Service> _repository;
    private readonly ILogger<ServicesController> _logger;

    public ServicesController(
        IServicesAppService servicesAppService,
        IRepository<Service> repository,
        ILogger<ServicesController> logger)
    {
        _servicesAppService = servicesAppService;
        _repository = repository;
        _logger = logger;
    }

    /// <summary>
    /// Get a paged list of services with dynamic filtering and sorting
    /// </summary>
    /// <param name="input">Query parameters including filtering and sorting</param>
    /// <returns>Paged list of services in standard ABP format</returns>
    [HttpPost("list")]
    [Authorize(WismaAppPermissions.PolicyService.View)]
    [ProducesResponseType(typeof(PagedResultDto<ServicesDto>), StatusCodes.Status200OK)]
    public virtual async Task<PagedResultDto<ServicesDto>> GetPagedListAsync(QueryParameters input)
    {
        try
        {
            // Get the base query with includes
            var query = await ExecuteDynamicQueryAsync(input);

            // Get total count before paging
            var totalCount = await query.CountAsync();

            // Apply paging
            var items = await query
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount)
                .ToListAsync();

            // Map to DTOs
            var dtos = ObjectMapper.Map<List<Service>, List<ServicesDto>>(items);

            // Return standard ABP paged result
            return new PagedResultDto<ServicesDto>
            {
                TotalCount = totalCount,
                Items = dtos
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting paged list of services: {Message}", ex.Message);
            throw new UserFriendlyException(
                "Could not retrieve services list",
                "Error.ServicesList.Failed",
                ex.Message);
        }
    }

    /// <summary>
    /// Execute dynamic query with filtering and sorting
    /// </summary>
    protected virtual async Task<IQueryable<Service>> ExecuteDynamicQueryAsync(QueryParameters parameters)
    {
        // Get base query with includes
        var query = await _repository.GetQueryableAsync();

        // Add includes for related entities
        query = query
            .AsNoTracking()
            .Include(x => x.ServiceType);

        // Check if we need to include deeper relationships based on filter or sort fields
        var fieldsToCheck = new List<string>();

        // Add filter fields
        if (parameters.FilterGroup?.Conditions != null)
        {
            fieldsToCheck.AddRange(parameters.FilterGroup.Conditions
                .Where(c => c.FieldName.Contains('.'))
                .Select(c => c.FieldName));
        }

        // Add sort fields
        if (parameters.Sort?.Count > 0)
        {
            fieldsToCheck.AddRange(parameters.Sort
                .Where(s => s.Field.Contains('.'))
                .Select(s => s.Field));
        }

        // Include service type status if needed
        if (fieldsToCheck.Any(f => f.StartsWith("serviceType.status")))
        {
            query = query.Include("ServiceType.Status");
        }

        // Apply filters
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<Service>.ApplyFilters(query, parameters.FilterGroup);
        }

        // Apply sorting
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<Service>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            // Use the default sorting if provided using Dynamic LINQ
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            // Default sorting by name
            query = query.OrderBy(x => x.Name);
        }

        return query;
    }
}
