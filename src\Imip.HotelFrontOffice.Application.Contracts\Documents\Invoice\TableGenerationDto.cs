using System.Collections.Generic;

namespace Imip.HotelFrontOffice.Documents.Invoice;

/// <summary>
/// DTO for table properties
/// </summary>
public class TablePropertiesDto
{
    /// <summary>
    /// The table width
    /// </summary>
    public int Width { get; set; } = 5000;

    /// <summary>
    /// The table width type (Pct, Dxa, Auto)
    /// </summary>
    public string WidthType { get; set; } = "Pct";

    /// <summary>
    /// The table layout (Fixed, Auto)
    /// </summary>
    public string Layout { get; set; } = "Fixed";

    /// <summary>
    /// The cell spacing
    /// </summary>
    public int CellSpacing { get; set; } = 0;

    /// <summary>
    /// The border size
    /// </summary>
    public int BorderSize { get; set; } = 1;
}

/// <summary>
/// DTO for table generation with merged cells
/// </summary>
public class TableGenerationDto
{
    /// <summary>
    /// The table headers
    /// </summary>
    public List<TableHeaderDto> Headers { get; set; } = new();

    /// <summary>
    /// The table rows
    /// </summary>
    public List<TableRowDto> Rows { get; set; } = new();

    /// <summary>
    /// The table properties
    /// </summary>
    public TablePropertiesDto? TableProperties { get; set; }
}

/// <summary>
/// DTO for table header
/// </summary>
public class TableHeaderDto
{
    /// <summary>
    /// The header text
    /// </summary>
    public string Text { get; set; } = string.Empty;

    /// <summary>
    /// The column span
    /// </summary>
    public int ColSpan { get; set; } = 1;

    /// <summary>
    /// The width in percentage
    /// </summary>
    public double WidthPercentage { get; set; } = 0;
}

/// <summary>
/// DTO for table row
/// </summary>
public class TableRowDto
{
    /// <summary>
    /// The cells in the row
    /// </summary>
    public List<TableCellDto> Cells { get; set; } = new();
}

/// <summary>
/// DTO for table cell
/// </summary>
public class TableCellDto
{
    /// <summary>
    /// The cell text
    /// </summary>
    public string Text { get; set; } = string.Empty;

    /// <summary>
    /// The column span
    /// </summary>
    public int ColSpan { get; set; } = 1;

    /// <summary>
    /// The row span
    /// </summary>
    public int RowSpan { get; set; } = 1;

    /// <summary>
    /// Whether the cell is a header
    /// </summary>
    public bool IsHeader { get; set; } = false;

    /// <summary>
    /// The horizontal alignment
    /// </summary>
    public TableCellAlignment HorizontalAlignment { get; set; } = TableCellAlignment.Left;

    /// <summary>
    /// The vertical alignment
    /// </summary>
    public TableCellAlignment VerticalAlignment { get; set; } = TableCellAlignment.Center;

    /// <summary>
    /// Whether to apply bold formatting
    /// </summary>
    public bool IsBold { get; set; } = false;

    /// <summary>
    /// The width in percentage
    /// </summary>
    public double WidthPercentage { get; set; } = 0;

    /// <summary>
    /// The font size in points (optional)
    /// </summary>
    public int? FontSize { get; set; } = null;
}

/// <summary>
/// Enum for table cell alignment
/// </summary>
public enum TableCellAlignment
{
    /// <summary>
    /// Left alignment
    /// </summary>
    Left,

    /// <summary>
    /// Center alignment
    /// </summary>
    Center,

    /// <summary>
    /// Right alignment
    /// </summary>
    Right,

    /// <summary>
    /// Top alignment
    /// </summary>
    Top,

    /// <summary>
    /// Bottom alignment
    /// </summary>
    Bottom
}
