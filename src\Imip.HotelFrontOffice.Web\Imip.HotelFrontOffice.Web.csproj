<Project Sdk="Microsoft.NET.Sdk.Web">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <AspNetCoreHostingModel>InProcess</AspNetCoreHostingModel>
    <RootNamespace>Imip.HotelFrontOffice.Web</RootNamespace>
    <AssetTargetFallback>$(AssetTargetFallback);portable-net45+win8+wp8+wpa81;</AssetTargetFallback>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <GenerateBindingRedirectsOutputType>true</GenerateBindingRedirectsOutputType>
    <GenerateRuntimeConfigurationFiles>true</GenerateRuntimeConfigurationFiles>
    <MvcRazorExcludeRefAssembliesFromPublish>false</MvcRazorExcludeRefAssembliesFromPublish>
    <PreserveCompilationReferences>true</PreserveCompilationReferences>
  </PropertyGroup>

  <ItemGroup Condition="Exists('./openiddict.pfx')">
    <None Remove="openiddict.pfx" />
    <EmbeddedResource Include="openiddict.pfx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Logs\**" />
    <Content Remove="Logs\**" />
    <EmbeddedResource Remove="Logs\**" />
    <None Remove="Logs\**" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="Pages\**\*.js">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Pages\**\*.css">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AspNetCore.HealthChecks.UI" Version="9.0.0" />
    <PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="9.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.0" />
    <PackageReference Include="AspNetCore.HealthChecks.UI.InMemory.Storage" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore" Version="9.0.4" />
    <PackageReference Include="OpenIddict.Validation.SystemNetHttp" Version="4.9.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.Async" Version="2.1.0" />
    <PackageReference Include="HarfBuzzSharp.NativeAssets.Linux" Version="*******" />
    <PackageReference Include="SkiaSharp.NativeAssets.Linux" Version="3.116.1" />
    <PackageReference Include="System.IO.Compression.ZipFile" Version="4.3.0" />
    <PackageReference Include="TickerQ" Version="2.0.0" />
    <PackageReference Include="TickerQ.Dashboard" Version="2.0.0" />
    <PackageReference Include="Volo.Abp.AspNetCore.SignalR" Version="9.0.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Imip.HotelFrontOffice.Application\Imip.HotelFrontOffice.Application.csproj" />
    <ProjectReference Include="..\Imip.HotelFrontOffice.HttpApi\Imip.HotelFrontOffice.HttpApi.csproj" />
    <ProjectReference Include="..\Imip.HotelFrontOffice.EntityFrameworkCore\Imip.HotelFrontOffice.EntityFrameworkCore.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Serilog.Sinks.Seq" Version="9.0.0" />
    <PackageReference Include="Volo.Abp.Autofac" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.AspNetCore.Serilog" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.AspNetCore.Authentication.JwtBearer" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.Swashbuckle" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.FeatureManagement.Web" Version="9.0.4" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite" Version="4.0.5" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Account.Web.OpenIddict" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.Identity.Web" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.TenantManagement.Web" Version="9.0.4" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Studio.Client.AspNetCore" Version="0.9.23" />
  </ItemGroup>

</Project>
