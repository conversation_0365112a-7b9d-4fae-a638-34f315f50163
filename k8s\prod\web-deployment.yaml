﻿apiVersion: apps/v1
kind: Deployment
metadata:
  name: imip-wisma-web
  namespace: imip-wisma-prod
  labels:
    app: imip-wisma-web
    environment: production
spec:
  replicas: 3 # Increased for better high availability
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0 # Ensures no pods are unavailable during the update
      maxSurge: 1 # Allows creating at most 1 extra pod during the update
  selector:
    matchLabels:
      app: imip-wisma-web
  template:
    metadata:
      labels:
        app: imip-wisma-web
        environment: production
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "80"
        prometheus.io/path: "/metrics"
    spec:
      # Use nodeSelector to target the new production nodes
      nodeSelector:
        node-role.kubernetes.io/worker: "worker"
      # Add tolerations for the production nodes
      tolerations:
        - key: "node-role.kubernetes.io/worker"
          operator: "Exists"
          effect: "NoSchedule"
      imagePullSecrets:
        - name: gitlab-registry-credentials
      # Add host aliases to resolve DNS names to specific IP addresses
      hostAliases:
        - ip: "**********"
          hostnames:
            - "api-identity.imip.co.id"
      # Improved pod anti-affinity for better distribution across nodes
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - imip-wisma-web
                topologyKey: kubernetes.io/hostname
      volumes:
        - name: certificate-volume
          secret:
            secretName: imip-wisma-certificate
            items:
              - key: identity-server.pfx
                path: identity-server.pfx
        - name: data-protection-keys
          hostPath:
            path: /mnt/data/imip-wisma-data-protection
            type: DirectoryOrCreate
      # Add terminationGracePeriodSeconds for graceful shutdown
      terminationGracePeriodSeconds: 90
      # Add securityContext at pod level
      securityContext:
        fsGroup: 1000
        runAsNonRoot: true
        seccompProfile:
          type: RuntimeDefault
      initContainers:
        - name: init-data-protection
          image: busybox:1.36.1
          command:
            [
              "sh",
              "-c",
              "mkdir -p /app/data-protection-keys && chmod -R 770 /app/data-protection-keys && chown -R 1000:1000 /app/data-protection-keys",
            ]
          volumeMounts:
            - name: data-protection-keys
              mountPath: /app/data-protection-keys

      containers:
        - name: imip-wisma-web
          image: ${CI_REGISTRY_IMAGE}/web:${CI_COMMIT_SHA}
          ports:
            - containerPort: 80
              name: http
            - containerPort: 443
              name: https
          volumeMounts:
            - name: certificate-volume
              mountPath: /app/certs
              readOnly: true
            - name: data-protection-keys
              mountPath: /app/data-protection-keys
          envFrom:
            - configMapRef:
                name: imip-wisma-config
            - secretRef:
                name: imip-wisma-secrets
          env:
            - name: ASPNETCORE_ENVIRONMENT
              value: "Production"
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: SYNCFUSION_DEBUG
              value: "true"
            - name: SKIASHARP_DEBUG
              value: "true"
            - name: DOTNET_gcServer
              value: "1"
            - name: DOTNET_gcConcurrent
              value: "1"
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          resources:
            requests:
              memory: "1Gi"
              cpu: "500m"
            limits:
              memory: "2.5Gi"
              cpu: "1500m"
          securityContext:
            runAsUser: 0
            allowPrivilegeEscalation: true
            capabilities:
              drop:
                - ALL
          startupProbe:
            httpGet:
              path: /api/health/kubernetes
              port: 80
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 15
            failureThreshold: 15
          readinessProbe:
            httpGet:
              path: /api/health/kubernetes
              port: 80
            initialDelaySeconds: 60
            periodSeconds: 15
            timeoutSeconds: 15
            successThreshold: 1
            failureThreshold: 3
          livenessProbe:
            httpGet:
              path: /api/health/kubernetes
              port: 80
            initialDelaySeconds: 90
            periodSeconds: 30
            timeoutSeconds: 15
            successThreshold: 1
            failureThreshold: 3
