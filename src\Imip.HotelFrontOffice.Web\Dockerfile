FROM mcr.microsoft.com/dotnet/aspnet:9.0

# Install SkiaSharp dependencies for Syncfusion document processing
RUN apt-get update && apt-get install -y --no-install-recommends \
    libfontconfig1 \
    libfreetype6 \
    libicu-dev \
    libharfbuzz0b \
    libgdiplus \
    libc6-dev \
    libssl3 \
    fontconfig \
    fonts-dejavu \
    fonts-liberation \
    fonts-noto \
    fonts-noto-cjk \
    fonts-freefont-ttf \
    libpango1.0-dev \
    libcairo2-dev \
    libpng-dev \
    libjpeg-dev \
    libgif-dev \
    libtiff-dev \
    libwebp-dev \
    locales \
    # Additional dependencies for SkiaSharp
    libfontconfig1 \
    libfontconfig-dev \
    libx11-dev \
    libxft-dev \
    libgl1-mesa-dev \
    libgl1 \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

# Set up locales
RUN sed -i -e 's/# en_US.UTF-8 UTF-8/en_US.UTF-8 UTF-8/' /etc/locale.gen && \
    locale-gen

# Create font cache
RUN fc-cache -f -v

COPY src/Imip.HotelFrontOffice.Web/bin/Release/net9.0/publish/ app/
WORKDIR /app
ENV ASPNETCORE_URLS=http://+:80
ENV ASPNETCORE_ENVIRONMENT=Production
ENV LD_LIBRARY_PATH="/app/runtimes/linux-x64/native:/usr/lib:/usr/lib/x86_64-linux-gnu:/lib:/lib64:/usr/lib64"
ENV SYNCFUSION_DEBUG="true"
ENV SKIASHARP_DEBUG="true"
ENV SYNCFUSION_LICENSING_PROVIDER_TRACE="true"
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT="false"
ENV LC_ALL="en_US.UTF-8"
ENV LANG="en_US.UTF-8"
# Additional environment variables for SkiaSharp
ENV FONTCONFIG_PATH="/etc/fonts"
ENV SKIASHARP_PRELOAD_NATIVE="true"
ENV DOTNET_EnableDiagnostics="1"

# Create directory for certificates and data protection keys
RUN mkdir -p /app/certs /app/data-protection-keys && \
    chmod -R 777 /app/data-protection-keys

# Create an entrypoint script to handle certificate setup
COPY src/Imip.HotelFrontOffice.Web/entrypoint.sh /app/entrypoint.sh
COPY src/Imip.HotelFrontOffice.Web/verify-skiasharp.sh /app/verify-skiasharp.sh
# Fix line endings and make the scripts executable
RUN sed -i 's/\r$//' /app/entrypoint.sh && \
    chmod +x /app/entrypoint.sh && \
    sed -i 's/\r$//' /app/verify-skiasharp.sh && \
    chmod +x /app/verify-skiasharp.sh && \
    cat /app/entrypoint.sh | head -1

# Create symbolic links to ensure SkiaSharp can find its dependencies
RUN mkdir -p /usr/lib/x86_64-linux-gnu/ && \
    mkdir -p /lib64 && \
    mkdir -p /usr/lib64 && \
    echo "Preparing for SkiaSharp library setup..." && \
    # Create a script to run after the application files are copied
    echo '#!/bin/bash\n\
    echo "Setting up SkiaSharp libraries..."\n\
    # Find all libSkiaSharp.so files and create symbolic links\n\
    find /app -name "libSkiaSharp.so" -type f | while read -r file; do\n\
        echo "Found libSkiaSharp.so at $file"\n\
        cp -v "$file" /usr/lib/ || echo "Failed to copy to /usr/lib/"\n\
        cp -v "$file" /usr/lib/x86_64-linux-gnu/ || echo "Failed to copy to /usr/lib/x86_64-linux-gnu/"\n\
        cp -v "$file" /lib/ || echo "Failed to copy to /lib/"\n\
        cp -v "$file" /lib64/ || echo "Failed to copy to /lib64/"\n\
        cp -v "$file" /usr/lib64/ || echo "Failed to copy to /usr/lib64/"\n\
        ln -sf "$file" /usr/lib/libSkiaSharp.so || echo "Failed to create symlink in /usr/lib/"\n\
        ln -sf "$file" /usr/lib/x86_64-linux-gnu/libSkiaSharp.so || echo "Failed to create symlink in /usr/lib/x86_64-linux-gnu/"\n\
        ln -sf "$file" /lib/libSkiaSharp.so || echo "Failed to create symlink in /lib/"\n\
        ln -sf "$file" /lib64/libSkiaSharp.so || echo "Failed to create symlink in /lib64/"\n\
        ln -sf "$file" /usr/lib64/libSkiaSharp.so || echo "Failed to create symlink in /usr/lib64/"\n\
    done\n\
    \n\
    # Copy all native libraries to standard locations\n\
    if [ -d "/app/runtimes/linux-x64/native" ]; then\n\
        echo "Copying all native libraries from /app/runtimes/linux-x64/native/"\n\
        cp -vf /app/runtimes/linux-x64/native/* /usr/lib/ || echo "Failed to copy to /usr/lib/"\n\
        cp -vf /app/runtimes/linux-x64/native/* /usr/lib/x86_64-linux-gnu/ || echo "Failed to copy to /usr/lib/x86_64-linux-gnu/"\n\
        cp -vf /app/runtimes/linux-x64/native/* /lib/ || echo "Failed to copy to /lib/"\n\
        cp -vf /app/runtimes/linux-x64/native/* /lib64/ || echo "Failed to copy to /lib64/"\n\
        cp -vf /app/runtimes/linux-x64/native/* /usr/lib64/ || echo "Failed to copy to /usr/lib64/"\n\
    else\n\
        echo "WARNING: /app/runtimes/linux-x64/native/ directory not found"\n\
    fi\n\
    \n\
    # List all library paths to verify\n\
    echo "Library paths:"\n\
    ldconfig -p | grep SkiaSharp || echo "SkiaSharp not found in ldconfig"\n\
    echo "LD_LIBRARY_PATH: $LD_LIBRARY_PATH"\n\
    ' > /setup-skiasharp.sh && \
    chmod +x /setup-skiasharp.sh

ENTRYPOINT ["/app/entrypoint.sh"]