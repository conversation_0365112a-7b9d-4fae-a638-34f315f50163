using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using IdentityModel.Client;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace Imip.HotelFrontOffice;

public class BearerTokenHandler : DelegatingHandler
{
    private readonly IConfiguration _configuration;
    private readonly ITokenCache _tokenCache;  // You'll need to implement this
    private readonly ILogger<BearerTokenHandler> _logger;

    public BearerTokenHandler(
        IConfiguration configuration,
        ITokenCache tokenCache,
        ILogger<BearerTokenHandler> logger)
    {
        _configuration = configuration;
        _tokenCache = tokenCache;
        _logger = logger;
    }

    protected override async Task<HttpResponseMessage> SendAsync(
        HttpRequestMessage request,
        CancellationToken cancellationToken)
    {
        // Try to get cached token
        var token = await _tokenCache.GetAsync();

        if (token == null)
        {
            // Get new token
            var client = new HttpClient();
            var disco = await client.GetDiscoveryDocumentAsync(
                _configuration["AuthServer:Authority"]);

            var tokenResponse = await client.RequestClientCredentialsTokenAsync(
                new ClientCredentialsTokenRequest
                {
                    Address = disco.TokenEndpoint,
                    ClientId = _configuration["AuthServer:ClientId"],
                    ClientSecret = _configuration["AuthServer:ClientSecret"],
                    Scope = "HotelFrontOffice"
                });

            if (tokenResponse.IsError)
            {
                throw new HttpRequestException($"Token acquisition failed: {tokenResponse.Error}");
            }

            token = tokenResponse.AccessToken;
            await _tokenCache.SetAsync(token, tokenResponse.ExpiresIn);
        }

        request.Headers.Authorization =
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);

        return await base.SendAsync(request, cancellationToken);
    }
}