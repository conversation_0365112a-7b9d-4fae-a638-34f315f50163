﻿using Volo.Abp.Ui.Branding;
using Volo.Abp.DependencyInjection;
using Microsoft.Extensions.Localization;
using Imip.HotelFrontOffice.Localization;

namespace Imip.HotelFrontOffice.Web;

[Dependency(ReplaceServices = true)]
public class HotelFrontOfficeBrandingProvider : DefaultBrandingProvider
{
    private IStringLocalizer<HotelFrontOfficeResource> _localizer;

    public HotelFrontOfficeBrandingProvider(IStringLocalizer<HotelFrontOfficeResource> localizer)
    {
        _localizer = localizer;
    }

    public override string AppName => _localizer["AppName"];
}
