﻿using System;
using System.Collections.Generic;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.HotelFrontOffice.PaymentMethods;

public class PaymentMethod : FullAuditedAggregateRoot<Guid>
{
    public string Name { get; set; } = default!;
    public string Information { get; set; } = default!;

    public virtual ICollection<Payments.Payment>? Payments { get; set; }
    public virtual ICollection<Reservations.Reservation>? Reservations { get; set; }

    protected PaymentMethod()
    {
        Payments = new HashSet<Payments.Payment>();
        Reservations = new HashSet<Reservations.Reservation>();
    }

    public PaymentMethod(Guid id, string name, string information)
    {
        Id = id;
        Name = name;
        Information = information;
    }
}