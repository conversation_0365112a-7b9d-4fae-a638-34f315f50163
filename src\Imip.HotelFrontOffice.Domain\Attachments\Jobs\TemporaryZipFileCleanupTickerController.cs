using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using TickerQ;
using Volo.Abp.BlobStoring;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Uow;

namespace Imip.HotelFrontOffice.Attachments.Jobs
{
    /// <summary>
    /// Controller for cleaning up temporary ZIP files
    /// </summary>
    public class TemporaryZipFileCleanupTickerController
    {
        private readonly ILogger<TemporaryZipFileCleanupTickerController> _logger;
        private readonly IServiceProvider _serviceProvider;

        /// <summary>
        /// Creates a new instance of TemporaryZipFileCleanupTickerController
        /// </summary>
        public TemporaryZipFileCleanupTickerController(
            ILogger<TemporaryZipFileCleanupTickerController> logger,
            IServiceProvider serviceProvider)
        {
            _logger = logger;
            _serviceProvider = serviceProvider;
        }

        /// <summary>
        /// Cleans up temporary ZIP files that are due for deletion
        /// </summary>
        [UnitOfWork]
        public async Task CleanupTemporaryZipFiles(CancellationToken cancellationToken)
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var temporaryZipFileRepository = scope.ServiceProvider.GetRequiredService<ITemporaryZipFileRepository>();
                var blobContainer = scope.ServiceProvider.GetRequiredService<IBlobContainerFactory>().Create("default");

                // Get files that are due for deletion
                var now = DateTime.UtcNow;
                var filesToDelete = await temporaryZipFileRepository.GetDueForDeletionAsync(now);

                // Process each file
                foreach (var file in filesToDelete)
                {
                    try
                    {
                        if (!string.IsNullOrEmpty(file.BlobName))
                        {
                            // Delete the file from blob storage
                            await blobContainer.DeleteAsync(file.BlobName, cancellationToken);
                        }
                        else if (!string.IsNullOrEmpty(file.Description))
                        {
                            // This is a local file, the Description field contains the file paths
                            try
                            {
                                // Parse the file paths from the Description field
                                var paths = file.Description.Split('|');
                                if (paths.Length >= 1 && !string.IsNullOrEmpty(paths[0]))
                                {
                                    // Delete the DOCX file if it exists
                                    if (System.IO.File.Exists(paths[0]))
                                    {
                                        System.IO.File.Delete(paths[0]);
                                    }
                                }

                                if (paths.Length >= 2 && !string.IsNullOrEmpty(paths[1]))
                                {
                                    // Delete the PDF file if it exists
                                    if (System.IO.File.Exists(paths[1]))
                                    {
                                        System.IO.File.Delete(paths[1]);
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "Error deleting local files for temporary ZIP file with ID {Id}", file.Id);
                            }
                        }
                        else
                        {
                            _logger.LogWarning("Skipped deletion of temporary ZIP file with ID {Id} because both BlobName and Description are null", file.Id);
                        }

                        // Mark the file as processed
                        await temporaryZipFileRepository.MarkAsProcessedAsync(file.Id);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error deleting temporary ZIP file {Id} with blob name {BlobName}",
                            file.Id, file.BlobName);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in scheduled temporary ZIP file cleanup");
                throw; // Rethrow to allow TickerQ to handle retries
            }
        }
    }
}
