﻿using System;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Imip.HotelFrontOffice.Data;
using Volo.Abp.DependencyInjection;

namespace Imip.HotelFrontOffice.EntityFrameworkCore;

public class EntityFrameworkCoreHotelFrontOfficeDbSchemaMigrator
    : IHotelFrontOfficeDbSchemaMigrator, ITransientDependency
{
    private readonly IServiceProvider _serviceProvider;

    public EntityFrameworkCoreHotelFrontOfficeDbSchemaMigrator(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public async Task MigrateAsync()
    {
        /* We intentionally resolving the HotelFrontOfficeDbContext
         * from IServiceProvider (instead of directly injecting it)
         * to properly get the connection string of the current tenant in the
         * current scope.
         */

        await _serviceProvider
            .GetRequiredService<HotelFrontOfficeDbContext>()
            .Database
            .MigrateAsync();
    }
}
