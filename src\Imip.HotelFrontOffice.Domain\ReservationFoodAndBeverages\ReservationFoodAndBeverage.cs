﻿using System;
using System.Collections.Generic;
using Imip.HotelFrontOffice.FoodAndBeverages;
using Imip.HotelFrontOffice.MasterStatuses;
using Imip.HotelFrontOffice.PaymentDetails;
using Imip.HotelFrontOffice.ReservationDetails;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.HotelFrontOffice.ReservationFoodAndBeverages;

public class ReservationFoodAndBeverage : FullAuditedAggregateRoot<Guid>
{
    public Guid ReservationDetailsId { get; set; }
    public Guid FoodAndBeverageId { get; set; }
    public Guid? PaymentStatusId { get; set; }
    public decimal TotalPrice { get; set; }
    public int Quantity { get; set; }
    public DateTime? TransactionDate { get; set; }

    public virtual ReservationDetail? ReservationDetails { get; set; }
    public virtual FoodAndBeverage? FoodAndBeverage { get; set; }
    public virtual MasterStatus? PaymentStatus { get; set; }
    // public virtual PaymentDetail? PaymentDetails { get; set; }

    protected ReservationFoodAndBeverage()
    {
    }

    public ReservationFoodAndBeverage(
        Guid id,
        Guid reservationDetailsId,
        Guid foodAndBeverageId,
        Guid? paymentStatusId,
        decimal totalPrice,
        int quantity,
        DateTime? transactionDate = null)
    {
        Id = id;
        ReservationDetailsId = reservationDetailsId;
        FoodAndBeverageId = foodAndBeverageId;
        PaymentStatusId = paymentStatusId;
        TotalPrice = totalPrice;
        Quantity = quantity;
        TransactionDate = transactionDate;
    }
}