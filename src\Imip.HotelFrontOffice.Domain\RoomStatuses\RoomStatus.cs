﻿using System;
using System.Collections.Generic;
using Imip.HotelFrontOffice.Rooms;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.HotelFrontOffice.RoomStatuses;

public class RoomStatus : FullAuditedAggregateRoot<Guid>
{
    public string Name { get; set; } = default!;
    public string Color { get; set; } = default!;
    public string Code { get; set; } = default!;


    public virtual ICollection<Room>? Rooms { get; set; }
    public virtual ICollection<RoomStatusLogs.RoomStatusLog>? RoomStatusLogs { get; set; }

    protected RoomStatus()
    {
        Rooms = new HashSet<Room>();
    }

    public RoomStatus(Guid id, string name, string color, string code) : this()
    {
        Id = id;
        Name = name;
        Code = code;
        Color = color;
    }
}