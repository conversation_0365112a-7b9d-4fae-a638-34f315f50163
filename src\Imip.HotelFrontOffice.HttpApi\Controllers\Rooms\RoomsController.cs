using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Models;
using Imip.HotelFrontOffice.Permissions;
using Imip.HotelFrontOffice.Rooms;
using Imip.HotelFrontOffice.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.Controllers.Rooms;

[Route("api/app/rooms")]
[RemoteService]

public class RoomsController : HotelFrontOfficeController
{
    private readonly IRoomAppService _roomAppService;
    private readonly IRepository<Room> _repository;
    private readonly ILogger<RoomsController> _logger;

    public RoomsController(
        IRoomAppService roomAppService,
        IRepository<Room> repository,
        ILogger<RoomsController> logger)
    {
        _roomAppService = roomAppService;
        _repository = repository;
        _logger = logger;
    }

    /// <summary>
    /// Get a paged list of rooms with dynamic filtering and sorting
    /// </summary>
    /// <param name="input">Query parameters including filtering and sorting</param>
    /// <returns>Paged list of rooms in standard ABP format</returns>
    [HttpPost("list")]
    [Authorize(WismaAppPermissions.PolicyRoom.View)]
    [ProducesResponseType(typeof(PagedResultDto<RoomDto>), StatusCodes.Status200OK)]
    public virtual async Task<PagedResultDto<RoomDto>> GetPagedListAsync(QueryParameters input)
    {
        try
        {
            // Get the base query with includes
            var query = await ExecuteDynamicQueryAsync(input);

            // Get total count before paging
            var totalCount = await query.CountAsync();

            // Apply paging
            var items = await query
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount)
                .ToListAsync();

            // Map to DTOs
            var dtos = ObjectMapper.Map<List<Room>, List<RoomDto>>(items);

            // Return standard ABP paged result
            return new PagedResultDto<RoomDto>
            {
                TotalCount = totalCount,
                Items = dtos
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting paged list of rooms: {Message}", ex.Message);
            throw new UserFriendlyException(
                "Could not retrieve rooms list",
                "Error.RoomsList.Failed",
                ex.Message);
        }
    }

    /// <summary>
    /// Execute dynamic query with filtering and sorting
    /// </summary>
    protected virtual async Task<IQueryable<Room>> ExecuteDynamicQueryAsync(QueryParameters parameters)
    {
        // Get base query with includes
        var query = await _repository.GetQueryableAsync();

        // Add includes for related entities
        query = query
            .AsNoTracking() // Disable change tracking for better performance
            .Include(x => x.RoomType)
                .ThenInclude(rt => rt.Status) // Include nested related entities
            .Include(x => x.RoomStatus);

        // Check if we need to include deeper relationships based on filter or sort fields
        var fieldsToCheck = new List<string>();

        // Add filter fields
        if (parameters.FilterGroup?.Conditions != null)
        {
            fieldsToCheck.AddRange(parameters.FilterGroup.Conditions
                .Where(c => c.FieldName.Contains('.'))
                .Select(c => c.FieldName));
        }

        // Add sort fields
        if (parameters.Sort?.Count > 0)
        {
            fieldsToCheck.AddRange(parameters.Sort
                .Where(s => s.Field.Contains('.'))
                .Select(s => s.Field));
        }

        // Apply filters
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<Room>.ApplyFilters(query, parameters.FilterGroup);
        }

        // Apply sorting
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<Room>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            // Use the default sorting if provided using Dynamic LINQ
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            // Default sorting by room number
            query = query.OrderBy(x => x.RoomNumber);
        }

        return query;
    }
}
