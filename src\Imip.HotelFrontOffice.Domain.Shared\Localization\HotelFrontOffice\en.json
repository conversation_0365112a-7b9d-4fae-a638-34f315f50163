{"Culture": "en", "Texts": {"AppName": "HotelFrontOffice", "Menu:Home": "Home", "LongWelcomeMessage": "Welcome to the application. This is a startup project based on the ABP framework. For more information visit", "Welcome": "Welcome", "FileNameCannotBeEmpty": "File name cannot be empty", "FileContentCannotBeEmpty": "File content cannot be empty", "FileIsRequired": "File is required", "FileMustBeDocx": "File must be a DOCX file", "FailedToUploadFile": "Failed to upload file", "FileNotFound": "File not found", "FileContentNotFound": "File content not found", "FailedToDownloadFile": "Failed to download file", "FailedToDeleteFile": "Failed to delete file", "NoFilesSelectedForDownload": "No files selected for download", "AttachmentNotFound": "Attachment not found", "AttachmentMustBeDocx": "Attachment must be a DOCX file", "NoDefaultTemplateFound": "No default template found for document type: {0}", "TemplateAttachmentNotFound": "Template attachment not found", "TemplateFileNotFound": "Template file not found", "ErrorConvertingDocxToPdf": "Error converting DOCX to PDF", "FailedToUploadTemplate": "Failed to upload document template", "DocumentType:Invoice": "Invoice", "DocumentType:Report": "Report", "DocumentType:Contract": "Contract", "DocumentType:Letter": "Letter", "DocumentType:Certificate": "Certificate", "DocumentType:Other": "Other", "HotelFrontOffice:TaxCodeAlreadyExists": "A tax with code '{Code}' already exists"}}