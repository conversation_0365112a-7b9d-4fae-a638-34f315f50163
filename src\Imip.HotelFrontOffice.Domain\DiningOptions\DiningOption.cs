﻿using System;
using System.Collections.Generic;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.HotelFrontOffice.DiningOptions;

public class DiningOption: FullAuditedAggregateRoot<Guid>
{
    public string Name { get; set; } = default!;
    
    public virtual ICollection<Reservations.Reservation>? Reservations { get; set; }

    protected DiningOption()
    {
        Reservations = new HashSet<Reservations.Reservation>();
    }
    
    public DiningOption(Guid id, string name)
    {
        Id = id;
        Name = name;
    }
}