﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.HotelFrontOffice.Migrations
{
    /// <inheritdoc />
    public partial class Correcting_Transaction_Rooms_And_FoodAndBeverages : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AppReservationFoodAndBeverages",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ReservationDetailsId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    FoodAndBeverageId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    TotalPrice = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Quantity = table.Column<int>(type: "int", nullable: false),
                    ExtraProperties = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ConcurrencyStamp = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AppReservationFoodAndBeverages", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AppReservationFoodAndBeverages_AppFoodAndBeverage_FoodAndBeverageId",
                        column: x => x.FoodAndBeverageId,
                        principalTable: "AppFoodAndBeverage",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_AppReservationFoodAndBeverages_AppReservationDetails_ReservationDetailsId",
                        column: x => x.ReservationDetailsId,
                        principalTable: "AppReservationDetails",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "AppReservationRooms",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ReservationDetailsId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ServiceId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    TotalPrice = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Quantity = table.Column<int>(type: "int", nullable: false),
                    ExtraProperties = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ConcurrencyStamp = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AppReservationRooms", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AppReservationRooms_AppReservationDetails_ReservationDetailsId",
                        column: x => x.ReservationDetailsId,
                        principalTable: "AppReservationDetails",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_AppReservationRooms_AppServices_ServiceId",
                        column: x => x.ServiceId,
                        principalTable: "AppServices",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationFoodAndBeverages_FoodAndBeverageId",
                table: "AppReservationFoodAndBeverages",
                column: "FoodAndBeverageId");

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationFoodAndBeverages_Id",
                table: "AppReservationFoodAndBeverages",
                column: "Id");

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationFoodAndBeverages_ReservationDetailsId",
                table: "AppReservationFoodAndBeverages",
                column: "ReservationDetailsId");

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationRooms_ReservationDetailsId",
                table: "AppReservationRooms",
                column: "ReservationDetailsId");

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationRooms_ServiceId",
                table: "AppReservationRooms",
                column: "ServiceId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AppReservationFoodAndBeverages");

            migrationBuilder.DropTable(
                name: "AppReservationRooms");
        }
    }
}
