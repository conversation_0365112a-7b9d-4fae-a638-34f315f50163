using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.MasterStatuses;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.Repositories
{
    public interface IMasterStatusRepository : IRepository<MasterStatus, Guid>
    {
        /// <summary>
        /// Gets MasterStatus entities filtered by DocType
        /// </summary>
        /// <param name="docType">The document type to filter by</param>
        /// <returns>A list of MasterStatus entities filtered by DocType</returns>
        Task<List<MasterStatus>> GetByDocTypeAsync(string docType);

        /// <summary>
        /// Gets a single MasterStatus entity by ID and DocType
        /// </summary>
        /// <param name="id">The ID of the MasterStatus entity</param>
        /// <param name="docType">The document type to filter by</param>
        /// <returns>A MasterStatus entity with the specified ID and DocType, or null if not found</returns>
        Task<MasterStatus?> GetByIdAndDocTypeAsync(Guid id, string docType);

        /// <summary>
        /// Gets a list of MasterStatus entities by name and DocType
        /// </summary>
        /// <param name="name">The name to search for</param>
        /// <param name="docType">The document type to filter by</param>
        /// <returns>A list of MasterStatus entities with the specified name and DocType</returns>
        Task<List<MasterStatus>> GetByNameAndDocTypeAsync(string name, string docType);
    }
}
