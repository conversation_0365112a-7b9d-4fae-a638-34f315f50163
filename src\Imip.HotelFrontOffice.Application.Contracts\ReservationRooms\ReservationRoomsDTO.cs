﻿using System;
using Imip.HotelFrontOffice.MasterStatuses;
using Imip.HotelFrontOffice.PaymentDetails;
using Imip.HotelFrontOffice.Services;
using Volo.Abp.Application.Dtos;

namespace Imip.HotelFrontOffice.ReservationRooms;

public class ReservationRoomsDto : AuditedEntityDto<Guid>
{
    public Guid ReservationDetailsId { get; set; } = default!;
    public Guid ServiceId { get; set; } = default!;
    public Guid? PaymentStatusId { get; set; } = default!; // Optional property for payment status ID
    public decimal TotalPrice { get; set; } = default!;
    public int Quantity { get; set; } = default!;
    public string ServiceName { get; set; } = default!;
    public string ServiceTypeName { get; set; } = default!;
    public DateTime? TransactionDate { get; set; } = default!;
    public MasterStatusDto? PaymentStatus { get; set; } = default!; // Optional property for payment status
    public ServicesDto? Services { get; set; } = default!;
    public PaymentDetailsDto? PaymentDetails { get; set; } = default!;
}