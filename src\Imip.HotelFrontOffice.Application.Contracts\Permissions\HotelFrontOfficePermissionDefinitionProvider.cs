using Imip.HotelFrontOffice.Localization;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Localization;
using Volo.Abp.MultiTenancy;

namespace Imip.HotelFrontOffice.Permissions;

public class HotelFrontOfficePermissionDefinitionProvider : PermissionDefinitionProvider
{
    public override void Define(IPermissionDefinitionContext context)
    {
        var myGroup = context.AddGroup(HotelFrontOfficePermissions.GroupName);

        //Define your own permissions here. Example:
        //myGroup.AddPermission(HotelFrontOfficePermissions.MyPermission1, L("Permission:MyPermission1"));
    }

    private static LocalizableString L(string name)
    {
        return LocalizableString.Create<HotelFrontOfficeResource>(name);
    }
}
