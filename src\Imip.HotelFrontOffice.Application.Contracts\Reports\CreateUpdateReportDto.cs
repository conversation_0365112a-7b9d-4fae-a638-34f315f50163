using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;

namespace Imip.HotelFrontOffice.Reports;

public class CreateUpdateReportDto : AuditedEntityDto<Guid>
{
    [Required]
    [StringLength(200)]
    public string Name { get; set; } = default!;

    [StringLength(500)]
    public string Description { get; set; } = null!;

    [Required]
    public ReportQueryType QueryType { get; set; }

    [Required]
    public string Query { get; set; } = default!;

    public string Parameters { get; set; } = string.Empty; // JSON string of parameter definitions
    public string Roles { get; set; } = string.Empty; // Comma-separated list of roles

    public bool IsActive { get; set; } = true;

}
