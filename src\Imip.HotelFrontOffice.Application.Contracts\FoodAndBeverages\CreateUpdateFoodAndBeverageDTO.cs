﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Imip.HotelFrontOffice.FoodAndBeverages;

public class CreateUpdateFoodAndBeverageDto
{
    [Required]
    [StringLength(200)]
    public string Name { get; set; } = default!;

    [Required]
    [Range(0, double.MaxValue, ErrorMessage = "Price must be greater than or equal to 0")]
    public decimal Price { get; set; }

    //[Required]
    [StringLength(200)]
    public string Information { get; set; } = default!;

    [Required]
    public Guid TypeFoodAndBeverageId { get; set; }

    // Dictionary for extra properties
    public Dictionary<string, object>? ExtraProperties { get; set; }
}