﻿using System;
using System.ComponentModel.DataAnnotations;
using Imip.HotelFrontOffice.Payments;
using Imip.HotelFrontOffice.ReservationDetails;
using Imip.HotelFrontOffice.ReservationFoodAndBeverages;
using Imip.HotelFrontOffice.ReservationRooms;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.HotelFrontOffice.PaymentDetails;

public class PaymentDetail : FullAuditedAggregateRoot<Guid>
{
    public Guid PaymentId { get; set; }

    [EnumDataType(typeof(PaymentSourceType))]
    public PaymentSourceType? SourceType { get; set; }

    public Guid? SourceId { get; set; }
    public decimal? Qty { get; set; }
    public decimal? UnitPrice { get; set; }
    public decimal? VatRate { get; set; }
    public decimal? VatAmount { get; set; }
    public decimal? Amount { get; set; }

    public Guid? ReservationDetailsId { get; set; }
    public Guid? TaxId { get; set; }
    public virtual Payment? Payments { get; set; }
    public virtual ReservationDetail? ReservationDetails { get; set; }
    public virtual ReservationFoodAndBeverage? ReservationFoodAndBeverages { get; set; }
    public virtual ReservationRoom? ReservationRoom { get; set; }

    protected PaymentDetail()
    {
    }

    public PaymentDetail(
        Guid id,
        Guid paymentId,
        PaymentSourceType? sourceType,
        Guid? sourceId,
        Guid? reservationDetailsId,
        decimal amount = 0,
        decimal qty = 0,
        decimal unitPrice = 0
    )
    {
        Id = id;
        PaymentId = paymentId;

        // Validate that sourceType is a valid enum value if it's not null
        if (sourceType.HasValue && !Enum.IsDefined(typeof(PaymentSourceType), sourceType.Value))
        {
            // Try to convert numeric value to enum if it's not a valid enum directly
            int sourceTypeValue;
            bool isValidSourceType = false;

            // Check if the value is a valid integer that corresponds to an enum value
            if (int.TryParse(sourceType.Value.ToString(), out sourceTypeValue))
            {
                isValidSourceType = Enum.IsDefined(typeof(PaymentSourceType), sourceTypeValue);
                if (isValidSourceType)
                {
                    // Convert the numeric value to the actual enum value
                    sourceType = (PaymentSourceType)sourceTypeValue;
                }
            }

            if (!isValidSourceType)
            {
                throw new ArgumentException($"Invalid source type: {sourceType}. Must be one of the valid payment source types (1=ReservationRoom, 2=ReservationRoomFoodAndBeverage, 3=ReservationRoomService).", nameof(sourceType));
            }
        }

        SourceType = sourceType;
        SourceId = sourceId;
        Amount = amount;
        Qty = qty;
        UnitPrice = unitPrice;
        // VatRate = vatRate;
        // VatAmount = vatAmount;
        ReservationDetailsId = reservationDetailsId;
    }
}