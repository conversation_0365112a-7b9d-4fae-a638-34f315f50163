﻿using System;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Master.Company;
using Imip.HotelFrontOffice.Permissions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.Companies;

[Route("api/master/company")]
[Authorize(WismaAppPermissions.PolicyMasterCompany.Default)]
public class CompanyAppService : CrudAppService<
    Companies.Company,
    CompanyDto,
    Guid,
    PagedAndSortedResultRequestDto,
    CreateUpdateCompanyDto,
    CreateUpdateCompanyDto
>
{
    public CompanyAppService(IRepository<Companies.Company, Guid> repository) : base(repository)
    {
    }

    [HttpGet]
    [Authorize(WismaAppPermissions.PolicyMasterCompany.View)]
    public override Task<PagedResultDto<CompanyDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        return base.GetListAsync(input);
    }

    [HttpGet("{id}")]
    [Authorize(WismaAppPermissions.PolicyMasterCompany.View)]
    public override Task<CompanyDto> GetAsync(Guid id)
    {
        return base.GetAsync(id);
    }

    [HttpPost]
    [Authorize(WismaAppPermissions.PolicyMasterCompany.Create)]
    public override Task<CompanyDto> CreateAsync(CreateUpdateCompanyDto input)
    {
        return base.CreateAsync(input);
    }

    [HttpPut("{id}")]
    [Authorize(WismaAppPermissions.PolicyMasterCompany.Edit)]
    public override Task<CompanyDto> UpdateAsync(Guid id, CreateUpdateCompanyDto input)
    {
        return base.UpdateAsync(id, input);
    }

    [HttpDelete("{id}")]
    [Authorize(WismaAppPermissions.PolicyMasterCompany.Delete)]
    public override Task DeleteAsync(Guid id)
    {
        return base.DeleteAsync(id);
    }
}