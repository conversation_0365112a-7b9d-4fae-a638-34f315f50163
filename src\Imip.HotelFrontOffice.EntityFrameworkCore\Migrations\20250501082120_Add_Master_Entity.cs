﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.HotelFrontOffice.Migrations
{
    /// <inheritdoc />
    public partial class Add_Master_Entity : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_AppPayments_ReservationsId",
                table: "AppPayments");

            migrationBuilder.DropColumn(
                name: "CompanyName",
                table: "AppReservations");

            migrationBuilder.DropColumn(
                name: "DiningOptions",
                table: "AppReservations");

            migrationBuilder.DropColumn(
                name: "PaymentMethod",
                table: "AppReservations");

            migrationBuilder.DropColumn(
                name: "Status",
                table: "AppReservations");

            migrationBuilder.DropColumn(
                name: "PaymentMethod",
                table: "AppPayments");

            migrationBuilder.DropColumn(
                name: "PaymentStatus",
                table: "AppPayments");

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "AppTypeFoodAndBeverage",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "AppTypeFoodAndBeverage",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "AppTypeFoodAndBeverage",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "AppServiceTypes",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "AppServiceTypes",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "AppServiceTypes",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "AppServices",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "AppServices",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "AppServices",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "AppRoomTypes",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "AppRoomTypes",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "AppRoomTypes",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "AppRoomStatusLogs",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "AppRoomStatusLogs",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "AppRoomStatusLogs",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "AppRoomStatuses",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "AppRoomStatuses",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "AppRoomStatuses",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "AppRoom",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "AppRoom",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "AppRoom",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "AppReservationTypes",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "AppReservationTypes",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "AppReservationTypes",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "CompanyId",
                table: "AppReservations",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "AppReservations",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "AppReservations",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "DiningOptionsId",
                table: "AppReservations",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "AppReservations",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "PaymentMethodId",
                table: "AppReservations",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "StatusId",
                table: "AppReservations",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "AppReservationRooms",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "AppReservationRooms",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "AppReservationRooms",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "AppReservationFoodAndBeverages",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "AppReservationFoodAndBeverages",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "AppReservationFoodAndBeverages",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "AppReservationDetails",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "AppReservationDetails",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "AppReservationDetails",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "AppPayments",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "AppPayments",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "AppPayments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "PaymentMethodId",
                table: "AppPayments",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "StatusId",
                table: "AppPayments",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "AppPaymentGuests",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "AppPaymentGuests",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "AppPaymentGuests",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "AppPaymentDetails",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "AppPaymentDetails",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "AppPaymentDetails",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Guid>(
                name: "DeleterId",
                table: "AppFoodAndBeverage",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletionTime",
                table: "AppFoodAndBeverage",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "AppFoodAndBeverage",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.CreateTable(
                name: "MasterCompany",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Address = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Alias = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ExtraProperties = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ConcurrencyStamp = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    DeleterId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MasterCompany", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "MasterDiningOptions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    ExtraProperties = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ConcurrencyStamp = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    DeleterId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MasterDiningOptions", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "MasterPaymentMethod",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Information = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    ExtraProperties = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ConcurrencyStamp = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    DeleterId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MasterPaymentMethod", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "MasterStatus",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    DocType = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    ExtraProperties = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ConcurrencyStamp = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    DeleterId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MasterStatus", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AppReservations_CompanyId",
                table: "AppReservations",
                column: "CompanyId");

            migrationBuilder.CreateIndex(
                name: "IX_AppReservations_DiningOptionsId",
                table: "AppReservations",
                column: "DiningOptionsId");

            migrationBuilder.CreateIndex(
                name: "IX_AppReservations_PaymentMethodId",
                table: "AppReservations",
                column: "PaymentMethodId");

            migrationBuilder.CreateIndex(
                name: "IX_AppReservations_StatusId_PaymentMethodId_DiningOptionsId_CompanyId",
                table: "AppReservations",
                columns: new[] { "StatusId", "PaymentMethodId", "DiningOptionsId", "CompanyId" });

            migrationBuilder.CreateIndex(
                name: "IX_AppPayments_PaymentMethodId",
                table: "AppPayments",
                column: "PaymentMethodId");

            migrationBuilder.CreateIndex(
                name: "IX_AppPayments_ReservationsId_ReservationDetailsId_StatusId_PaymentMethodId",
                table: "AppPayments",
                columns: new[] { "ReservationsId", "ReservationDetailsId", "StatusId", "PaymentMethodId" });

            migrationBuilder.CreateIndex(
                name: "IX_AppPayments_StatusId",
                table: "AppPayments",
                column: "StatusId");

            migrationBuilder.CreateIndex(
                name: "IX_MasterCompany_Name",
                table: "MasterCompany",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_MasterDiningOptions_Name",
                table: "MasterDiningOptions",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_MasterPaymentMethod_Name",
                table: "MasterPaymentMethod",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_MasterStatus_Name",
                table: "MasterStatus",
                column: "Name");

            migrationBuilder.AddForeignKey(
                name: "FK_AppPayments_MasterPaymentMethod_PaymentMethodId",
                table: "AppPayments",
                column: "PaymentMethodId",
                principalTable: "MasterPaymentMethod",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_AppPayments_MasterStatus_StatusId",
                table: "AppPayments",
                column: "StatusId",
                principalTable: "MasterStatus",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_AppReservations_MasterDiningOptions_CompanyId",
                table: "AppReservations",
                column: "CompanyId",
                principalTable: "MasterDiningOptions",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_AppReservations_MasterDiningOptions_DiningOptionsId",
                table: "AppReservations",
                column: "DiningOptionsId",
                principalTable: "MasterDiningOptions",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_AppReservations_MasterPaymentMethod_PaymentMethodId",
                table: "AppReservations",
                column: "PaymentMethodId",
                principalTable: "MasterPaymentMethod",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_AppReservations_MasterStatus_StatusId",
                table: "AppReservations",
                column: "StatusId",
                principalTable: "MasterStatus",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AppPayments_MasterPaymentMethod_PaymentMethodId",
                table: "AppPayments");

            migrationBuilder.DropForeignKey(
                name: "FK_AppPayments_MasterStatus_StatusId",
                table: "AppPayments");

            migrationBuilder.DropForeignKey(
                name: "FK_AppReservations_MasterDiningOptions_CompanyId",
                table: "AppReservations");

            migrationBuilder.DropForeignKey(
                name: "FK_AppReservations_MasterDiningOptions_DiningOptionsId",
                table: "AppReservations");

            migrationBuilder.DropForeignKey(
                name: "FK_AppReservations_MasterPaymentMethod_PaymentMethodId",
                table: "AppReservations");

            migrationBuilder.DropForeignKey(
                name: "FK_AppReservations_MasterStatus_StatusId",
                table: "AppReservations");

            migrationBuilder.DropTable(
                name: "MasterCompany");

            migrationBuilder.DropTable(
                name: "MasterDiningOptions");

            migrationBuilder.DropTable(
                name: "MasterPaymentMethod");

            migrationBuilder.DropTable(
                name: "MasterStatus");

            migrationBuilder.DropIndex(
                name: "IX_AppReservations_CompanyId",
                table: "AppReservations");

            migrationBuilder.DropIndex(
                name: "IX_AppReservations_DiningOptionsId",
                table: "AppReservations");

            migrationBuilder.DropIndex(
                name: "IX_AppReservations_PaymentMethodId",
                table: "AppReservations");

            migrationBuilder.DropIndex(
                name: "IX_AppReservations_StatusId_PaymentMethodId_DiningOptionsId_CompanyId",
                table: "AppReservations");

            migrationBuilder.DropIndex(
                name: "IX_AppPayments_PaymentMethodId",
                table: "AppPayments");

            migrationBuilder.DropIndex(
                name: "IX_AppPayments_ReservationsId_ReservationDetailsId_StatusId_PaymentMethodId",
                table: "AppPayments");

            migrationBuilder.DropIndex(
                name: "IX_AppPayments_StatusId",
                table: "AppPayments");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "AppTypeFoodAndBeverage");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "AppTypeFoodAndBeverage");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "AppTypeFoodAndBeverage");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "AppServiceTypes");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "AppServiceTypes");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "AppServiceTypes");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "AppServices");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "AppServices");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "AppServices");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "AppRoomTypes");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "AppRoomTypes");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "AppRoomTypes");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "AppRoomStatusLogs");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "AppRoomStatusLogs");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "AppRoomStatusLogs");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "AppRoomStatuses");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "AppRoomStatuses");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "AppRoomStatuses");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "AppRoom");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "AppRoom");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "AppRoom");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "AppReservationTypes");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "AppReservationTypes");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "AppReservationTypes");

            migrationBuilder.DropColumn(
                name: "CompanyId",
                table: "AppReservations");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "AppReservations");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "AppReservations");

            migrationBuilder.DropColumn(
                name: "DiningOptionsId",
                table: "AppReservations");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "AppReservations");

            migrationBuilder.DropColumn(
                name: "PaymentMethodId",
                table: "AppReservations");

            migrationBuilder.DropColumn(
                name: "StatusId",
                table: "AppReservations");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "AppReservationRooms");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "AppReservationRooms");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "AppReservationRooms");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "AppReservationFoodAndBeverages");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "AppReservationFoodAndBeverages");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "AppReservationFoodAndBeverages");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "AppReservationDetails");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "AppReservationDetails");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "AppReservationDetails");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "AppPayments");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "AppPayments");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "AppPayments");

            migrationBuilder.DropColumn(
                name: "PaymentMethodId",
                table: "AppPayments");

            migrationBuilder.DropColumn(
                name: "StatusId",
                table: "AppPayments");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "AppPaymentGuests");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "AppPaymentGuests");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "AppPaymentGuests");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "AppPaymentDetails");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "AppPaymentDetails");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "AppPaymentDetails");

            migrationBuilder.DropColumn(
                name: "DeleterId",
                table: "AppFoodAndBeverage");

            migrationBuilder.DropColumn(
                name: "DeletionTime",
                table: "AppFoodAndBeverage");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "AppFoodAndBeverage");

            migrationBuilder.AddColumn<string>(
                name: "CompanyName",
                table: "AppReservations",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DiningOptions",
                table: "AppReservations",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PaymentMethod",
                table: "AppReservations",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true);

            migrationBuilder.AddColumn<short>(
                name: "Status",
                table: "AppReservations",
                type: "smallint",
                nullable: false,
                defaultValue: (short)1);

            migrationBuilder.AddColumn<string>(
                name: "PaymentMethod",
                table: "AppPayments",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true);

            migrationBuilder.AddColumn<short>(
                name: "PaymentStatus",
                table: "AppPayments",
                type: "smallint",
                nullable: false,
                defaultValue: (short)0);

            migrationBuilder.CreateIndex(
                name: "IX_AppPayments_ReservationsId",
                table: "AppPayments",
                column: "ReservationsId");
        }
    }
}
