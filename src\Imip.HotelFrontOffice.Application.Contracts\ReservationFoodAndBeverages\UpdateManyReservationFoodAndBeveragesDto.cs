using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Imip.HotelFrontOffice.ReservationFoodAndBeverages
{
    /// <summary>
    /// DTO for updating multiple reservation food and beverages in a single request
    /// </summary>
    public class UpdateManyReservationFoodAndBeveragesDto
    {
        /// <summary>
        /// List of food and beverage items to update
        /// </summary>
        [Required]
        public List<CreateUpdateReservationFoodAndBeveragesDto> Items { get; set; } = new List<CreateUpdateReservationFoodAndBeveragesDto>();
    }
}
