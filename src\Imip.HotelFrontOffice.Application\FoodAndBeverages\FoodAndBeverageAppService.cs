﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Permissions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Data;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.FoodAndBeverages;

[Authorize(WismaAppPermissions.PolicyFoodAndBeverage.Default)]
public class FoodAndBeverageAppService : PermissionCheckedCrudAppService<
    FoodAndBeverage,
    FoodAndBeverageDto,
    Guid,
    PagedAndSortedResultRequestDto,
    CreateUpdateFoodAndBeverageDto,
    CreateUpdateFoodAndBeverageDto
>, IFoodAndBeverageAppService
{
    private readonly IRepository<FoodAndBeverage, Guid> _repository;
    public FoodAndBeverageAppService(IRepository<FoodAndBeverage, Guid> repository,
        IPermissionChecker permissionChecker)
        : base(repository, permissionChecker)
    {
        _repository = repository;

        GetPolicyName = WismaAppPermissions.PolicyFoodAndBeverage.View;
        GetListPolicyName = WismaAppPermissions.PolicyFoodAndBeverage.View;
        CreatePolicyName = WismaAppPermissions.PolicyFoodAndBeverage.Create;
        UpdatePolicyName = WismaAppPermissions.PolicyFoodAndBeverage.Edit;
        DeletePolicyName = WismaAppPermissions.PolicyFoodAndBeverage.Delete;
    }

    protected override async Task<IQueryable<FoodAndBeverage>> CreateFilteredQueryAsync(
        PagedAndSortedResultRequestDto input)
    {
        var query = await Repository.GetQueryableAsync();
        return query.Include(x => x.TypeFoodAndBeverage)
            .OrderByDescending(x => x.CreationTime);
    }

    public override async Task<FoodAndBeverageDto> CreateAsync(CreateUpdateFoodAndBeverageDto input)
    {
        await CheckCreatePolicyAsync();

        var entity = await MapToEntityAsync(input);

        // Set ExtraProperties for foreign names if provided in the input
        if (input.ExtraProperties != null)
        {
            foreach (var property in input.ExtraProperties)
            {
                entity.SetProperty(property.Key, property.Value);
            }
        }
        else
        {
            // Set default foreign names if not provided
            entity.SetProperty("ForeignName1", string.Empty);
            entity.SetProperty("ForeignName2", string.Empty);
        }

        await Repository.InsertAsync(entity);

        return await MapToGetOutputDtoAsync(entity);
    }

    public override async Task<FoodAndBeverageDto> UpdateAsync(Guid id, CreateUpdateFoodAndBeverageDto input)
    {
        await CheckUpdatePolicyAsync();

        var entity = await Repository.GetAsync(id);

        await MapToEntityAsync(input, entity);

        // Update ExtraProperties for foreign names if provided in the input
        if (input.ExtraProperties != null)
        {
            foreach (var property in input.ExtraProperties)
            {
                entity.SetProperty(property.Key, property.Value);
            }
        }

        await Repository.UpdateAsync(entity);

        return await MapToGetOutputDtoAsync(entity);
    }
}