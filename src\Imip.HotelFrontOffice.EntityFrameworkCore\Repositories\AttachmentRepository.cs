using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Attachments;
using Imip.HotelFrontOffice.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.HotelFrontOffice.Repositories
{
    /// <summary>
    /// Repository implementation for Attachment entity
    /// </summary>
    public class AttachmentRepository : EfCoreRepository<HotelFrontOfficeDbContext, Attachment, Guid>, IAttachmentRepository
    {
        public AttachmentRepository(IDbContextProvider<HotelFrontOfficeDbContext> dbContextProvider)
            : base(dbContextProvider)
        {
        }

        /// <summary>
        /// Gets attachments by reference ID and type
        /// </summary>
        public async Task<List<Attachment>> GetByReferenceAsync(Guid referenceId, string referenceType)
        {
            var dbContext = await GetDbContextAsync();
            
            return await dbContext.Set<Attachment>()
                .Where(a => a.ReferenceId == referenceId && a.ReferenceType == referenceType)
                .ToListAsync();
        }
    }
}
