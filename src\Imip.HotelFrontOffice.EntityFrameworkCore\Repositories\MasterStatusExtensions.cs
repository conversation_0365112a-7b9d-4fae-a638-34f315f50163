using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.MasterStatuses;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.Repositories
{
    public static class MasterStatusExtensions
    {
        /// <summary>
        /// Gets MasterStatus entities filtered by DocType
        /// </summary>
        /// <param name="repository">The MasterStatus repository</param>
        /// <param name="docType">The document type to filter by</param>
        /// <returns>A queryable of MasterStatus entities filtered by DocType</returns>
        public static IQueryable<MasterStatus> GetByDocType(this IRepository<MasterStatus, Guid> repository, string docType)
        {
            return repository.GetQueryableAsync().Result.Where(x => x.DocType == docType);
        }

        /// <summary>
        /// Gets MasterStatus entities filtered by DocType asynchronously
        /// </summary>
        /// <param name="repository">The MasterStatus repository</param>
        /// <param name="docType">The document type to filter by</param>
        /// <returns>A list of MasterStatus entities filtered by DocType</returns>
        public static async Task<List<MasterStatus>> GetByDocTypeAsync(this IRepository<MasterStatus, Guid> repository, string docType)
        {
            var queryable = await repository.GetQueryableAsync();
            return await queryable.Where(x => x.DocType == docType).ToListAsync();
        }

        /// <summary>
        /// Gets a single MasterStatus entity by ID and DocType
        /// </summary>
        /// <param name="repository">The MasterStatus repository</param>
        /// <param name="id">The ID of the MasterStatus entity</param>
        /// <param name="docType">The document type to filter by</param>
        /// <returns>A MasterStatus entity with the specified ID and DocType, or null if not found</returns>
        public static async Task<MasterStatus?> GetByIdAndDocTypeAsync(this IRepository<MasterStatus, Guid> repository, Guid id, string docType)
        {
            var queryable = await repository.GetQueryableAsync();
            return await queryable.FirstOrDefaultAsync(x => x.Id == id && x.DocType == docType);
        }
    }
}
