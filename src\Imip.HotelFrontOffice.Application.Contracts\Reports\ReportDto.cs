using System;
using Volo.Abp.Application.Dtos;

namespace Imip.HotelFrontOffice.Reports;

public class ReportDto : AuditedEntityDto<Guid>
{
    public string Name { get; set; } = default!;
    public string? Description { get; set; }
    public ReportQueryType QueryType { get; set; }
    public string? Query { get; set; }
    public string? Parameters { get; set; } // JSON string of parameter definitions
    public bool IsActive { get; set; } = true;
    public string? Roles { get; set; }

}
