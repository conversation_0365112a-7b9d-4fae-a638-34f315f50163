using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.MultiTenancy;

namespace Imip.HotelFrontOffice.TestDataSeedContributors;

/// <summary>
/// A test-specific implementation of identity data seeder that doesn't rely on database repositories
/// </summary>
public class TestIdentityDataSeedContributor : IDataSeedContributor, ITransientDependency
{
    private readonly ICurrentTenant _currentTenant;
    private readonly ILogger<TestIdentityDataSeedContributor> _logger;

    public TestIdentityDataSeedContributor(
        ICurrentTenant currentTenant,
        ILogger<TestIdentityDataSeedContributor> logger)
    {
        _currentTenant = currentTenant;
        _logger = logger;
    }

    public Task SeedAsync(DataSeedContext context)
    {
        try
        {
            using (_currentTenant.Change(context?.TenantId))
            {
                // In a test environment, we don't need to seed actual identity data
                // This is just a placeholder to satisfy the dependency
                return Task.CompletedTask;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning("Error while seeding test identity data: {Message}", ex.Message);
            // Don't throw exceptions during test data seeding to prevent test failures
            return Task.CompletedTask;
        }
    }
}
