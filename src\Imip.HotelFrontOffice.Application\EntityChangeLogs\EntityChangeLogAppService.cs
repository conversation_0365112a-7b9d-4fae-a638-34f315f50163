using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.EntityChangeLogs.Dtos;
using Imip.HotelFrontOffice.EntityFrameworkCore;
using Imip.HotelFrontOffice.Models;
using Imip.HotelFrontOffice.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.AuditLogging;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.HotelFrontOffice.EntityChangeLogs;

/// <summary>
/// Implementation of the IEntityChangeLogAppService interface
/// </summary>
[Authorize]
[RemoteService(false)] // This prevents the service from being exposed as an API endpoint in Swagger
public class EntityChangeLogAppService : HotelFrontOfficeAppService, IEntityChangeLogAppService
{
    private readonly IAuditLogRepository _auditLogRepository;
    private readonly IRepository<MasterStatuses.MasterStatus, Guid>? _masterStatusRepository;
    private readonly IRepository<RoomStatuses.RoomStatus, Guid>? _roomStatusesRepository;
    private readonly IDbContextProvider<HotelFrontOfficeDbContext> _dbContextProvider;

    public EntityChangeLogAppService(
        IAuditLogRepository auditLogRepository,
        IDbContextProvider<HotelFrontOfficeDbContext> dbContextProvider,
        IRepository<MasterStatuses.MasterStatus, Guid>? masterStatusRepository = null,
        IRepository<RoomStatuses.RoomStatus, Guid>? roomStatusesRepository = null)
    {
        _auditLogRepository = auditLogRepository;
        _dbContextProvider = dbContextProvider;
        _masterStatusRepository = masterStatusRepository;
        _roomStatusesRepository = roomStatusesRepository;
    }

    public async Task<PagedResultDto<EntityChangeLogDto>> GetEntityChangeLogsAsync(GetEntityChangeLogInput input)
    {
        // Get audit logs with entity changes
        var auditLogs = await _auditLogRepository.GetListAsync(
            startTime: input.StartTime,
            endTime: input.EndTime,
            maxResultCount: input.MaxResultCount,
            skipCount: input.SkipCount,
            includeDetails: true);

        // Extract and filter entity changes
        var entityChanges = auditLogs
            .SelectMany(al => al.EntityChanges)
            .Where(ec =>
                (string.IsNullOrEmpty(input.EntityTypeFullName) || ec.EntityTypeFullName == input.EntityTypeFullName) &&
                (string.IsNullOrEmpty(input.EntityId) || ec.EntityId == input.EntityId) &&
                (!input.ChangeType.HasValue || (EntityChangeType)input.ChangeType.Value == (EntityChangeType)ec.ChangeType))
            .OrderByDescending(ec => ec.ChangeTime)
            .Take(input.MaxResultCount)
            .ToList();

        // Get total count
        var totalCount = entityChanges.Count;

        // Map to DTOs
        var dtos = ObjectMapper.Map<List<EntityChange>, List<EntityChangeLogDto>>(entityChanges);

        // Enhance property changes with reference information
        foreach (var entityChangeDto in dtos)
        {
            if (entityChangeDto.PropertyChanges == null)
            {
                continue;
            }

            foreach (var propertyChange in entityChangeDto.PropertyChanges)
            {
                await EnhancePropertyChangeWithReferenceInfoAsync(propertyChange);
            }
        }

        return new PagedResultDto<EntityChangeLogDto>(totalCount, dtos);
    }

    public async Task<EntityChangeLogDto> GetLatestEntityChangeLogAsync(string entityId, string entityTypeFullName)
    {
        // Get audit logs with entity changes
        var auditLogs = await _auditLogRepository.GetListAsync(
            includeDetails: true,
            maxResultCount: 100);

        // Find the latest entity change
        var entityChange = auditLogs
            .SelectMany(al => al.EntityChanges)
            .Where(ec => ec.EntityId == entityId && ec.EntityTypeFullName == entityTypeFullName)
            .OrderByDescending(ec => ec.ChangeTime)
            .FirstOrDefault();

        if (entityChange == null)
        {
            return new EntityChangeLogDto();
        }

        var dto = ObjectMapper.Map<EntityChange, EntityChangeLogDto>(entityChange);

        // Enhance property changes with reference information
        if (dto.PropertyChanges != null)
        {
            foreach (var propertyChange in dto.PropertyChanges)
            {
                await EnhancePropertyChangeWithReferenceInfoAsync(propertyChange);
            }
        }

        return dto;
    }

    public async Task<List<EntityChangeLogDto>> GetEntityTypeChangeLogsAsync(
        string entityTypeFullName,
        int maxResultCount = 10,
        int skipCount = 0)
    {
        // Get audit logs with entity changes - use a larger maxResultCount to ensure we get enough data
        // We need to fetch more records because we're filtering at the application level
        var auditLogs = await _auditLogRepository.GetListAsync(
            includeDetails: true,
            maxResultCount: 1000);

        // Extract and filter entity changes
        var entityChanges = auditLogs
            .SelectMany(al => al.EntityChanges)
            .Where(ec => ec.EntityTypeFullName == entityTypeFullName)
            .OrderByDescending(ec => ec.ChangeTime)
            .Skip(skipCount)
            .Take(maxResultCount)
            .ToList();

        var dtos = ObjectMapper.Map<List<EntityChange>, List<EntityChangeLogDto>>(entityChanges);

        // Enhance property changes with reference information
        foreach (var entityChangeDto in dtos)
        {
            if (entityChangeDto.PropertyChanges == null)
            {
                continue;
            }

            foreach (var propertyChange in entityChangeDto.PropertyChanges)
            {
                await EnhancePropertyChangeWithReferenceInfoAsync(propertyChange);
            }
        }

        return dtos;
    }

    public async Task<List<EntityChangeLogDto>> GetTimeRangeChangeLogsAsync(
        DateTime startTime,
        DateTime endTime,
        int maxResultCount = 10,
        int skipCount = 0)
    {
        // Get audit logs with entity changes - use a larger maxResultCount to ensure we get enough data
        // We need to fetch more records because we're filtering at the application level
        var auditLogs = await _auditLogRepository.GetListAsync(
            startTime: startTime,
            endTime: endTime,
            maxResultCount: 1000,
            includeDetails: true);

        // Extract and filter entity changes
        var entityChanges = auditLogs
            .SelectMany(al => al.EntityChanges)
            .Where(ec => ec.ChangeTime >= startTime && ec.ChangeTime <= endTime)
            .OrderByDescending(ec => ec.ChangeTime)
            .Skip(skipCount)
            .Take(maxResultCount)
            .ToList();

        var dtos = ObjectMapper.Map<List<EntityChange>, List<EntityChangeLogDto>>(entityChanges);

        // Enhance property changes with reference information
        foreach (var entityChangeDto in dtos)
        {
            if (entityChangeDto.PropertyChanges == null)
            {
                continue;
            }

            foreach (var propertyChange in entityChangeDto.PropertyChanges)
            {
                await EnhancePropertyChangeWithReferenceInfoAsync(propertyChange);
            }
        }

        return dtos;
    }

    public async Task<List<EntityPropertyChangeLogDto>> GetPropertyChangeHistoryAsync(
        string entityId,
        string entityTypeFullName,
        string propertyName,
        int maxResultCount = 10,
        int skipCount = 0)
    {
        // Get audit logs with entity changes - use a larger maxResultCount to ensure we get enough data
        // We need to fetch more records because we're filtering at the application level
        var auditLogs = await _auditLogRepository.GetListAsync(
            includeDetails: true,
            maxResultCount: 1000);

        // Extract and filter property changes
        var entityChanges = auditLogs
            .SelectMany(al => al.EntityChanges)
            .Where(ec => ec.EntityId == entityId && ec.EntityTypeFullName == entityTypeFullName)
            .ToList();

        var propertyChanges = new List<EntityPropertyChange>();

        foreach (var entityChange in entityChanges)
        {
            var matchingPropertyChanges = entityChange.PropertyChanges
                .Where(pc => pc.PropertyName == propertyName)
                .ToList();

            propertyChanges.AddRange(matchingPropertyChanges);
        }

        // Get total count before pagination
        var totalCount = propertyChanges.Count;

        // Apply pagination - since EntityPropertyChange doesn't have ChangeTime,
        // we'll just apply pagination without sorting
        propertyChanges = propertyChanges
            .Skip(skipCount)
            .Take(maxResultCount)
            .ToList();

        var result = ObjectMapper.Map<List<EntityPropertyChange>, List<EntityPropertyChangeLogDto>>(propertyChanges);

        // Enhance property changes with reference information
        foreach (var propertyChange in result)
        {
            await EnhancePropertyChangeWithReferenceInfoAsync(propertyChange);
        }

        return result;
    }

    public async Task<PagedResultDto<AuditLogWithDetailsDto>> GetAuditLogsWithDetailsAsync(
        DateTime startTime,
        DateTime endTime,
        int maxResultCount = 10,
        int skipCount = 0)
    {
        // Get total count
        var totalCount = await _auditLogRepository.GetCountAsync(
            startTime: startTime,
            endTime: endTime);

        // Ensure maxResultCount is at least 1
        maxResultCount = Math.Max(1, maxResultCount);

        // Get audit logs with details - apply pagination directly at the database level
        var auditLogs = await _auditLogRepository.GetListAsync(
            startTime: startTime,
            endTime: endTime,
            maxResultCount: maxResultCount,
            skipCount: skipCount,
            includeDetails: true);

        // Map to DTOs
        var auditLogDtos = ObjectMapper.Map<List<AuditLog>, List<AuditLogWithDetailsDto>>(auditLogs);

        // Populate additional information
        foreach (var auditLogDto in auditLogDtos)
        {
            // Sort entity changes by change time
            auditLogDto.EntityChanges = auditLogDto.EntityChanges
                .OrderByDescending(x => x.ChangeTime)
                .ToList();

            // Sort actions by execution time
            auditLogDto.Actions = auditLogDto.Actions
                .OrderByDescending(x => x.ExecutionTime)
                .ToList();

            // Enhance property changes with reference information
            await EnhancePropertyChangesWithReferenceInfoAsync(auditLogDto);
        }

        return new PagedResultDto<AuditLogWithDetailsDto>(totalCount, auditLogDtos);
    }

    /// <summary>
    /// Enhances property changes with reference information for an audit log
    /// </summary>
    private async Task EnhancePropertyChangesWithReferenceInfoAsync(AuditLogWithDetailsDto auditLogDto)
    {
        foreach (var entityChange in auditLogDto.EntityChanges)
        {
            if (entityChange.PropertyChanges == null)
            {
                continue;
            }

            foreach (var propertyChange in entityChange.PropertyChanges)
            {
                await EnhancePropertyChangeWithReferenceInfoAsync(propertyChange);
            }
        }
    }

    /// <summary>
    /// Enhances a single property change with reference information
    /// </summary>
    private async Task EnhancePropertyChangeWithReferenceInfoAsync(EntityPropertyChangeLogDto propertyChange)
    {
        // Check if this is a StatusId property
        if (propertyChange.PropertyName != null && propertyChange.PropertyName.EndsWith("StatusId", StringComparison.OrdinalIgnoreCase))
        {
            propertyChange.IsReference = true;

            // Try to get the status name for the original value
            if (!string.IsNullOrEmpty(propertyChange.OriginalValue) &&
                propertyChange.OriginalValue.Contains('"') && // Check if it's a JSON string
                Guid.TryParse(propertyChange.OriginalValue.Trim('"'), out var originalStatusId))
            {
                var originalStatus = await GetStatusNameAsync(originalStatusId);
                if (originalStatus != null)
                {
                    propertyChange.OriginalValueDisplay = originalStatus;
                }
            }

            // Try to get the status name for the new value
            if (!string.IsNullOrEmpty(propertyChange.NewValue) &&
                propertyChange.NewValue.Contains('"') && // Check if it's a JSON string
                Guid.TryParse(propertyChange.NewValue.Trim('"'), out var newStatusId))
            {
                var newStatus = await GetStatusNameAsync(newStatusId);
                if (newStatus != null)
                {
                    propertyChange.NewValueDisplay = newStatus;
                }
            }
        }

        // Add more property types here as needed
        // For example: RoomId, UserId, etc.
    }

    /// <summary>
    /// Gets the status name for a given status ID
    /// </summary>
    private async Task<string?> GetStatusNameAsync(Guid statusId)
    {
        try
        {
            // Try to get from MasterStatus
            if (_masterStatusRepository != null)
            {
                var masterStatus = await _masterStatusRepository.FindAsync(statusId);
                if (masterStatus != null)
                {
                    return $"{masterStatus.Name} ({masterStatus.DocType})";
                }
            }

            // Try to get from RoomStatuses
            if (_roomStatusesRepository != null)
            {
                var roomStatus = await _roomStatusesRepository.FindAsync(statusId);
                if (roomStatus != null)
                {
                    return roomStatus.Name;
                }
            }

            // If we couldn't find the status, return the ID as a fallback
            return $"Status ID: {statusId}";
        }
        catch
        {
            // Catch any exceptions but don't throw them
            // Just return the fallback value
            return $"Status ID: {statusId}";
        }
    }

    /// <summary>
    /// Process audit logs manually for a specific time range
    /// </summary>
    public async Task<int> ProcessAuditLogsManuallyAsync(DateTime startTime, DateTime endTime)
    {
        // Get audit logs for the specified time range
        var auditLogs = await _auditLogRepository.GetListAsync(
            startTime: startTime,
            endTime: endTime,
            includeDetails: true);

        // Process each audit log
        int processedCount = 0;
        foreach (var auditLog in auditLogs)
        {
            // Process entity changes
            foreach (var entityChange in auditLog.EntityChanges)
            {
                // Process property changes
                foreach (var propertyChange in entityChange.PropertyChanges)
                {
                    // Process the property change as needed
                    // This is just a placeholder for any custom processing logic
                }
            }
            processedCount++;
        }

        return processedCount;
    }

    /// <summary>
    /// Get raw audit logs for a specific time range
    /// </summary>
    public async Task<List<object>> GetRawAuditLogsAsync(DateTime startTime, DateTime endTime, int maxResultCount = 100)
    {
        // Get audit logs for the specified time range
        var auditLogs = await _auditLogRepository.GetListAsync(
            startTime: startTime,
            endTime: endTime,
            maxResultCount: maxResultCount,
            includeDetails: true);

        // Convert to list of objects
        var result = new List<object>();
        foreach (var auditLog in auditLogs)
        {
            result.Add(auditLog);
        }

        return result;
    }

    /// <summary>
    /// Gets entity changes with property changes by entity ID with pagination
    /// </summary>
    public async Task<PagedResultDto<EntityChangeLogDto>> GetEntityChangesByIdAsync(GetEntityChangesByIdInput input)
    {
        // Validate input
        if (string.IsNullOrEmpty(input.EntityId))
        {
            throw new ArgumentException("EntityId is required", nameof(input));
        }

        // Get audit logs with entity changes - use a larger maxResultCount to ensure we get enough data
        // We need to fetch more records because we're filtering at the application level
        var auditLogs = await _auditLogRepository.GetListAsync(
            startTime: input.StartTime,
            endTime: input.EndTime,
            maxResultCount: input.MaxResultCount,
            includeDetails: true);

        // Create a dictionary to store audit logs by ID for quick lookup
        var auditLogDict = auditLogs.ToDictionary(al => al.Id, al => al);

        // Extract and filter entity changes
        var query = auditLogs
            .SelectMany(al => al.EntityChanges.Select(ec => new { AuditLog = al, EntityChange = ec }))
            .Where(x => x.EntityChange.EntityId == input.EntityId);

        // Apply additional filter if EntityTypeFullName is provided
        if (!string.IsNullOrEmpty(input.EntityTypeFullName))
        {
            query = query.Where(x => x.EntityChange.EntityTypeFullName == input.EntityTypeFullName);
        }

        // Get total count before pagination
        var totalCount = query.Count();

        // Apply sorting and pagination
        var entityChangesWithAuditLogs = query
            .OrderByDescending(x => x.EntityChange.ChangeTime)
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToList();

        // Extract just the entity changes for mapping
        var entityChanges = entityChangesWithAuditLogs.Select(x => x.EntityChange).ToList();

        // Map to DTOs
        var dtos = ObjectMapper.Map<List<EntityChange>, List<EntityChangeLogDto>>(entityChanges);

        // Add username from audit logs
        for (int i = 0; i < dtos.Count; i++)
        {
            var auditLog = entityChangesWithAuditLogs[i].AuditLog;
            dtos[i].UserName = auditLog.UserName;
        }

        // Enhance property changes with reference information
        foreach (var entityChangeDto in dtos)
        {
            if (entityChangeDto.PropertyChanges == null)
            {
                continue;
            }

            foreach (var propertyChange in entityChangeDto.PropertyChanges)
            {
                await EnhancePropertyChangeWithReferenceInfoAsync(propertyChange);
            }
        }

        return new PagedResultDto<EntityChangeLogDto>(totalCount, dtos);
    }

    /// <summary>
    /// Gets entity change logs with dynamic filtering and sorting
    /// </summary>
    /// <param name="input">Query parameters including filtering and sorting</param>
    /// <returns>Paged list of entity change logs</returns>
    public async Task<PagedResultDto<EntityChangeLogDto>> GetEntityChangeLogsWithDynamicQueryAsync(QueryParameters input)
    {
        // Get the database context to access ABP's EntityChanges directly
        var dbContext = await _dbContextProvider.GetDbContextAsync();

        // Create base query for ABP's EntityChanges with related audit log data
        // Note: EntityChange doesn't have direct navigation to AuditLog, so we'll join manually
        var auditLogQuery = dbContext.Set<AuditLog>().AsNoTracking();
        var auditLogActionQuery = dbContext.Set<AuditLogAction>().AsNoTracking();

        IQueryable<EntityChange> query = dbContext.Set<EntityChange>()
            .AsNoTracking()
            .Include(ec => ec.PropertyChanges);

        // Apply dynamic filters if provided
        if (input.FilterGroup?.Conditions != null && input.FilterGroup.Conditions.Count > 0)
        {
            // Check if we need to join with audit logs for filtering
            var needsAuditLogJoin = input.FilterGroup.Conditions.Any(c =>
                c.FieldName.StartsWith("AuditLog.") ||
                c.FieldName.StartsWith("UserName") ||
                c.FieldName.StartsWith("ExecutionTime"));

            var needsAuditLogActionJoin = input.FilterGroup.Conditions.Any(c =>
                c.FieldName.StartsWith("AuditLogActions.") ||
                c.FieldName.StartsWith("ServiceName") ||
                c.FieldName.StartsWith("MethodName"));

            if (needsAuditLogJoin || needsAuditLogActionJoin)
            {
                // Create a more complex query with joins for filtering
                var joinedQuery = from ec in query
                                  join al in auditLogQuery on ec.AuditLogId equals al.Id
                                  select new { EntityChange = ec, AuditLog = al };

                if (needsAuditLogActionJoin)
                {
                    var joinedWithActionsQuery = from eca in joinedQuery
                                                 join ala in auditLogActionQuery on eca.AuditLog.Id equals ala.AuditLogId into actions
                                                 from action in actions.DefaultIfEmpty()
                                                 select new { eca.EntityChange, eca.AuditLog, AuditLogAction = action };

                    // Apply filters on the joined query (this is complex and may need custom implementation)
                    // For now, we'll apply basic EntityChange filters and handle audit log filters separately
                    query = DynamicQueryBuilder<EntityChange>.ApplyFilters(query, input.FilterGroup);
                }
                else
                {
                    // Apply filters on the joined query
                    query = DynamicQueryBuilder<EntityChange>.ApplyFilters(query, input.FilterGroup);
                }
            }
            else
            {
                // Apply standard filters on EntityChange only
                query = DynamicQueryBuilder<EntityChange>.ApplyFilters(query, input.FilterGroup);
            }
        }

        // Apply sorting
        if (input.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<EntityChange>.ApplyMultipleSorting(query, input.Sort);
        }
        else if (!string.IsNullOrEmpty(input.Sorting))
        {
            // Use the default sorting if provided
            var sortDesc = input.Sorting.StartsWith('-');
            var sortField = sortDesc ? input.Sorting[1..] : input.Sorting;
            query = DynamicQueryBuilder<EntityChange>.ApplySorting(query, sortField, sortDesc);
        }
        else
        {
            // Default sorting by change time descending
            query = query.OrderByDescending(x => x.ChangeTime);
        }

        // Get total count before pagination
        var totalCount = await query.CountAsync();

        // Apply pagination
        var entityChanges = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        // Get audit log IDs for the entity changes
        var auditLogIds = entityChanges.Select(ec => ec.AuditLogId).Distinct().ToList();

        // Fetch audit logs with actions in bulk
        var auditLogs = await auditLogQuery
            .Where(al => auditLogIds.Contains(al.Id))
            .ToListAsync();

        var auditLogActions = await auditLogActionQuery
            .Where(ala => auditLogIds.Contains(ala.AuditLogId))
            .ToListAsync();

        // Create lookup dictionaries for performance
        var auditLogDict = auditLogs.ToDictionary(al => al.Id, al => al);
        var auditLogActionsDict = auditLogActions
            .GroupBy(ala => ala.AuditLogId)
            .ToDictionary(g => g.Key, g => g.ToList());

        // Map to DTOs
        var dtos = ObjectMapper.Map<List<EntityChange>, List<EntityChangeLogDto>>(entityChanges);

        // Enhance DTOs with audit log information
        foreach (var dto in dtos)
        {
            // Add audit log information
            if (auditLogDict.TryGetValue(dto.AuditLogId, out var auditLog))
            {
                dto.AuditLog = ObjectMapper.Map<AuditLog, AuditLogInfoDto>(auditLog);
                dto.UserName = auditLog.UserName; // Update UserName from audit log
            }

            // Add audit log actions
            if (auditLogActionsDict.TryGetValue(dto.AuditLogId, out var actions))
            {
                dto.AuditLogActions = ObjectMapper.Map<List<AuditLogAction>, List<AuditLogActionInfoDto>>(actions);
            }

            // Enhance property changes with reference information
            if (dto.PropertyChanges != null)
            {
                foreach (var propertyChange in dto.PropertyChanges)
                {
                    await EnhancePropertyChangeWithReferenceInfoAsync(propertyChange);
                }
            }
        }

        return new PagedResultDto<EntityChangeLogDto>(totalCount, dtos);
    }
}
