﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.HotelFrontOffice.Migrations
{
    /// <inheritdoc />
    public partial class TickerQSchemaUpdate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "ticker");

            migrationBuilder.RenameTable(
                name: "TimeTickers",
                newName: "TimeTickers",
                newSchema: "ticker");

            migrationBuilder.RenameTable(
                name: "CronTickers",
                newName: "CronTickers",
                newSchema: "ticker");

            migrationBuilder.RenameTable(
                name: "CronTickerOccurrences",
                newName: "CronTickerOccurrences",
                newSchema: "ticker");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameTable(
                name: "TimeTickers",
                schema: "ticker",
                newName: "TimeTickers");

            migrationBuilder.RenameTable(
                name: "CronTickers",
                schema: "ticker",
                newName: "CronTickers");

            migrationBuilder.RenameTable(
                name: "CronTickerOccurrences",
                schema: "ticker",
                newName: "CronTickerOccurrences");
        }
    }
}
