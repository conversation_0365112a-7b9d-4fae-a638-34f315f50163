using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using TickerQ;
using Volo.Abp.BlobStoring;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Uow;

namespace Imip.HotelFrontOffice.Attachments.Jobs
{
    /// <summary>
    /// Controller for deleting temporary ZIP files
    /// </summary>
    public class DeleteTemporaryZipFileTickerController
    {
        private readonly ILogger<DeleteTemporaryZipFileTickerController> _logger;
        private readonly IServiceProvider _serviceProvider;

        public DeleteTemporaryZipFileTickerController(
            ILogger<DeleteTemporaryZipFileTickerController> logger,
            IServiceProvider serviceProvider)
        {
            _logger = logger;
            _serviceProvider = serviceProvider;
        }

        /// <summary>
        /// Deletes a temporary ZIP file
        /// </summary>
        [UnitOfWork]
        public async Task DeleteTemporaryZipFile(Guid temporaryZipFileId, CancellationToken cancellationToken)
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var temporaryZipFileRepository = scope.ServiceProvider.GetRequiredService<ITemporaryZipFileRepository>();
                var blobContainerFactory = scope.ServiceProvider.GetRequiredService<IBlobContainerFactory>();
                var blobContainer = blobContainerFactory.Create("default");

                // Get the temporary ZIP file
                var temporaryZipFile = await temporaryZipFileRepository.FindAsync(temporaryZipFileId, cancellationToken: cancellationToken);
                if (temporaryZipFile == null)
                {
                    _logger.LogWarning("Temporary ZIP file with ID {Id} not found", temporaryZipFileId);
                    return;
                }

                // Delete the file from blob storage if BlobName is not null
                try
                {
                    if (!string.IsNullOrEmpty(temporaryZipFile.BlobName))
                    {
                        await blobContainer.DeleteAsync(temporaryZipFile.BlobName, cancellationToken);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error deleting temporary ZIP file {BlobName} from blob storage", temporaryZipFile.BlobName);
                    // Continue to mark as processed even if deletion fails
                }

                // Delete local files if Description contains file paths
                try
                {
                    if (!string.IsNullOrEmpty(temporaryZipFile.Description) && temporaryZipFile.Description.Contains('|'))
                    {
                        var filePaths = temporaryZipFile.Description.Split('|');
                        foreach (var filePath in filePaths)
                        {
                            if (File.Exists(filePath))
                            {
                                File.Delete(filePath);
                            }
                            else
                            {
                                _logger.LogWarning("Local file not found: {FilePath}", filePath);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error deleting local files for temporary file with ID {Id}", temporaryZipFile.Id);
                    // Continue to mark as processed even if deletion fails
                }

                // Mark the file as processed
                await temporaryZipFileRepository.MarkAsProcessedAsync(temporaryZipFile.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling DeleteTemporaryZipFile job for ID {Id}", temporaryZipFileId);
                throw; // Rethrow to allow TickerQ to handle retries
            }
        }
    }
}
