using System.Threading.Tasks;
using Imip.HotelFrontOffice.Permissions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Imip.HotelFrontOffice.Web.Authorization;

/// <summary>
/// A dynamic policy provider that creates policies on-the-fly for any policy name
/// </summary>
public class DynamicAuthorizationPolicyProvider : DefaultAuthorizationPolicyProvider
{
    private readonly ILogger<DynamicAuthorizationPolicyProvider> _logger;

    public DynamicAuthorizationPolicyProvider(
        IOptions<AuthorizationOptions> options,
        ILogger<DynamicAuthorizationPolicyProvider> logger)
        : base(options)
    {
        _logger = logger;
    }

    public override async Task<AuthorizationPolicy?> GetPolicyAsync(string policyName)
    {
        // Check if the policy exists in the options first (this is the default behavior)
        var policy = await base.GetPolicyAsync(policyName);

        // If the policy exists, return it
        if (policy != null)
        {
            return policy;
        }

        // Create a policy builder
        var policyBuilder = new AuthorizationPolicyBuilder();

        // Require authenticated user
        policyBuilder.RequireAuthenticatedUser();

        // Add a custom requirement that will be handled by our authorization handler
        policyBuilder.AddRequirements(new PermissionRequirement(policyName));

        // Build and return the policy
        return policyBuilder.Build();
    }
}
