﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.HotelFrontOffice.Migrations
{
    /// <inheritdoc />
    public partial class Add_Status_To_Reservation_Details : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AppReservationDetails_AppGuests_GuestId",
                table: "AppReservationDetails");

            migrationBuilder.DropForeignKey(
                name: "FK_AppReservationDetails_AppRoom_RoomId",
                table: "AppReservationDetails");

            migrationBuilder.AddColumn<Guid>(
                name: "StatusId",
                table: "AppReservationDetails",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "AppDocumentTemplates",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    DocumentType = table.Column<int>(type: "int", nullable: false),
                    AttachmentId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    IsDefault = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    ExtraProperties = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ConcurrencyStamp = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    DeleterId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AppDocumentTemplates", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationDetails_StatusId_RoomId_GuestId_ReservationId",
                table: "AppReservationDetails",
                columns: new[] { "StatusId", "RoomId", "GuestId", "ReservationId" });

            migrationBuilder.CreateIndex(
                name: "IX_AppDocumentTemplates_AttachmentId",
                table: "AppDocumentTemplates",
                column: "AttachmentId");

            migrationBuilder.CreateIndex(
                name: "IX_AppDocumentTemplates_DocumentType",
                table: "AppDocumentTemplates",
                column: "DocumentType");

            migrationBuilder.CreateIndex(
                name: "IX_AppDocumentTemplates_DocumentType_IsDefault",
                table: "AppDocumentTemplates",
                columns: new[] { "DocumentType", "IsDefault" });

            migrationBuilder.AddForeignKey(
                name: "FK_AppReservationDetails_AppGuests_GuestId",
                table: "AppReservationDetails",
                column: "GuestId",
                principalTable: "AppGuests",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_AppReservationDetails_AppRoom_RoomId",
                table: "AppReservationDetails",
                column: "RoomId",
                principalTable: "AppRoom",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AppReservationDetails_AppGuests_GuestId",
                table: "AppReservationDetails");

            migrationBuilder.DropForeignKey(
                name: "FK_AppReservationDetails_AppRoom_RoomId",
                table: "AppReservationDetails");

            migrationBuilder.DropTable(
                name: "AppDocumentTemplates");

            migrationBuilder.DropIndex(
                name: "IX_AppReservationDetails_StatusId_RoomId_GuestId_ReservationId",
                table: "AppReservationDetails");

            migrationBuilder.DropColumn(
                name: "StatusId",
                table: "AppReservationDetails");

            migrationBuilder.AddForeignKey(
                name: "FK_AppReservationDetails_AppGuests_GuestId",
                table: "AppReservationDetails",
                column: "GuestId",
                principalTable: "AppGuests",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_AppReservationDetails_AppRoom_RoomId",
                table: "AppReservationDetails",
                column: "RoomId",
                principalTable: "AppRoom",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
