﻿using System;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Permissions;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Validation;

namespace Imip.HotelFrontOffice.PaymentDetails;

[Authorize(WismaAppPermissions.PolicyPaymentDetails.Default)]
public class PaymentDetailsAppService : PermissionCheckedCrudAppService<
    PaymentDetail,
    PaymentDetailsDto,
    Guid,
    PagedAndSortedResultRequestDto,
    CreateUpdatePaymentDetailsDto,
    CreateUpdatePaymentDetailsDto
>, IPaymentDetailsAppService
{
    private readonly IRepository<PaymentDetail, Guid> _repository;

    public PaymentDetailsAppService(IRepository<PaymentDetail, Guid> repository,
        IPermissionChecker permissionChecker)
        : base(repository, permissionChecker)
    {
        _repository = repository;

        GetPolicyName = WismaAppPermissions.PolicyPaymentDetails.View;
        GetListPolicyName = WismaAppPermissions.PolicyPaymentDetails.View;
        CreatePolicyName = WismaAppPermissions.PolicyPaymentDetails.Create;
        UpdatePolicyName = WismaAppPermissions.PolicyPaymentDetails.Edit;
        DeletePolicyName = WismaAppPermissions.PolicyPaymentDetails.Delete;
    }

    public override async Task<PaymentDetailsDto> CreateAsync(CreateUpdatePaymentDetailsDto input)
    {
        await CheckCreatePolicyAsync();

        // Validate that the SourceType is a valid enum value
        // This check allows both enum values (1, 2, 3) and enum names ("ReservationRoom", etc.)
        if (!Enum.IsDefined(typeof(PaymentSourceType), input.SourceType))
        {
            // Try to convert numeric value to enum if it's not a valid enum directly
            // This handles cases where the client sends numeric values (1, 2, 3) instead of enum names
            int sourceTypeValue;
            bool isValidSourceType = false;

            // Check if the value is a valid integer that corresponds to an enum value
            if (int.TryParse(input.SourceType.ToString(), out sourceTypeValue))
            {
                isValidSourceType = Enum.IsDefined(typeof(PaymentSourceType), sourceTypeValue);
                if (isValidSourceType)
                {
                    // Convert the numeric value to the actual enum value
                    input.SourceType = (PaymentSourceType)sourceTypeValue;
                }
            }

            if (!isValidSourceType)
            {
                throw new AbpValidationException(
                    "Invalid source type. Must be one of the valid payment source types (1=ReservationRoom, 2=ReservationRoomFoodAndBeverage, 3=ReservationRoomService).",
                    new ValidationResult[]
                    {
                        new ValidationResult(
                            "Invalid source type. Must be one of the valid payment source types (1=ReservationRoom, 2=ReservationRoomFoodAndBeverage, 3=ReservationRoomService).",
                            new[] { nameof(input.SourceType) }
                        )
                    });
            }
        }

        var entity = await MapToEntityAsync(input);

        await Repository.InsertAsync(entity, autoSave: true);

        return await MapToGetOutputDtoAsync(entity);
    }

    public override async Task<PaymentDetailsDto> UpdateAsync(Guid id, CreateUpdatePaymentDetailsDto input)
    {
        await CheckUpdatePolicyAsync();

        // Validate that the SourceType is a valid enum value
        // This check allows both enum values (1, 2, 3) and enum names ("ReservationRoom", etc.)
        if (!Enum.IsDefined(typeof(PaymentSourceType), input.SourceType))
        {
            // Try to convert numeric value to enum if it's not a valid enum directly
            // This handles cases where the client sends numeric values (1, 2, 3) instead of enum names
            int sourceTypeValue;
            bool isValidSourceType = false;

            // Check if the value is a valid integer that corresponds to an enum value
            if (int.TryParse(input.SourceType.ToString(), out sourceTypeValue))
            {
                isValidSourceType = Enum.IsDefined(typeof(PaymentSourceType), sourceTypeValue);
                if (isValidSourceType)
                {
                    // Convert the numeric value to the actual enum value
                    input.SourceType = (PaymentSourceType)sourceTypeValue;
                }
            }

            if (!isValidSourceType)
            {
                throw new AbpValidationException(
                    "Invalid source type. Must be one of the valid payment source types (1=ReservationRoom, 2=ReservationRoomFoodAndBeverage, 3=ReservationRoomService).",
                    new ValidationResult[]
                    {
                        new ValidationResult(
                            "Invalid source type. Must be one of the valid payment source types (1=ReservationRoom, 2=ReservationRoomFoodAndBeverage, 3=ReservationRoomService).",
                            new[] { nameof(input.SourceType) }
                        )
                    });
            }
        }

        var entity = await GetEntityByIdAsync(id);

        await MapToEntityAsync(input, entity);

        await Repository.UpdateAsync(entity, autoSave: true);

        return await MapToGetOutputDtoAsync(entity);
    }
}