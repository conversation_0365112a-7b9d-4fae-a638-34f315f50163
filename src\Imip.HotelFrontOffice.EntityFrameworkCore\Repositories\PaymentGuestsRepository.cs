﻿using System;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.HotelFrontOffice.Repositories
{
    public class PaymentGuestsRepository : EfCoreRepository<HotelFrontOfficeDbContext, PaymentGuests.PaymentGuest, Guid>, IPaymentGuestsRepository
    {
        public PaymentGuestsRepository(IDbContextProvider<HotelFrontOfficeDbContext> dbContextProvider)
            : base(dbContextProvider)
        {
            
        }

        public async Task<PaymentGuests.PaymentGuest?> FindByNameAsync(string name)
        {
            var dbContext = await GetDbContextAsync();
            return await dbContext.PaymentGuests
                .Include(x => x.Payments)
                .FirstOrDefaultAsync();
        }
    }
}