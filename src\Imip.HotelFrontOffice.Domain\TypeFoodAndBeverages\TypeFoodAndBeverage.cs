﻿using System;
using System.Collections.Generic;
using Imip.HotelFrontOffice.FoodAndBeverages;
using Imip.HotelFrontOffice.MasterStatuses;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.HotelFrontOffice.TypeFoodAndBeverages;

public class TypeFoodAndBeverage : FullAuditedAggregateRoot<Guid>
{
    public string Name { get; set; } = default!;
    public Guid? StatusId { get; set; }

    public virtual MasterStatus? Status { get; set; }
    public virtual ICollection<FoodAndBeverage> FoodAndBeverages { get; set; }

    protected TypeFoodAndBeverage()
    {
        FoodAndBeverages = new HashSet<FoodAndBeverage>();
    }

    public TypeFoodAndBeverage(Guid id, string name, Guid? statusId) : this()
    {
        Id = id;
        Name = name;
        StatusId = statusId;
    }
}