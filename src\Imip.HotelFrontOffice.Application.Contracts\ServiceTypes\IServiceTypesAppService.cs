﻿using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Imip.HotelFrontOffice.ServiceTypes
{
    public interface IServiceTypesAppService : ICrudAppService<
        ServiceTypesDto,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdateServiceTypesDto,
        CreateUpdateServiceTypesDto
    >
    {
        new Task<ServiceTypesDto> CreateAsync(CreateUpdateServiceTypesDto input);
        new Task<ServiceTypesDto> UpdateAsync(Guid id, CreateUpdateServiceTypesDto input);
    }
}