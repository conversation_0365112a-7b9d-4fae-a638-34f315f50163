using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;

namespace Imip.HotelFrontOffice.Payments;

public class PaymentCodeGeneratorService : DomainService, IPaymentCodeGeneratorService
{
    private readonly IRepository<Payment, Guid> _paymentRepository;
    private readonly ILogger<PaymentCodeGeneratorService> _logger;

    public PaymentCodeGeneratorService(
        IRepository<Payment, Guid> paymentRepository,
        ILogger<PaymentCodeGeneratorService> logger)
    {
        _paymentRepository = paymentRepository;
        _logger = logger;
    }


    public async Task<string> GeneratePaymentCode()
    {
        // Format: IMIPYYYYMM001 (increments each month)
        var currentDate = DateTime.Now;
        var yearMonth = currentDate.ToString("yyyyMM");
        var prefix = $"IMIP{yearMonth}";

        // Get the last reservation code for the current month
        var lastReservationCode = await GetLastPaymentCodeForMonth(currentDate);

        // Extract the sequence number from the last code or start with 0
        int sequenceNumber = 0;
        if (!string.IsNullOrEmpty(lastReservationCode) && lastReservationCode.StartsWith(prefix))
        {
            // Extract the sequence part (after the prefix)
            var lastSequencePart = lastReservationCode[(prefix.Length)..];
            if (int.TryParse(lastSequencePart, out int lastSequence))
            {
                sequenceNumber = lastSequence;
            }
        }

        // Increment the sequence number
        sequenceNumber++;

        // Format the new code with a 3-digit sequence number
        return $"{prefix}{sequenceNumber:D3}";
    }

    private async Task<string?> GetLastPaymentCodeForMonth(DateTime date)
    {
        try
        {
            var yearMonth = date.ToString("yyyyMM");
            var prefix = $"IMIP{yearMonth}";

            // query to find the last payment code for current month
            var query = await _paymentRepository.GetQueryableAsync();
            var lastPayment = await query
                .Where(r => r.PaymentCode.StartsWith(prefix))
                .OrderByDescending(r => r.PaymentCode)
                .FirstOrDefaultAsync();

            return lastPayment?.PaymentCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting last reservation code: {Message}", ex.Message);
            return string.Empty;
        }
    }

}