﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.HotelFrontOffice.Migrations
{
    /// <inheritdoc />
    public partial class AddPerformanceIndexs : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_AppReservationDetails_ReservationId",
                table: "AppReservationDetails");

            migrationBuilder.DropIndex(
                name: "IX_AppReservationDetails_RoomId",
                table: "AppReservationDetails");

            migrationBuilder.AlterColumn<string>(
                name: "Code",
                table: "MasterStatus",
                type: "nvarchar(450)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Code",
                table: "AppRoomStatuses",
                type: "nvarchar(450)",
                nullable: false,
                defaultValue: "1",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldDefaultValue: "1");

            migrationBuilder.AddColumn<DateTime>(
                name: "TransactionDate",
                table: "AppReservationRooms",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "TransactionDate",
                table: "AppReservationFoodAndBeverages",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "CriteriaId",
                table: "AppReservationDetails",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "ReservationFoodAndBeveragesId",
                table: "AppPaymentDetails",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "ReservationRoomId",
                table: "AppPaymentDetails",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_MasterStatus_Code",
                table: "MasterStatus",
                column: "Code");

            migrationBuilder.CreateIndex(
                name: "IX_MasterStatus_CreationTime",
                table: "MasterStatus",
                column: "CreationTime");

            migrationBuilder.CreateIndex(
                name: "IX_MasterStatus_DocType_Code",
                table: "MasterStatus",
                columns: new[] { "DocType", "Code" });

            migrationBuilder.CreateIndex(
                name: "IX_MasterStatus_DocType_Name",
                table: "MasterStatus",
                columns: new[] { "DocType", "Name" });

            migrationBuilder.CreateIndex(
                name: "IX_AppTypeFoodAndBeverage_CreationTime",
                table: "AppTypeFoodAndBeverage",
                column: "CreationTime");

            migrationBuilder.CreateIndex(
                name: "IX_AppTypeFoodAndBeverage_StatusId_Name",
                table: "AppTypeFoodAndBeverage",
                columns: new[] { "StatusId", "Name" });

            migrationBuilder.CreateIndex(
                name: "IX_AppServiceTypes_CreationTime",
                table: "AppServiceTypes",
                column: "CreationTime");

            migrationBuilder.CreateIndex(
                name: "IX_AppServiceTypes_StatusId_Name",
                table: "AppServiceTypes",
                columns: new[] { "StatusId", "Name" });

            migrationBuilder.CreateIndex(
                name: "IX_AppServices_CreationTime",
                table: "AppServices",
                column: "CreationTime");

            migrationBuilder.CreateIndex(
                name: "IX_AppServices_Name_ServiceTypeId",
                table: "AppServices",
                columns: new[] { "Name", "ServiceTypeId" });

            migrationBuilder.CreateIndex(
                name: "IX_AppServices_Price",
                table: "AppServices",
                column: "Price");

            migrationBuilder.CreateIndex(
                name: "IX_AppServices_ServiceTypeId_Price",
                table: "AppServices",
                columns: new[] { "ServiceTypeId", "Price" });

            migrationBuilder.CreateIndex(
                name: "IX_AppServices_UsageTime",
                table: "AppServices",
                column: "UsageTime");

            migrationBuilder.CreateIndex(
                name: "IX_AppRoomTypes_Alias",
                table: "AppRoomTypes",
                column: "Alias");

            migrationBuilder.CreateIndex(
                name: "IX_AppRoomTypes_Alias_StatusId",
                table: "AppRoomTypes",
                columns: new[] { "Alias", "StatusId" });

            migrationBuilder.CreateIndex(
                name: "IX_AppRoomTypes_CreationTime",
                table: "AppRoomTypes",
                column: "CreationTime");

            migrationBuilder.CreateIndex(
                name: "IX_AppRoomTypes_StatusId_Name",
                table: "AppRoomTypes",
                columns: new[] { "StatusId", "Name" });

            migrationBuilder.CreateIndex(
                name: "IX_AppRoomStatuses_Code",
                table: "AppRoomStatuses",
                column: "Code");

            migrationBuilder.CreateIndex(
                name: "IX_AppRoomStatuses_Code_Name",
                table: "AppRoomStatuses",
                columns: new[] { "Code", "Name" });

            migrationBuilder.CreateIndex(
                name: "IX_AppRoomStatuses_Color",
                table: "AppRoomStatuses",
                column: "Color");

            migrationBuilder.CreateIndex(
                name: "IX_AppRoomStatuses_CreationTime",
                table: "AppRoomStatuses",
                column: "CreationTime");

            migrationBuilder.CreateIndex(
                name: "IX_AppRoom_Price",
                table: "AppRoom",
                column: "Price");

            migrationBuilder.CreateIndex(
                name: "IX_AppRoom_RoomCode",
                table: "AppRoom",
                column: "RoomCode");

            migrationBuilder.CreateIndex(
                name: "IX_AppRoom_RoomNumber",
                table: "AppRoom",
                column: "RoomNumber");

            migrationBuilder.CreateIndex(
                name: "IX_AppRoom_RoomStatusId_RoomTypeId",
                table: "AppRoom",
                columns: new[] { "RoomStatusId", "RoomTypeId" });

            migrationBuilder.CreateIndex(
                name: "IX_AppRoom_RoomTypeId_Price",
                table: "AppRoom",
                columns: new[] { "RoomTypeId", "Price" });

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationTypes_CreationTime",
                table: "AppReservationTypes",
                column: "CreationTime");

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationTypes_StatusId_Name",
                table: "AppReservationTypes",
                columns: new[] { "StatusId", "Name" });

            migrationBuilder.CreateIndex(
                name: "IX_AppReservations_ArrivalDate",
                table: "AppReservations",
                column: "ArrivalDate");

            migrationBuilder.CreateIndex(
                name: "IX_AppReservations_ArrivalDate_StatusId",
                table: "AppReservations",
                columns: new[] { "ArrivalDate", "StatusId" });

            migrationBuilder.CreateIndex(
                name: "IX_AppReservations_BookerEmail",
                table: "AppReservations",
                column: "BookerEmail");

            migrationBuilder.CreateIndex(
                name: "IX_AppReservations_BookerName",
                table: "AppReservations",
                column: "BookerName");

            migrationBuilder.CreateIndex(
                name: "IX_AppReservations_BookerPhoneNumber",
                table: "AppReservations",
                column: "BookerPhoneNumber");

            migrationBuilder.CreateIndex(
                name: "IX_AppReservations_CompanyId_StatusId",
                table: "AppReservations",
                columns: new[] { "CompanyId", "StatusId" });

            migrationBuilder.CreateIndex(
                name: "IX_AppReservations_CreationTime",
                table: "AppReservations",
                column: "CreationTime");

            migrationBuilder.CreateIndex(
                name: "IX_AppReservations_GroupCode",
                table: "AppReservations",
                column: "GroupCode");

            migrationBuilder.CreateIndex(
                name: "IX_AppReservations_ReservationTypeId_StatusId",
                table: "AppReservations",
                columns: new[] { "ReservationTypeId", "StatusId" });

            migrationBuilder.CreateIndex(
                name: "IX_AppReservations_StatusId",
                table: "AppReservations",
                column: "StatusId");

            migrationBuilder.CreateIndex(
                name: "IX_AppReservations_StatusId_ArrivalDate",
                table: "AppReservations",
                columns: new[] { "StatusId", "ArrivalDate" });

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationRooms_ReservationDetailsId_PaymentStatusId",
                table: "AppReservationRooms",
                columns: new[] { "ReservationDetailsId", "PaymentStatusId" });

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationRooms_ServiceId_PaymentStatusId",
                table: "AppReservationRooms",
                columns: new[] { "ServiceId", "PaymentStatusId" });

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationRooms_TotalPrice",
                table: "AppReservationRooms",
                column: "TotalPrice");

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationFoodAndBeverages_FoodAndBeverageId_PaymentStatusId",
                table: "AppReservationFoodAndBeverages",
                columns: new[] { "FoodAndBeverageId", "PaymentStatusId" });

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationFoodAndBeverages_ReservationDetailsId_PaymentStatusId",
                table: "AppReservationFoodAndBeverages",
                columns: new[] { "ReservationDetailsId", "PaymentStatusId" });

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationFoodAndBeverages_TotalPrice",
                table: "AppReservationFoodAndBeverages",
                column: "TotalPrice");

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationDetails_CheckInDate",
                table: "AppReservationDetails",
                column: "CheckInDate");

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationDetails_CheckInDate_CheckOutDate",
                table: "AppReservationDetails",
                columns: new[] { "CheckInDate", "CheckOutDate" });

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationDetails_CheckOutDate",
                table: "AppReservationDetails",
                column: "CheckOutDate");

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationDetails_CreationTime",
                table: "AppReservationDetails",
                column: "CreationTime");

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationDetails_CriteriaId",
                table: "AppReservationDetails",
                column: "CriteriaId");

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationDetails_CriteriaId_StatusId",
                table: "AppReservationDetails",
                columns: new[] { "CriteriaId", "StatusId" });

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationDetails_GuestId_StatusId",
                table: "AppReservationDetails",
                columns: new[] { "GuestId", "StatusId" });

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationDetails_PaymentStatusId_CheckInDate",
                table: "AppReservationDetails",
                columns: new[] { "PaymentStatusId", "CheckInDate" });

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationDetails_PaymentStatusId_CreationTime",
                table: "AppReservationDetails",
                columns: new[] { "PaymentStatusId", "CreationTime" });

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationDetails_Price",
                table: "AppReservationDetails",
                column: "Price");

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationDetails_ReservationId_GuestId",
                table: "AppReservationDetails",
                columns: new[] { "ReservationId", "GuestId" });

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationDetails_ReservationId_StatusId",
                table: "AppReservationDetails",
                columns: new[] { "ReservationId", "StatusId" });

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationDetails_Rfid",
                table: "AppReservationDetails",
                column: "Rfid");

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationDetails_RoomId_CheckInDate_CheckOutDate",
                table: "AppReservationDetails",
                columns: new[] { "RoomId", "CheckInDate", "CheckOutDate" });

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationDetails_RoomId_CreationTime",
                table: "AppReservationDetails",
                columns: new[] { "RoomId", "CreationTime" });

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationDetails_RoomId_StatusId",
                table: "AppReservationDetails",
                columns: new[] { "RoomId", "StatusId" });

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationDetails_StatusId_CheckInDate_CheckOutDate",
                table: "AppReservationDetails",
                columns: new[] { "StatusId", "CheckInDate", "CheckOutDate" });

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationDetails_StatusId_CreationTime",
                table: "AppReservationDetails",
                columns: new[] { "StatusId", "CreationTime" });

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationDetails_StatusId_PaymentStatusId",
                table: "AppReservationDetails",
                columns: new[] { "StatusId", "PaymentStatusId" });

            migrationBuilder.CreateIndex(
                name: "IX_AppPayments_CreationTime",
                table: "AppPayments",
                column: "CreationTime");

            migrationBuilder.CreateIndex(
                name: "IX_AppPayments_GrantTotal",
                table: "AppPayments",
                column: "GrantTotal");

            migrationBuilder.CreateIndex(
                name: "IX_AppPayments_PaidAmount",
                table: "AppPayments",
                column: "PaidAmount");

            migrationBuilder.CreateIndex(
                name: "IX_AppPayments_PaymentCode",
                table: "AppPayments",
                column: "PaymentCode");

            migrationBuilder.CreateIndex(
                name: "IX_AppPayments_PaymentMethodId_StatusId",
                table: "AppPayments",
                columns: new[] { "PaymentMethodId", "StatusId" });

            migrationBuilder.CreateIndex(
                name: "IX_AppPayments_ReservationsId",
                table: "AppPayments",
                column: "ReservationsId");

            migrationBuilder.CreateIndex(
                name: "IX_AppPayments_ReservationsId_PaymentMethodId",
                table: "AppPayments",
                columns: new[] { "ReservationsId", "PaymentMethodId" });

            migrationBuilder.CreateIndex(
                name: "IX_AppPayments_ReservationsId_TransactionDate",
                table: "AppPayments",
                columns: new[] { "ReservationsId", "TransactionDate" });

            migrationBuilder.CreateIndex(
                name: "IX_AppPayments_StatusId_CreationTime",
                table: "AppPayments",
                columns: new[] { "StatusId", "CreationTime" });

            migrationBuilder.CreateIndex(
                name: "IX_AppPayments_StatusId_TransactionDate",
                table: "AppPayments",
                columns: new[] { "StatusId", "TransactionDate" });

            migrationBuilder.CreateIndex(
                name: "IX_AppPayments_TaxId",
                table: "AppPayments",
                column: "TaxId");

            migrationBuilder.CreateIndex(
                name: "IX_AppPayments_TotalAmount",
                table: "AppPayments",
                column: "TotalAmount");

            migrationBuilder.CreateIndex(
                name: "IX_AppPayments_TransactionDate",
                table: "AppPayments",
                column: "TransactionDate");

            migrationBuilder.CreateIndex(
                name: "IX_AppPayments_TransactionDate_StatusId",
                table: "AppPayments",
                columns: new[] { "TransactionDate", "StatusId" });

            migrationBuilder.CreateIndex(
                name: "IX_AppPaymentGuests_AmountPaid",
                table: "AppPaymentGuests",
                column: "AmountPaid");

            migrationBuilder.CreateIndex(
                name: "IX_AppPaymentGuests_CreationTime",
                table: "AppPaymentGuests",
                column: "CreationTime");

            migrationBuilder.CreateIndex(
                name: "IX_AppPaymentGuests_PaymentId",
                table: "AppPaymentGuests",
                column: "PaymentId");

            migrationBuilder.CreateIndex(
                name: "IX_AppPaymentDetails_Amount",
                table: "AppPaymentDetails",
                column: "Amount");

            migrationBuilder.CreateIndex(
                name: "IX_AppPaymentDetails_CreationTime",
                table: "AppPaymentDetails",
                column: "CreationTime");

            migrationBuilder.CreateIndex(
                name: "IX_AppPaymentDetails_PaymentId_ReservationDetailsId",
                table: "AppPaymentDetails",
                columns: new[] { "PaymentId", "ReservationDetailsId" });

            migrationBuilder.CreateIndex(
                name: "IX_AppPaymentDetails_PaymentId_SourceType",
                table: "AppPaymentDetails",
                columns: new[] { "PaymentId", "SourceType" });

            migrationBuilder.CreateIndex(
                name: "IX_AppPaymentDetails_Qty",
                table: "AppPaymentDetails",
                column: "Qty");

            migrationBuilder.CreateIndex(
                name: "IX_AppPaymentDetails_ReservationDetailsId_SourceType",
                table: "AppPaymentDetails",
                columns: new[] { "ReservationDetailsId", "SourceType" });

            migrationBuilder.CreateIndex(
                name: "IX_AppPaymentDetails_ReservationFoodAndBeveragesId",
                table: "AppPaymentDetails",
                column: "ReservationFoodAndBeveragesId");

            migrationBuilder.CreateIndex(
                name: "IX_AppPaymentDetails_ReservationRoomId",
                table: "AppPaymentDetails",
                column: "ReservationRoomId");

            migrationBuilder.CreateIndex(
                name: "IX_AppPaymentDetails_SourceId",
                table: "AppPaymentDetails",
                column: "SourceId");

            migrationBuilder.CreateIndex(
                name: "IX_AppPaymentDetails_SourceType",
                table: "AppPaymentDetails",
                column: "SourceType");

            migrationBuilder.CreateIndex(
                name: "IX_AppPaymentDetails_SourceType_Amount",
                table: "AppPaymentDetails",
                columns: new[] { "SourceType", "Amount" });

            migrationBuilder.CreateIndex(
                name: "IX_AppPaymentDetails_TaxId",
                table: "AppPaymentDetails",
                column: "TaxId");

            migrationBuilder.CreateIndex(
                name: "IX_AppPaymentDetails_TaxId_SourceType",
                table: "AppPaymentDetails",
                columns: new[] { "TaxId", "SourceType" });

            migrationBuilder.CreateIndex(
                name: "IX_AppPaymentDetails_UnitPrice",
                table: "AppPaymentDetails",
                column: "UnitPrice");

            migrationBuilder.CreateIndex(
                name: "IX_AppPaymentDetails_VatAmount",
                table: "AppPaymentDetails",
                column: "VatAmount");

            migrationBuilder.CreateIndex(
                name: "IX_AppGuests_CompanyName",
                table: "AppGuests",
                column: "CompanyName");

            migrationBuilder.CreateIndex(
                name: "IX_AppGuests_CreationTime",
                table: "AppGuests",
                column: "CreationTime");

            migrationBuilder.CreateIndex(
                name: "IX_AppGuests_Fullname",
                table: "AppGuests",
                column: "Fullname");

            migrationBuilder.CreateIndex(
                name: "IX_AppGuests_Nationality",
                table: "AppGuests",
                column: "Nationality");

            migrationBuilder.CreateIndex(
                name: "IX_AppGuests_PhoneNumber",
                table: "AppGuests",
                column: "PhoneNumber");

            migrationBuilder.CreateIndex(
                name: "IX_AppFoodAndBeverage_CreationTime",
                table: "AppFoodAndBeverage",
                column: "CreationTime");

            migrationBuilder.CreateIndex(
                name: "IX_AppFoodAndBeverage_Name_TypeFoodAndBeverageId",
                table: "AppFoodAndBeverage",
                columns: new[] { "Name", "TypeFoodAndBeverageId" });

            migrationBuilder.CreateIndex(
                name: "IX_AppFoodAndBeverage_Price",
                table: "AppFoodAndBeverage",
                column: "Price");

            migrationBuilder.CreateIndex(
                name: "IX_AppFoodAndBeverage_TypeFoodAndBeverageId_Price",
                table: "AppFoodAndBeverage",
                columns: new[] { "TypeFoodAndBeverageId", "Price" });

            migrationBuilder.AddForeignKey(
                name: "FK_AppPaymentDetails_AppReservationFoodAndBeverages_ReservationFoodAndBeveragesId",
                table: "AppPaymentDetails",
                column: "ReservationFoodAndBeveragesId",
                principalTable: "AppReservationFoodAndBeverages",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AppPaymentDetails_AppReservationRooms_ReservationRoomId",
                table: "AppPaymentDetails",
                column: "ReservationRoomId",
                principalTable: "AppReservationRooms",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AppReservationDetails_MasterStatus_CriteriaId",
                table: "AppReservationDetails",
                column: "CriteriaId",
                principalTable: "MasterStatus",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AppPaymentDetails_AppReservationFoodAndBeverages_ReservationFoodAndBeveragesId",
                table: "AppPaymentDetails");

            migrationBuilder.DropForeignKey(
                name: "FK_AppPaymentDetails_AppReservationRooms_ReservationRoomId",
                table: "AppPaymentDetails");

            migrationBuilder.DropForeignKey(
                name: "FK_AppReservationDetails_MasterStatus_CriteriaId",
                table: "AppReservationDetails");

            migrationBuilder.DropIndex(
                name: "IX_MasterStatus_Code",
                table: "MasterStatus");

            migrationBuilder.DropIndex(
                name: "IX_MasterStatus_CreationTime",
                table: "MasterStatus");

            migrationBuilder.DropIndex(
                name: "IX_MasterStatus_DocType_Code",
                table: "MasterStatus");

            migrationBuilder.DropIndex(
                name: "IX_MasterStatus_DocType_Name",
                table: "MasterStatus");

            migrationBuilder.DropIndex(
                name: "IX_AppTypeFoodAndBeverage_CreationTime",
                table: "AppTypeFoodAndBeverage");

            migrationBuilder.DropIndex(
                name: "IX_AppTypeFoodAndBeverage_StatusId_Name",
                table: "AppTypeFoodAndBeverage");

            migrationBuilder.DropIndex(
                name: "IX_AppServiceTypes_CreationTime",
                table: "AppServiceTypes");

            migrationBuilder.DropIndex(
                name: "IX_AppServiceTypes_StatusId_Name",
                table: "AppServiceTypes");

            migrationBuilder.DropIndex(
                name: "IX_AppServices_CreationTime",
                table: "AppServices");

            migrationBuilder.DropIndex(
                name: "IX_AppServices_Name_ServiceTypeId",
                table: "AppServices");

            migrationBuilder.DropIndex(
                name: "IX_AppServices_Price",
                table: "AppServices");

            migrationBuilder.DropIndex(
                name: "IX_AppServices_ServiceTypeId_Price",
                table: "AppServices");

            migrationBuilder.DropIndex(
                name: "IX_AppServices_UsageTime",
                table: "AppServices");

            migrationBuilder.DropIndex(
                name: "IX_AppRoomTypes_Alias",
                table: "AppRoomTypes");

            migrationBuilder.DropIndex(
                name: "IX_AppRoomTypes_Alias_StatusId",
                table: "AppRoomTypes");

            migrationBuilder.DropIndex(
                name: "IX_AppRoomTypes_CreationTime",
                table: "AppRoomTypes");

            migrationBuilder.DropIndex(
                name: "IX_AppRoomTypes_StatusId_Name",
                table: "AppRoomTypes");

            migrationBuilder.DropIndex(
                name: "IX_AppRoomStatuses_Code",
                table: "AppRoomStatuses");

            migrationBuilder.DropIndex(
                name: "IX_AppRoomStatuses_Code_Name",
                table: "AppRoomStatuses");

            migrationBuilder.DropIndex(
                name: "IX_AppRoomStatuses_Color",
                table: "AppRoomStatuses");

            migrationBuilder.DropIndex(
                name: "IX_AppRoomStatuses_CreationTime",
                table: "AppRoomStatuses");

            migrationBuilder.DropIndex(
                name: "IX_AppRoom_Price",
                table: "AppRoom");

            migrationBuilder.DropIndex(
                name: "IX_AppRoom_RoomCode",
                table: "AppRoom");

            migrationBuilder.DropIndex(
                name: "IX_AppRoom_RoomNumber",
                table: "AppRoom");

            migrationBuilder.DropIndex(
                name: "IX_AppRoom_RoomStatusId_RoomTypeId",
                table: "AppRoom");

            migrationBuilder.DropIndex(
                name: "IX_AppRoom_RoomTypeId_Price",
                table: "AppRoom");

            migrationBuilder.DropIndex(
                name: "IX_AppReservationTypes_CreationTime",
                table: "AppReservationTypes");

            migrationBuilder.DropIndex(
                name: "IX_AppReservationTypes_StatusId_Name",
                table: "AppReservationTypes");

            migrationBuilder.DropIndex(
                name: "IX_AppReservations_ArrivalDate",
                table: "AppReservations");

            migrationBuilder.DropIndex(
                name: "IX_AppReservations_ArrivalDate_StatusId",
                table: "AppReservations");

            migrationBuilder.DropIndex(
                name: "IX_AppReservations_BookerEmail",
                table: "AppReservations");

            migrationBuilder.DropIndex(
                name: "IX_AppReservations_BookerName",
                table: "AppReservations");

            migrationBuilder.DropIndex(
                name: "IX_AppReservations_BookerPhoneNumber",
                table: "AppReservations");

            migrationBuilder.DropIndex(
                name: "IX_AppReservations_CompanyId_StatusId",
                table: "AppReservations");

            migrationBuilder.DropIndex(
                name: "IX_AppReservations_CreationTime",
                table: "AppReservations");

            migrationBuilder.DropIndex(
                name: "IX_AppReservations_GroupCode",
                table: "AppReservations");

            migrationBuilder.DropIndex(
                name: "IX_AppReservations_ReservationTypeId_StatusId",
                table: "AppReservations");

            migrationBuilder.DropIndex(
                name: "IX_AppReservations_StatusId",
                table: "AppReservations");

            migrationBuilder.DropIndex(
                name: "IX_AppReservations_StatusId_ArrivalDate",
                table: "AppReservations");

            migrationBuilder.DropIndex(
                name: "IX_AppReservationRooms_ReservationDetailsId_PaymentStatusId",
                table: "AppReservationRooms");

            migrationBuilder.DropIndex(
                name: "IX_AppReservationRooms_ServiceId_PaymentStatusId",
                table: "AppReservationRooms");

            migrationBuilder.DropIndex(
                name: "IX_AppReservationRooms_TotalPrice",
                table: "AppReservationRooms");

            migrationBuilder.DropIndex(
                name: "IX_AppReservationFoodAndBeverages_FoodAndBeverageId_PaymentStatusId",
                table: "AppReservationFoodAndBeverages");

            migrationBuilder.DropIndex(
                name: "IX_AppReservationFoodAndBeverages_ReservationDetailsId_PaymentStatusId",
                table: "AppReservationFoodAndBeverages");

            migrationBuilder.DropIndex(
                name: "IX_AppReservationFoodAndBeverages_TotalPrice",
                table: "AppReservationFoodAndBeverages");

            migrationBuilder.DropIndex(
                name: "IX_AppReservationDetails_CheckInDate",
                table: "AppReservationDetails");

            migrationBuilder.DropIndex(
                name: "IX_AppReservationDetails_CheckInDate_CheckOutDate",
                table: "AppReservationDetails");

            migrationBuilder.DropIndex(
                name: "IX_AppReservationDetails_CheckOutDate",
                table: "AppReservationDetails");

            migrationBuilder.DropIndex(
                name: "IX_AppReservationDetails_CreationTime",
                table: "AppReservationDetails");

            migrationBuilder.DropIndex(
                name: "IX_AppReservationDetails_CriteriaId",
                table: "AppReservationDetails");

            migrationBuilder.DropIndex(
                name: "IX_AppReservationDetails_CriteriaId_StatusId",
                table: "AppReservationDetails");

            migrationBuilder.DropIndex(
                name: "IX_AppReservationDetails_GuestId_StatusId",
                table: "AppReservationDetails");

            migrationBuilder.DropIndex(
                name: "IX_AppReservationDetails_PaymentStatusId_CheckInDate",
                table: "AppReservationDetails");

            migrationBuilder.DropIndex(
                name: "IX_AppReservationDetails_PaymentStatusId_CreationTime",
                table: "AppReservationDetails");

            migrationBuilder.DropIndex(
                name: "IX_AppReservationDetails_Price",
                table: "AppReservationDetails");

            migrationBuilder.DropIndex(
                name: "IX_AppReservationDetails_ReservationId_GuestId",
                table: "AppReservationDetails");

            migrationBuilder.DropIndex(
                name: "IX_AppReservationDetails_ReservationId_StatusId",
                table: "AppReservationDetails");

            migrationBuilder.DropIndex(
                name: "IX_AppReservationDetails_Rfid",
                table: "AppReservationDetails");

            migrationBuilder.DropIndex(
                name: "IX_AppReservationDetails_RoomId_CheckInDate_CheckOutDate",
                table: "AppReservationDetails");

            migrationBuilder.DropIndex(
                name: "IX_AppReservationDetails_RoomId_CreationTime",
                table: "AppReservationDetails");

            migrationBuilder.DropIndex(
                name: "IX_AppReservationDetails_RoomId_StatusId",
                table: "AppReservationDetails");

            migrationBuilder.DropIndex(
                name: "IX_AppReservationDetails_StatusId_CheckInDate_CheckOutDate",
                table: "AppReservationDetails");

            migrationBuilder.DropIndex(
                name: "IX_AppReservationDetails_StatusId_CreationTime",
                table: "AppReservationDetails");

            migrationBuilder.DropIndex(
                name: "IX_AppReservationDetails_StatusId_PaymentStatusId",
                table: "AppReservationDetails");

            migrationBuilder.DropIndex(
                name: "IX_AppPayments_CreationTime",
                table: "AppPayments");

            migrationBuilder.DropIndex(
                name: "IX_AppPayments_GrantTotal",
                table: "AppPayments");

            migrationBuilder.DropIndex(
                name: "IX_AppPayments_PaidAmount",
                table: "AppPayments");

            migrationBuilder.DropIndex(
                name: "IX_AppPayments_PaymentCode",
                table: "AppPayments");

            migrationBuilder.DropIndex(
                name: "IX_AppPayments_PaymentMethodId_StatusId",
                table: "AppPayments");

            migrationBuilder.DropIndex(
                name: "IX_AppPayments_ReservationsId",
                table: "AppPayments");

            migrationBuilder.DropIndex(
                name: "IX_AppPayments_ReservationsId_PaymentMethodId",
                table: "AppPayments");

            migrationBuilder.DropIndex(
                name: "IX_AppPayments_ReservationsId_TransactionDate",
                table: "AppPayments");

            migrationBuilder.DropIndex(
                name: "IX_AppPayments_StatusId_CreationTime",
                table: "AppPayments");

            migrationBuilder.DropIndex(
                name: "IX_AppPayments_StatusId_TransactionDate",
                table: "AppPayments");

            migrationBuilder.DropIndex(
                name: "IX_AppPayments_TaxId",
                table: "AppPayments");

            migrationBuilder.DropIndex(
                name: "IX_AppPayments_TotalAmount",
                table: "AppPayments");

            migrationBuilder.DropIndex(
                name: "IX_AppPayments_TransactionDate",
                table: "AppPayments");

            migrationBuilder.DropIndex(
                name: "IX_AppPayments_TransactionDate_StatusId",
                table: "AppPayments");

            migrationBuilder.DropIndex(
                name: "IX_AppPaymentGuests_AmountPaid",
                table: "AppPaymentGuests");

            migrationBuilder.DropIndex(
                name: "IX_AppPaymentGuests_CreationTime",
                table: "AppPaymentGuests");

            migrationBuilder.DropIndex(
                name: "IX_AppPaymentGuests_PaymentId",
                table: "AppPaymentGuests");

            migrationBuilder.DropIndex(
                name: "IX_AppPaymentDetails_Amount",
                table: "AppPaymentDetails");

            migrationBuilder.DropIndex(
                name: "IX_AppPaymentDetails_CreationTime",
                table: "AppPaymentDetails");

            migrationBuilder.DropIndex(
                name: "IX_AppPaymentDetails_PaymentId_ReservationDetailsId",
                table: "AppPaymentDetails");

            migrationBuilder.DropIndex(
                name: "IX_AppPaymentDetails_PaymentId_SourceType",
                table: "AppPaymentDetails");

            migrationBuilder.DropIndex(
                name: "IX_AppPaymentDetails_Qty",
                table: "AppPaymentDetails");

            migrationBuilder.DropIndex(
                name: "IX_AppPaymentDetails_ReservationDetailsId_SourceType",
                table: "AppPaymentDetails");

            migrationBuilder.DropIndex(
                name: "IX_AppPaymentDetails_ReservationFoodAndBeveragesId",
                table: "AppPaymentDetails");

            migrationBuilder.DropIndex(
                name: "IX_AppPaymentDetails_ReservationRoomId",
                table: "AppPaymentDetails");

            migrationBuilder.DropIndex(
                name: "IX_AppPaymentDetails_SourceId",
                table: "AppPaymentDetails");

            migrationBuilder.DropIndex(
                name: "IX_AppPaymentDetails_SourceType",
                table: "AppPaymentDetails");

            migrationBuilder.DropIndex(
                name: "IX_AppPaymentDetails_SourceType_Amount",
                table: "AppPaymentDetails");

            migrationBuilder.DropIndex(
                name: "IX_AppPaymentDetails_TaxId",
                table: "AppPaymentDetails");

            migrationBuilder.DropIndex(
                name: "IX_AppPaymentDetails_TaxId_SourceType",
                table: "AppPaymentDetails");

            migrationBuilder.DropIndex(
                name: "IX_AppPaymentDetails_UnitPrice",
                table: "AppPaymentDetails");

            migrationBuilder.DropIndex(
                name: "IX_AppPaymentDetails_VatAmount",
                table: "AppPaymentDetails");

            migrationBuilder.DropIndex(
                name: "IX_AppGuests_CompanyName",
                table: "AppGuests");

            migrationBuilder.DropIndex(
                name: "IX_AppGuests_CreationTime",
                table: "AppGuests");

            migrationBuilder.DropIndex(
                name: "IX_AppGuests_Fullname",
                table: "AppGuests");

            migrationBuilder.DropIndex(
                name: "IX_AppGuests_Nationality",
                table: "AppGuests");

            migrationBuilder.DropIndex(
                name: "IX_AppGuests_PhoneNumber",
                table: "AppGuests");

            migrationBuilder.DropIndex(
                name: "IX_AppFoodAndBeverage_CreationTime",
                table: "AppFoodAndBeverage");

            migrationBuilder.DropIndex(
                name: "IX_AppFoodAndBeverage_Name_TypeFoodAndBeverageId",
                table: "AppFoodAndBeverage");

            migrationBuilder.DropIndex(
                name: "IX_AppFoodAndBeverage_Price",
                table: "AppFoodAndBeverage");

            migrationBuilder.DropIndex(
                name: "IX_AppFoodAndBeverage_TypeFoodAndBeverageId_Price",
                table: "AppFoodAndBeverage");

            migrationBuilder.DropColumn(
                name: "TransactionDate",
                table: "AppReservationRooms");

            migrationBuilder.DropColumn(
                name: "TransactionDate",
                table: "AppReservationFoodAndBeverages");

            migrationBuilder.DropColumn(
                name: "CriteriaId",
                table: "AppReservationDetails");

            migrationBuilder.DropColumn(
                name: "ReservationFoodAndBeveragesId",
                table: "AppPaymentDetails");

            migrationBuilder.DropColumn(
                name: "ReservationRoomId",
                table: "AppPaymentDetails");

            migrationBuilder.AlterColumn<string>(
                name: "Code",
                table: "MasterStatus",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(450)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Code",
                table: "AppRoomStatuses",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "1",
                oldClrType: typeof(string),
                oldType: "nvarchar(450)",
                oldDefaultValue: "1");

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationDetails_ReservationId",
                table: "AppReservationDetails",
                column: "ReservationId");

            migrationBuilder.CreateIndex(
                name: "IX_AppReservationDetails_RoomId",
                table: "AppReservationDetails",
                column: "RoomId");
        }
    }
}
