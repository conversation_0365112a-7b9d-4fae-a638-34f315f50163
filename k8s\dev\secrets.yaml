﻿apiVersion: v1
kind: Secret
metadata:
  name: imip-wisma-secrets
  namespace: imip-wisma-dev-new
type: Opaque
stringData:
  ConnectionStrings__Default: "${DEV_DB_CONNECTION}"
  Seq__ApiKey: "${SEQ_API_KEY}"
  AuthServer__CertificatePassPhrase: "${CERT_PASSPHRASE}"
  StringEncryption__DefaultPassPhrase: "${ENCRYPTION_PASSPHRASE}"
  AuthServer__ClientId: "${DEV_AUTH_CLIENT_ID}"
  AuthServer__ClientSecret: "${DEV_AUTH_CLIENT_SECRET}"
