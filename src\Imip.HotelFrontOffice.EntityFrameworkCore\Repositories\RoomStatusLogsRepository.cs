﻿using System;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.HotelFrontOffice.Repositories
{
    public class RoomStatusLogsRepository : EfCoreRepository<HotelFrontOfficeDbContext, RoomStatusLogs.RoomStatusLog, Guid>, IRoomStatusLogsRepository
    {
        public RoomStatusLogsRepository(IDbContextProvider<HotelFrontOfficeDbContext> dbContextProvider)
            : base(dbContextProvider)
        {
        }

        public async Task<RoomStatusLogs.RoomStatusLog?> FindByNameAsync(string name)
        {
            var dbContext = await GetDbContextAsync();
            return await dbContext.RoomStatusLogs
                .Include(x => x.Room)
                .Include(x => x.RoomStatus)
                .FirstOrDefaultAsync();
        }
    }
}