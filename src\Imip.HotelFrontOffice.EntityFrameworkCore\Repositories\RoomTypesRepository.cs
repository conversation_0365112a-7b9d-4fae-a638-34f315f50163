﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.HotelFrontOffice.Repositories
{
    public class RoomTypesRepository : EfCoreRepository<HotelFrontOfficeDbContext, RoomTypes.RoomType, Guid>, IRoomTypesRepository
    {
        public RoomTypesRepository(IDbContextProvider<HotelFrontOfficeDbContext> dbContextProvider)
            : base(dbContextProvider)
        {
        }

        public async Task<List<RoomTypes.RoomType>> GetActiveAsync() 
        {
            var dbContext = await GetDbContextAsync();
            return await dbContext.RoomTypes
                .ToListAsync(); 
        }

        public async Task<RoomTypes.RoomType?> FindByNameAsync(string name)
        {
            var dbContext = await GetDbContextAsync();
            return await dbContext.RoomTypes
                .Where(x => x.Name == name)
                .FirstOrDefaultAsync(); 
        }
    }
}