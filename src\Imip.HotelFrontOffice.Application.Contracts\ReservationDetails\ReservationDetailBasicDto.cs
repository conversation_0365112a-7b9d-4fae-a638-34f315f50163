﻿using System;
using Imip.HotelFrontOffice.Guests;
using Imip.HotelFrontOffice.MasterStatuses;
using Imip.HotelFrontOffice.Reservations;
using Imip.HotelFrontOffice.Rooms;
using Volo.Abp.Application.Dtos;

namespace Imip.HotelFrontOffice.ReservationDetails;

/// <summary>
/// Simplified DTO for ReservationDetail to avoid circular references
/// Does not include collections that could cause circular references
/// </summary>
public class ReservationDetailBasicDto : AuditedEntityDto<Guid>
{
    public DateTime? CheckInDate { get; set; }
    public DateTime? CheckOutDate { get; set; }
    public string? Rfid { get; set; }
    public decimal Price { get; set; }
    public Guid ReservationId { get; set; }

    // Navigation properties
    public Guid RoomId { get; set; }
    public RoomDto? Room { get; set; }
    public string? RoomNumber { get; set; }

    public Guid GuestId { get; set; }
    public GuestDto? Guest { get; set; }
    public string? GuestName { get; set; }

    public Guid StatusId { get; set; }
    public MasterStatusDto? Status { get; set; }

    public Guid CriteriaId { get; set; }
    public MasterStatusDto? Criteria { get; set; }

    public Guid PaymentStatusId { get; set; }
    public MasterStatusDto? PaymentStatus { get; set; }

    // Use a simplified reservation DTO to avoid circular references
    public ReservationBasicInfoDto? Reservation { get; set; }

    // Intentionally omitting collections to avoid circular references:
    // - ReservationRooms
    // - ReservationFoodAndBeverages
    // - PaymentDetails
}
