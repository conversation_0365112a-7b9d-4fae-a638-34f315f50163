using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace Imip.HotelFrontOffice.Settings;

/// <summary>
/// Interface for the status settings application service
/// </summary>
public interface IStatusSettingsAppService : IApplicationService
{
    /// <summary>
    /// Gets all status settings
    /// </summary>
    /// <returns>The status settings</returns>
    Task<StatusSettingsDto> GetAsync();

    /// <summary>
    /// Updates all status settings
    /// </summary>
    /// <param name="input">The status settings to update</param>
    /// <returns>The updated status settings</returns>
    Task<StatusSettingsDto> UpdateAsync(StatusSettingsDto input);

    /// <summary>
    /// Updates a single setting
    /// </summary>
    /// <param name="input">The setting to update</param>
    /// <returns>True if the setting was updated successfully</returns>
    Task<bool> UpdateSettingAsync(UpdateSettingDto input);
}
