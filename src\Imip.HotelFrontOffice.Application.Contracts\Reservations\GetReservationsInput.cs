﻿using Imip.HotelFrontOffice.Models;
using System;
using System.Collections.Generic;
using FilterGroup = Imip.HotelFrontOffice.Models.FilterGroup;

namespace Imip.HotelFrontOffice.Reservations
{
    /// <summary>
    /// Input DTO for getting reservations with dynamic filtering
    /// </summary>
    public class GetReservationsInput
    {
        /// <summary>
        /// Optional date range filter for arrival date
        /// </summary>
        public DateTime? ArrivalDateFrom { get; set; }

        /// <summary>
        /// Optional date range filter for arrival date
        /// </summary>
        public DateTime? ArrivalDateTo { get; set; }

        /// <summary>
        /// Optional filter for reservation code
        /// </summary>
        public string? ReservationCodeFilter { get; set; }

        /// <summary>
        /// Optional filter for booker name
        /// </summary>
        public string? BookerNameFilter { get; set; }

        /// <summary>
        /// Override to use Models.FilterGroup instead of Common.Filtering.FilterGroup
        /// </summary>
        public new FilterGroup? FilterGroup { get; set; }

        /// <summary>
        /// Override to use Models.SortInfo instead of Common.Filtering.SortItem
        /// </summary>
        public new List<SortInfo>? Sort { get; set; }
    }
}
