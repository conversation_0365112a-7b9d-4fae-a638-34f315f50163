﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.TypeFoodAndBeverages;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.Repositories
{
    public interface ITypeFoodAndBeverageRepository : IRepository<TypeFoodAndBeverage, Guid>
    {
        Task<TypeFoodAndBeverage?> FindByNameAsync(string name);

        Task<List<TypeFoodAndBeverage>> GetActiveFandBsAsync();
    }
}
