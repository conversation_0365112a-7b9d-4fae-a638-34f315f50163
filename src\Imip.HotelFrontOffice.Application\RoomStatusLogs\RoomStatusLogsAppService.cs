﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

using Imip.HotelFrontOffice.RoomStatusLogs;
using Volo.Abp.Application.Dtos;

namespace Imip.HotelFrontOffice.RoomStatusLogs
{
    public class RoomStatusLogsAppService : CrudAppService<
        RoomStatusLog,
        RoomStatusLogsDto,
        Guid,
        PagedAndSortedResultRequestDto,
        CreateUpdateRoomStatusLogsDto,
        CreateUpdateRoomStatusLogsDto
    >, IRoomStatusLogsAppService
    {
        private readonly IRepository<RoomStatusLog, Guid> _repository;
        public RoomStatusLogsAppService(IRepository<RoomStatusLog, Guid> repository)
            : base(repository)
        {
            _repository = repository;
        }
    }
}