﻿using System;
using Imip.HotelFrontOffice.MasterStatuses;
using Imip.HotelFrontOffice.PaymentDetails;
using Imip.HotelFrontOffice.ReservationDetails;
using Imip.HotelFrontOffice.Services;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.HotelFrontOffice.ReservationRooms;

public class ReservationRoom : FullAuditedAggregateRoot<Guid>
{
    public Guid ReservationDetailsId { get; set; }
    public Guid ServiceId { get; set; }
    public Guid? PaymentStatusId { get; set; } // Optional property for payment status ID
    public decimal TotalPrice { get; set; }
    public int Quantity { get; set; }
    public DateTime? TransactionDate { get; set; }

    public virtual ReservationDetail? ReservationDetails { get; set; }
    public virtual Service? Services { get; set; }
    public virtual MasterStatus? PaymentStatus { get; set; }

    // Note: PaymentDetails navigation property removed due to polymorphic relationship

    protected ReservationRoom()
    {
    }

    public ReservationRoom(
        Guid id,
        Guid reservationDetailsId,
        Guid serviceId,
        Guid? paymentStatusId,
        decimal totalPrice,
        int quantity,
        DateTime? transactionDate = null
    )
    {
        Id = id;
        ReservationDetailsId = reservationDetailsId;
        ServiceId = serviceId;
        PaymentStatusId = paymentStatusId;
        TotalPrice = totalPrice;
        Quantity = quantity;
        TransactionDate = transactionDate;
    }
}