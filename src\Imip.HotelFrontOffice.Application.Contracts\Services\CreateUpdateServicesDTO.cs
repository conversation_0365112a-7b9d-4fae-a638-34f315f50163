﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Imip.HotelFrontOffice.Services;

public class CreateUpdateServicesDto
{
    [Required]
    [StringLength(128)]
    public string Name { get; set; } = default!;

    [Required]
    public decimal Price { get; set; } = default!;
    [Required]
    public int UsegeTime { get; set; } = default!;
    public string Information { get; set; } = default!;
    public Guid ServiceTypeId { get; set; } = default!;

    // Dictionary for extra properties
    public Dictionary<string, object>? ExtraProperties { get; set; }
}