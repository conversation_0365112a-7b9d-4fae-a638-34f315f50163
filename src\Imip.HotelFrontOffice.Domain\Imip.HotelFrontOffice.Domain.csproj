﻿<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>Imip.HotelFrontOffice</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Imip.HotelFrontOffice.Domain.Shared\Imip.HotelFrontOffice.Domain.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="SSH.NET" Version="2025.0.0" />
    <PackageReference Include="TickerQ" Version="2.0.0" />
    <PackageReference Include="TickerQ.EntityFrameworkCore" Version="2.0.0" />
    <PackageReference Include="Volo.Abp.Emailing" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.Caching" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.PermissionManagement.Domain.Identity" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.PermissionManagement.Domain.OpenIddict" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.BackgroundJobs.Domain" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.AuditLogging.Domain" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.FeatureManagement.Domain" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.SettingManagement.Domain" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.BlobStoring.Database.Domain" Version="9.0.4" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.OpenIddict.Domain" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.Identity.Domain" Version="9.0.4" />
    <PackageReference Include="Volo.Abp.TenantManagement.Domain" Version="9.0.4" />
  </ItemGroup>

</Project>
