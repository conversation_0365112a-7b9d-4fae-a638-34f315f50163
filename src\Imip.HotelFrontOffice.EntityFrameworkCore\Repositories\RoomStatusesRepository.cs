﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.EntityFrameworkCore;
using Imip.HotelFrontOffice.RoomStatuses;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.HotelFrontOffice.Repositories
{
    public class RoomStatusesRepository : EfCoreRepository<HotelFrontOfficeDbContext, RoomStatus, Guid>, IRoomStatusesRepository
    {
        public RoomStatusesRepository(IDbContextProvider<HotelFrontOfficeDbContext> dbContextProvider)
            : base(dbContextProvider)
        {
        }

        public async Task<List<RoomStatus>> GetActiveAsync() 
        {
            var dbContext = await GetDbContextAsync();
            return await dbContext.RoomStatuses
                .ToListAsync(); 
        }

        public async Task<RoomStatus?> FindByNameAsync(string name)
        {
            var dbContext = await GetDbContextAsync();
            return await dbContext.RoomStatuses
                .Where(x => x.Name == name)
                .FirstOrDefaultAsync(); 
        }
    }
}