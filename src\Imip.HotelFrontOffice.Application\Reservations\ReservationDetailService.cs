﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Attachments;
using Imip.HotelFrontOffice.Guests;
using Imip.HotelFrontOffice.ReservationDetails;
using Imip.HotelFrontOffice.Rooms;
using Imip.HotelFrontOffice.RoomStatuses;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Data;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;
using Volo.Abp.Guids;
using Volo.Abp.Uow;

namespace Imip.HotelFrontOffice.Reservations;

public class ReservationDetailService : DomainService
{
    private readonly IRepository<ReservationDetail, Guid> _reservationDetailsRepository;
    private readonly IRepository<Guest, Guid> _guestRepository;
    private readonly IRepository<Room, Guid> _roomRepository;
    private readonly ILogger<ReservationDetailService> _logger;
    private readonly IGuidGenerator _guidGenerator;
    private readonly IAttachmentAppService _attachmentAppService;

    public ReservationDetailService(
        IRepository<ReservationDetail, Guid> reservationDetailsRepository,
        IRepository<Guest, Guid> guestRepository,
        IRepository<Room, Guid> roomRepository,
        ILogger<ReservationDetailService> logger,
        IGuidGenerator guidGenerator,
        IAttachmentAppService attachmentAppService)
    {
        _reservationDetailsRepository = reservationDetailsRepository;
        _guestRepository = guestRepository;
        _roomRepository = roomRepository;
        _logger = logger;
        _guidGenerator = guidGenerator;
        _attachmentAppService = attachmentAppService;
    }

    [UnitOfWork]
    public virtual async Task UpdateReservationDetailAsync(UpdateReservationDetailDto detailDto)
    {
        try
        {
            // Get the existing reservation detail
            var reservationDetail = await _reservationDetailsRepository.GetAsync(detailDto.Id);
            if (reservationDetail == null)
            {
                throw new EntityNotFoundException(typeof(ReservationDetail), detailDto.Id);
            }

            // Set concurrency stamp for optimistic concurrency control
            if (!string.IsNullOrEmpty(detailDto.ConcurrencyStamp))
            {
                reservationDetail.ConcurrencyStamp = detailDto.ConcurrencyStamp;
            }

            // Update reservation detail properties
            reservationDetail.Rfid = detailDto.Rfid;
            reservationDetail.CriteriaId = detailDto.CriteriaId;
            reservationDetail.Price = detailDto.Price ?? 0;

            // Handle nullable DateTime properties with null checks
            if (detailDto.CheckInDate.HasValue)
            {
                reservationDetail.CheckInDate = detailDto.CheckInDate.Value;
            }
            else
            {
                throw new UserFriendlyException("Check-in date is required", "Error.ReservationDetail.MissingCheckInDate");
            }

            if (detailDto.CheckOutDate.HasValue)
            {
                reservationDetail.CheckOutDate = detailDto.CheckOutDate.Value;
            }
            else
            {
                throw new UserFriendlyException("Check-out date is required", "Error.ReservationDetail.MissingCheckOutDate");
            }

            reservationDetail.RoomId = detailDto.RoomId;
            reservationDetail.GuestId = detailDto.GuestId;

            // Handle nullable StatusId with null check
            if (detailDto.StatusId.HasValue)
            {
                reservationDetail.StatusId = detailDto.StatusId.Value;
            }
            else
            {
                throw new UserFriendlyException("Status is required", "Error.ReservationDetail.MissingStatus");
            }

            // Update the guest information if provided
            if (detailDto.Guest != null)
            {
                await UpdateGuestAsync(detailDto.Guest);
            }

            // Save changes to the reservation detail with autoSave to get a new concurrency stamp
            await _reservationDetailsRepository.UpdateAsync(reservationDetail, autoSave: true);
        }
        catch (AbpDbConcurrencyException ex)
        {
            _logger.LogWarning(ex, "Concurrency exception occurred while updating reservation detail {DetailId}", detailDto.Id);
            throw; // Let the parent method handle this exception
        }
    }

    [UnitOfWork]
    public virtual async Task UpdateGuestAsync(UpdateReservationGuestDto guestDto)
    {
        try
        {
            // Get the existing guest
            var guest = await _guestRepository.GetAsync(guestDto.Id);
            if (guest == null)
            {
                throw new EntityNotFoundException(typeof(Guest), guestDto.Id);
            }

            // Set concurrency stamp for optimistic concurrency control
            if (!string.IsNullOrEmpty(guestDto.ConcurrencyStamp))
            {
                guest.ConcurrencyStamp = guestDto.ConcurrencyStamp;
            }

            // Update guest properties
            guest.Fullname = guestDto.Fullname;
            guest.Email = guestDto.Email;
            guest.PhoneNumber = guestDto.PhoneNumber;
            guest.IdentityNumber = guestDto.IdentityNumber;
            guest.CompanyName = guestDto.CompanyName;
            guest.Nationality = guestDto.Nationality;
            guest.Attachment = guestDto.Attachment;

            // Save changes to the guest with autoSave to get a new concurrency stamp
            await _guestRepository.UpdateAsync(guest, autoSave: true);
        }
        catch (AbpDbConcurrencyException ex)
        {
            _logger.LogWarning(ex, "Concurrency exception occurred while updating guest {GuestId}", guestDto.Id);
            throw; // Let the parent method handle this exception
        }
    }

    [UnitOfWork]
    public virtual async Task<Guid> CreateOrUpdateGuestAsync(CreateReservationGuestDto guestData, Guid? existingGuestId = null)
    {
        try
        {
            Guid finalGuestId;

            if (existingGuestId == null)
            {
                // We need guest data if no existing guest ID is provided
                if (guestData == null)
                {
                    throw new ArgumentNullException(nameof(guestData), "Guest data cannot be null when no existing guest ID is provided");
                }

                // Check if guest already exists
                var guestQuery = await _guestRepository.GetQueryableAsync();
                var existingGuest = await guestQuery
                    .Where(g => g.Fullname == guestData.Fullname &&
                                g.IdentityNumber == guestData.IdentityNumber)
                    .FirstOrDefaultAsync();

                if (existingGuest == null)
                {
                    // Create new guest
                    var newGuest = new Guest(
                        _guidGenerator.Create(),
                        guestData.Fullname,
                        guestData.IdentityNumber,
                        guestData.IdentityNumber,
                        guestData.PhoneNumber,
                        guestData.Email,
                        guestData.Nationality,
                        guestData.CompanyName,
                        string.Empty,
                        guestData.StatusId
                    );

                    await _guestRepository.InsertAsync(newGuest, autoSave: true);
                    finalGuestId = newGuest.Id;
                }
                else
                {
                    // Update existing guest data
                    existingGuest.PhoneNumber = guestData.PhoneNumber;
                    existingGuest.Email = guestData.Email;
                    existingGuest.Nationality = guestData.Nationality;
                    existingGuest.CompanyName = guestData.CompanyName;

                    await _guestRepository.UpdateAsync(existingGuest, autoSave: true);
                    finalGuestId = existingGuest.Id;
                }
            }
            else
            {
                // Use existing guest ID and update their data
                var existingGuest = await _guestRepository.GetAsync(existingGuestId.Value);
                if (existingGuest != null && guestData != null)
                {
                    existingGuest.PhoneNumber = guestData.PhoneNumber;
                    existingGuest.Email = guestData.Email;
                    existingGuest.Nationality = guestData.Nationality;
                    existingGuest.CompanyName = guestData.CompanyName;

                    await _guestRepository.UpdateAsync(existingGuest, autoSave: true);
                }

                finalGuestId = existingGuestId.Value;
            }

            return finalGuestId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating or updating guest: {Message}", ex.Message);
            throw new UserFriendlyException(
                "An error occurred while creating or updating the guest. Please try again.",
                "Error.Guest.CreateUpdateFailed",
                ex.Message);
        }
    }

    [UnitOfWork]
    public virtual async Task<Guid> CreateReservationDetailAsync(CreateReservationDetailDto detailDto, Guid reservationId)
    {
        try
        {
            // Validate that the room can be reserved (has "VR" status)
            if (!await CanRoomBeReservedAsync(detailDto.RoomId))
            {
                throw new UserFriendlyException(
                    "Room cannot be reserved",
                    "Error.ReservationDetail.RoomNotVacantReady",
                    "Only rooms with 'Vacant Ready' status can be reserved");
            }

            // Process guest information
            Guid finalGuestId;
            if (detailDto.GuestData != null)
            {
                finalGuestId = await CreateOrUpdateGuestAsync(detailDto.GuestData, detailDto.GuestId);

                // Process guest attachments if provided
                if (detailDto.GuestData.Attachments != null && detailDto.GuestData.Attachments.Count > 0)
                {
                    await ProcessGuestAttachmentsAsync(detailDto.GuestData.Attachments, finalGuestId, detailDto.GuestData.Fullname);
                }
            }
            else if (detailDto.GuestId.HasValue)
            {
                // If no guest data is provided but a guest ID is, use the existing guest
                finalGuestId = detailDto.GuestId.Value;
            }
            else
            {
                throw new UserFriendlyException(
                    "Guest information is required",
                    "Error.ReservationDetail.MissingGuestInfo",
                    "Either GuestData or GuestId must be provided");
            }

            var guest = await _guestRepository.GetAsync(finalGuestId);
            if (guest == null)
            {
                throw new EntityNotFoundException(typeof(Guest), finalGuestId);
            }

            var room = await _roomRepository.GetAsync(detailDto.RoomId);
            if (room == null)
            {
                throw new EntityNotFoundException(typeof(Room), detailDto.RoomId);
            }

            // Create reservation detail
            var reservationDetailEntity = new ReservationDetail(
                _guidGenerator.Create(),
                detailDto.CheckInDate,
                detailDto.CheckOutDate,
                detailDto.Rfid,
                detailDto.Price ?? 0m,
                reservationId,
                detailDto.RoomId,
                detailDto.StatusId,
                null,
                finalGuestId,
                detailDto.CriteriaId
            );

            await _reservationDetailsRepository.InsertAsync(reservationDetailEntity, autoSave: true);
            return reservationDetailEntity.Id;
        }
        catch (UserFriendlyException)
        {
            // Re-throw user-friendly exceptions as-is
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating reservation detail: {Message}", ex.Message);
            throw new UserFriendlyException(
                "An error occurred while creating the reservation detail. Please try again.",
                "Error.ReservationDetail.CreateFailed",
                ex.Message);
        }
    }

    /// <summary>
    /// Process guest attachments and upload them to the SFTP server
    /// </summary>
    private async Task ProcessGuestAttachmentsAsync(List<GuestAttachmentDto> attachments, Guid guestId, string guestName)
    {
        foreach (var attachment in attachments)
        {
            try
            {
                // Validate the base64 string
                if (string.IsNullOrEmpty(attachment.Base64Content))
                {
                    _logger.LogWarning("Empty base64 content for file {FileName}", attachment.FileName);
                    continue;
                }

                // Validate file type
                if (!IsAllowedFileType(attachment.ContentType))
                {
                    _logger.LogWarning("Invalid file type {ContentType} for file {FileName}",
                        attachment.ContentType, attachment.FileName);
                    continue;
                }

                // Decode the base64 string to a byte array
                byte[] fileBytes;
                try
                {
                    fileBytes = Convert.FromBase64String(attachment.Base64Content);
                }
                catch (FormatException ex)
                {
                    _logger.LogWarning(ex, "Invalid base64 string for file {FileName}", attachment.FileName);
                    continue;
                }

                // Create file upload DTO
                var fileUploadDto = new FileUploadDto
                {
                    Description = attachment.Description ?? $"Guest attachment for {guestName}",
                    ReferenceId = guestId,
                    ReferenceType = "Guest"
                };

                // Upload file
                await _attachmentAppService.UploadFileAsync(
                    fileUploadDto,
                    attachment.FileName,
                    attachment.ContentType,
                    fileBytes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing guest attachment {FileName}: {Message}",
                    attachment.FileName, ex.Message);
                // Continue with other attachments even if one fails
            }
        }
    }

    /// <summary>
    /// Checks if the file type is allowed (PDF or image)
    /// </summary>
    private static bool IsAllowedFileType(string contentType)
    {
        if (string.IsNullOrEmpty(contentType))
            return false;

        contentType = contentType.ToLower();

        // Allow PDF files
        if (contentType == "application/pdf")
            return true;

        // Allow image files
        if (contentType.StartsWith("image/"))
            return true;

        return false;
    }

    /// <summary>
    /// Validates that a room can be reserved by checking if it has the "VR" (Vacant Ready) status code
    /// </summary>
    /// <param name="roomId">The ID of the room to validate</param>
    /// <returns>True if the room can be reserved, false otherwise</returns>
    /// <exception cref="EntityNotFoundException">Thrown if the room is not found</exception>
    private async Task<bool> CanRoomBeReservedAsync(Guid roomId)
    {
        // Get the room with its status
        var room = await _roomRepository.GetAsync(roomId, includeDetails: true);
        if (room == null)
        {
            throw new EntityNotFoundException(typeof(Room), roomId);
        }

        // Load the room status if not already loaded
        if (room.RoomStatus == null && room.RoomStatusId != Guid.Empty)
        {
            var roomStatusRepository = LazyServiceProvider.LazyGetRequiredService<IRepository<RoomStatus, Guid>>();
            room.RoomStatus = await roomStatusRepository.GetAsync(room.RoomStatusId);
        }

        // Check if the room status code is "VR" (Vacant Ready)
        return room.RoomStatus != null && room.RoomStatus.Code == "VR";
    }
}
