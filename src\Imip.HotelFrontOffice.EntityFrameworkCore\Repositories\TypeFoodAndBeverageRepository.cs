﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.EntityFrameworkCore;
using Imip.HotelFrontOffice.TypeFoodAndBeverages;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.HotelFrontOffice.Repositories
{
    public class TypeFoodAndBeverageRepository : EfCoreRepository<HotelFrontOfficeDbContext, TypeFoodAndBeverage, Guid>, ITypeFoodAndBeverageRepository
    {
        public TypeFoodAndBeverageRepository(IDbContextProvider<HotelFrontOfficeDbContext> dbContextProvider)
            : base(dbContextProvider)
        {
        }

        public async Task<List<TypeFoodAndBeverage>> GetActiveFandBsAsync() 
        {
            var dbContext = await GetDbContextAsync();
            return await dbContext.TypeFoodAndBeverages
                .ToListAsync(); 
        }

        public async Task<TypeFoodAndBeverage?> FindByNameAsync(string name)
        {
            var dbContext = await GetDbContextAsync();
            return await dbContext.TypeFoodAndBeverages
                .Where(x => x.Name == name)
                .FirstOrDefaultAsync(); 
        }
    }
}
