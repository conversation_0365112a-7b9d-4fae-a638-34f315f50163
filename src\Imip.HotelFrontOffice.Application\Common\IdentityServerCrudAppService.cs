using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.Common;

/// <summary>
/// Base class for CRUD application services that bypass permission checks
/// and rely on the Identity Server for authorization.
/// </summary>
public abstract class IdentityServerCrudAppService<TEntity, TEntityDto, TKey, TGetListInput, TCreateInput, TUpdateInput> :
    CrudAppService<TEntity, TEntityDto, TKey, TGetListInput, TCreateInput, TUpdateInput>
    where TEntity : class, IEntity<TKey>
    where TEntityDto : IEntityDto<TKey>
    where TGetListInput : PagedAndSortedResultRequestDto
    where TUpdateInput : class
    where TCreateInput : class
{
    protected new readonly ILogger<IdentityServerCrudAppService<TEntity, TEntityDto, TKey, TGetListInput, TCreateInput, TUpdateInput>> Logger;

    protected IdentityServerCrudAppService(
        IRepository<TEntity, TKey> repository,
        ILogger<IdentityServerCrudAppService<TEntity, TEntityDto, TKey, TGetListInput, TCreateInput, TUpdateInput>> logger)
        : base(repository)
    {
        Logger = logger;
    }

    // Override the CheckGetListPolicyAsync method to bypass the standard permission check
    protected override Task CheckGetListPolicyAsync()
    {
        // Skip the permission check since we rely on the class-level [Authorize] attribute
        return Task.CompletedTask;
    }

    // Override the CheckGetPolicyAsync method to bypass the standard permission check
    protected override Task CheckGetPolicyAsync()
    {
        // Skip the permission check since we rely on the class-level [Authorize] attribute
        return Task.CompletedTask;
    }

    // Override the CheckUpdatePolicyAsync method to bypass the standard permission check
    protected override Task CheckUpdatePolicyAsync()
    {
        // Skip the permission check since we rely on the class-level [Authorize] attribute
        return Task.CompletedTask;
    }

    // Override the CheckCreatePolicyAsync method to bypass the standard permission check
    protected override Task CheckCreatePolicyAsync()
    {
        // Skip the permission check since we rely on the class-level [Authorize] attribute
        return Task.CompletedTask;
    }

    // Override the CheckDeletePolicyAsync method to bypass the standard permission check
    protected override Task CheckDeletePolicyAsync()
    {
        // Skip the permission check since we rely on the class-level [Authorize] attribute
        return Task.CompletedTask;
    }
}