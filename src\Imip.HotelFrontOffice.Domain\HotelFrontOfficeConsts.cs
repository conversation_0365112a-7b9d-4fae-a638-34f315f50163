﻿using Volo.Abp.Identity;

namespace Imip.HotelFrontOffice;

public static class HotelFrontOfficeConsts
{
    public const string DbTablePrefix = "App";
    public const string DbTableMasterPrefix = "Master";
    public const string DbTableLogPrefix = "Log";
    public const string? DbSchema = null;
    public const string AdminEmailDefaultValue = IdentityDataSeedContributor.AdminEmailDefaultValue;
    public const string AdminPasswordDefaultValue = IdentityDataSeedContributor.AdminPasswordDefaultValue;
}
