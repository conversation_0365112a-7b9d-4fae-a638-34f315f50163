using System;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.HotelFrontOffice.Taxes;

public class Tax : FullAuditedAggregateRoot<Guid>
{
    // VAT11, VAT12
    public string Code { get; set; } = default!;
    // VAT/PPN/PPH
    public string Name { get; set; } = default!;
    public decimal? Rate { get; set; }
    public DateOnly? StartDate { get; set; } = default!;
    public DateOnly? EndDate { get; set; } = default!;
    public bool IsActive { get; set; } = default!;

    protected Tax()
    {
    }

    public Tax(Guid id, string code, string name, decimal rate, DateOnly startDate, DateOnly endDate, bool isActive)
    {
        Id = id;
        Code = code;
        Name = name;
        Rate = rate;
        StartDate = startDate;
        EndDate = endDate;
        IsActive = isActive;
    }
}