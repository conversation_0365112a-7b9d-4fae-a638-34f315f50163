using Serilog;
using Serilog.Events;

namespace Imip.HotelFrontOffice.Logging;

public static class LoggerExtensions
{
    public static void LogOperation(
        this ILogger logger,
        string operationName,
        string userId,
        string details,
        LogEventLevel level = LogEventLevel.Information)
    {
        logger.Write(level, 
            "{OperationType} operation performed by {UserId}: {Details}",
            operationName,
            userId,
            details);
    }

    public static void LogBusinessEvent(
        this ILogger logger,
        string eventName,
        string entityId,
        string details,
        LogEventLevel level = LogEventLevel.Information)
    {
        logger.Write(level,
            "{BusinessEvent} occurred for {EntityId}: {Details}",
            eventName,
            entityId,
            details);
    }

    public static void LogMetric(
        this ILogger logger,
        string metricName,
        double value,
        string unit = "",
        LogEventLevel level = LogEventLevel.Information)
    {
        logger.Write(level,
            "Metric {MetricName} = {MetricValue} {Unit}",
            metricName,
            value,
            unit);
    }
}