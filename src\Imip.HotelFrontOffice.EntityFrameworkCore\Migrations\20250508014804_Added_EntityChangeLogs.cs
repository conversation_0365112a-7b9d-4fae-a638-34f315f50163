﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Imip.HotelFrontOffice.Migrations
{
    /// <inheritdoc />
    public partial class Added_EntityChangeLogs : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "LogEntityChangeLogs",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ChangeTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ChangeType = table.Column<byte>(type: "tinyint", nullable: false),
                    EntityId = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: false),
                    EntityTenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    EntityTypeFullName = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: false),
                    EntityDisplayName = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: false),
                    EntityDisplayValue = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    AuditLogId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ExtraProperties = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ConcurrencyStamp = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    DeleterId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LogEntityChangeLogs", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "LogEntityPropertyChangeLogs",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    EntityChangeLogId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    PropertyName = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: false),
                    PropertyDisplayName = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: false),
                    OriginalValue = table.Column<string>(type: "nvarchar(512)", maxLength: 512, nullable: false),
                    NewValue = table.Column<string>(type: "nvarchar(512)", maxLength: 512, nullable: false),
                    PropertyTypeFullName = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    DeleterId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LogEntityPropertyChangeLogs", x => x.Id);
                    table.ForeignKey(
                        name: "FK_LogEntityPropertyChangeLogs_LogEntityChangeLogs_EntityChangeLogId",
                        column: x => x.EntityChangeLogId,
                        principalTable: "LogEntityChangeLogs",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_LogEntityChangeLogs_AuditLogId",
                table: "LogEntityChangeLogs",
                column: "AuditLogId");

            migrationBuilder.CreateIndex(
                name: "IX_LogEntityChangeLogs_ChangeTime",
                table: "LogEntityChangeLogs",
                column: "ChangeTime");

            migrationBuilder.CreateIndex(
                name: "IX_LogEntityChangeLogs_ChangeType",
                table: "LogEntityChangeLogs",
                column: "ChangeType");

            migrationBuilder.CreateIndex(
                name: "IX_LogEntityChangeLogs_EntityId",
                table: "LogEntityChangeLogs",
                column: "EntityId");

            migrationBuilder.CreateIndex(
                name: "IX_LogEntityChangeLogs_EntityTypeFullName",
                table: "LogEntityChangeLogs",
                column: "EntityTypeFullName");

            migrationBuilder.CreateIndex(
                name: "IX_LogEntityPropertyChangeLogs_EntityChangeLogId",
                table: "LogEntityPropertyChangeLogs",
                column: "EntityChangeLogId");

            migrationBuilder.CreateIndex(
                name: "IX_LogEntityPropertyChangeLogs_PropertyName",
                table: "LogEntityPropertyChangeLogs",
                column: "PropertyName");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "LogEntityPropertyChangeLogs");

            migrationBuilder.DropTable(
                name: "LogEntityChangeLogs");
        }
    }
}
