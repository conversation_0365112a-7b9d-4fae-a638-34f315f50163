using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Models;
using Imip.HotelFrontOffice.Permissions;
using Imip.HotelFrontOffice.ReservationDetails;
using Imip.HotelFrontOffice.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.Controllers.ReservationDetails;

/// <summary>
/// Custom API controller for advanced ReservationDetails operations
/// </summary>
[Route("api/app/reservation-details")]
[RemoteService]
public class ReservationDetailsController : HotelFrontOfficeController
{
    private readonly IRepository<ReservationDetail, Guid> _repository;
    private readonly ILogger<ReservationDetailsController> _logger;

    public ReservationDetailsController(
        IRepository<ReservationDetail, Guid> repository,
        ILogger<ReservationDetailsController> logger)
    {
        _repository = repository;
        _logger = logger;
    }

    /// <summary>
    /// Get a paged list of reservation details with dynamic filtering and sorting
    /// </summary>
    /// <param name="input">Query parameters including filtering and sorting</param>
    /// <returns>Paged list of reservation details in standard ABP format</returns>
    [HttpPost("list")]
    [Authorize(WismaAppPermissions.PolicyPaymentDetails.View)]
    [ProducesResponseType(typeof(PagedResultDto<ReservationDetailsDto>), StatusCodes.Status200OK)]
    public virtual async Task<PagedResultDto<ReservationDetailsDto>> GetPagedListAsync(QueryParameters input)
    {
        try
        {
            // Get the base query with includes
            var query = await ExecuteDynamicQueryAsync(input);

            // Get total count before paging
            var totalCount = await query.CountAsync();

            // Apply paging
            var items = await query
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount)
                .ToListAsync();

            // Map to DTOs
            var dtos = ObjectMapper.Map<List<ReservationDetail>, List<ReservationDetailsDto>>(items);

            // Return standard ABP paged result
            return new PagedResultDto<ReservationDetailsDto>
            {
                TotalCount = totalCount,
                Items = dtos
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting paged list of reservation details: {Message}", ex.Message);
            throw new UserFriendlyException(
                "Could not retrieve reservation details list",
                "Error.ReservationDetailsList.Failed",
                ex.Message);
        }
    }

    /// <summary>
    /// Execute dynamic query with filtering and sorting
    /// </summary>
    protected virtual async Task<IQueryable<ReservationDetail>> ExecuteDynamicQueryAsync(QueryParameters parameters)
    {
        // Get base query with includes
        var query = await _repository.GetQueryableAsync();

        // Add includes for related entities
        query = query
            .AsNoTracking()
            .Include(x => x.Reservation)
            .Include(x => x.Criteria)
            .Include(x => x.Room!)
                .ThenInclude(r => r.RoomType)
            .Include(x => x.Room!)
                .ThenInclude(r => r.RoomStatus)
            .Include(x => x.Guest)
            .Include(x => x.Status)
            .Include(x => x.ReservationFoodAndBeverages)
                .ThenInclude(rf => rf.FoodAndBeverage)
            .Include(x => x.ReservationRooms)
                .ThenInclude(rr => rr.Services)
            .Include(x => x.PaymentStatus);

        // Check if we need to include deeper relationships based on filter or sort fields
        var fieldsToCheck = new List<string>();

        // Add filter fields
        if (parameters.FilterGroup?.Conditions != null)
        {
            fieldsToCheck.AddRange(parameters.FilterGroup.Conditions
                .Where(c => c.FieldName.Contains('.'))
                .Select(c => c.FieldName));
        }

        // Add sort fields
        if (parameters.Sort?.Count > 0)
        {
            fieldsToCheck.AddRange(parameters.Sort
                .Where(s => s.Field.Contains('.'))
                .Select(s => s.Field));
        }

        // Include room details if needed
        if (fieldsToCheck.Any(f => f.StartsWith("room.")))
        {
            query = query.Include(x => x.Room!)
                .ThenInclude(r => r.RoomType!);
            query = query.Include(x => x.Room!)
                .ThenInclude(r => r.RoomStatus!);
        }

        // Include reservation details if needed
        if (fieldsToCheck.Any(f => f.StartsWith("reservation.")))
        {
            query = query.Include("Reservation.ReservationType");
            query = query.Include("Reservation.Company");
            query = query.Include("Reservation.Status");
        }

        // Include reservation rooms if needed
        if (fieldsToCheck.Any(f => f.StartsWith("reservationRooms.")))
        {
            query = query.Include("ReservationRooms");

            if (fieldsToCheck.Any(f => f.Contains("reservationRooms.services")))
            {
                query = query.Include("ReservationRooms.Services");
                query = query.Include("ReservationRooms.Services.ServiceType");
            }
        }

        // Include food and beverages if needed
        if (fieldsToCheck.Any(f => f.StartsWith("reservationFoodAndBeverages.")))
        {
            query = query.Include("ReservationFoodAndBeverages");

            if (fieldsToCheck.Any(f => f.Contains("reservationFoodAndBeverages.foodAndBeverage")))
            {
                query = query.Include("ReservationFoodAndBeverages.FoodAndBeverage");
                query = query.Include("ReservationFoodAndBeverages.FoodAndBeverage.TypeFoodAndBeverage");
            }
        }

        // Apply filters
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<ReservationDetail>.ApplyFilters(query, parameters.FilterGroup);
        }

        // Apply sorting
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<ReservationDetail>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            // Use the default sorting if provided using Dynamic LINQ
            query = query.OrderBy(parameters.Sorting);
        }
        else
        {
            // Default sorting by creation time descending
            query = query.OrderByDescending(x => x.CreationTime);
        }

        return query;
    }
}
