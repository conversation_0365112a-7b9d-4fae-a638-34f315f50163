﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Imip.HotelFrontOffice.ReservationRooms;

public class CreateUpdateReservationRoomsDto
{
    // Id property is required for bulk updates
    public Guid Id { get; set; }

    [Required]
    public Guid ReservationDetailsId { get; set; } = default!;

    [Required]
    public Guid ServiceId { get; set; } = default!;
    public Guid? PaymentStatusId { get; set; } = default!; // Optional property for payment status ID
    public decimal TotalPrice { get; set; } = default!;
    public int Quantity { get; set; } = default!;
    public DateTime? TransactionDate { get; set; } = default!;

}