using System;
using AutoMapper;
using Imip.HotelFrontOffice.Companies;
using Imip.HotelFrontOffice.DiningOptions;
using Imip.HotelFrontOffice.EntityChangeLogs;
using Imip.HotelFrontOffice.EntityChangeLogs.Dtos;
using Imip.HotelFrontOffice.FoodAndBeverages;
using Imip.HotelFrontOffice.Guests;
using Imip.HotelFrontOffice.Master.Company;
using Imip.HotelFrontOffice.MasterStatuses;
using Imip.HotelFrontOffice.PaymentDetails;
using Imip.HotelFrontOffice.PaymentGuests;
using Imip.HotelFrontOffice.PaymentMethods;
using Imip.HotelFrontOffice.Payments;
using Imip.HotelFrontOffice.Reports;
using Imip.HotelFrontOffice.ReservationDetails;
using Imip.HotelFrontOffice.ReservationFoodAndBeverages;
using Imip.HotelFrontOffice.ReservationRooms;
using Imip.HotelFrontOffice.Reservations;
using Imip.HotelFrontOffice.ReservationTypes;
using Imip.HotelFrontOffice.Rooms;
using Imip.HotelFrontOffice.RoomStatuses;
using Imip.HotelFrontOffice.RoomStatusLogs;
using Imip.HotelFrontOffice.RoomTypes;
using Imip.HotelFrontOffice.Services;
using Imip.HotelFrontOffice.ServiceTypes;
using Imip.HotelFrontOffice.Taxes;
using Imip.HotelFrontOffice.TypeFoodAndBeverages;
using Volo.Abp.AuditLogging;


namespace Imip.HotelFrontOffice;

public class HotelFrontOfficeApplicationAutoMapperProfile : Profile
{
    public HotelFrontOfficeApplicationAutoMapperProfile()
    {
        CreateMap<TypeFoodAndBeverage, TypeFoodAndBeverageDto>()
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status));
        CreateMap<CreateUpdateTypeFoodAndBeverageDto, TypeFoodAndBeverage>();

        CreateMap<Company, CompanyDto>();
        CreateMap<CreateUpdateCompanyDto, Company>();

        CreateMap<DiningOption, DiningOptionsDto>();
        CreateMap<CreateUpdateDiningOptionsDto, DiningOption>();

        CreateMap<MasterStatus, MasterStatusDto>();
        CreateMap<CreateUpdateMasterStatusDto, MasterStatus>();

        CreateMap<PaymentMethod, PaymentMethodDto>();
        CreateMap<CreateUpdatePaymentMethodDto, PaymentMethod>();

        CreateMap<FoodAndBeverage, FoodAndBeverageDto>()
            .ForMember(dest => dest.TypeFoodAndBeverage, opt => opt.MapFrom(src => src.TypeFoodAndBeverage))
            .ForMember(dest => dest.TypeFoodAndBeverageName,
                      opt => opt.MapFrom(src => src.TypeFoodAndBeverage.Name))
            .ForMember(dest => dest.ExtraProperties, opt => opt.MapFrom(src => src.ExtraProperties));
        CreateMap<CreateUpdateFoodAndBeverageDto, FoodAndBeverage>()
            .ForMember(dest => dest.ExtraProperties, opt => opt.Condition(src => src.ExtraProperties != null))
            .ForMember(dest => dest.ExtraProperties, opt => opt.MapFrom(src => src.ExtraProperties));

        CreateMap<RoomType, RoomTypeDto>()
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status)); ;
        CreateMap<CreateUpdateRoomTypeDto, RoomType>();

        CreateMap<Room, RoomDto>()
            .ForMember(dest => dest.RoomType, opt => opt.MapFrom(src => src.RoomType))
            .ForMember(dest => dest.RoomStatus, opt => opt.MapFrom(src => src.RoomStatus))
            .ForMember(dest => dest.ExtraProperties, opt => opt.MapFrom(src => src.ExtraProperties))
            .AfterMap((src, dest) =>
            {
                // Ensure RoomType is not null in the DTO
                if (dest.RoomType == null && src.RoomTypeId != Guid.Empty)
                {
                    dest.RoomType = new RoomTypeDto { Id = src.RoomTypeId };
                }

                // Ensure RoomStatus is not null in the DTO
                if (dest.RoomStatus == null && src.RoomStatusId != Guid.Empty)
                {
                    dest.RoomStatus = new RoomStatusDto { Id = src.RoomStatusId };
                }
            });
        CreateMap<CreateUpdateRoomDto, Room>()
            .ForMember(dest => dest.ExtraProperties, opt => opt.Condition(src => src.ExtraProperties != null))
            .ForMember(dest => dest.ExtraProperties, opt => opt.MapFrom(src => src.ExtraProperties));

        CreateMap<RoomStatus, RoomStatusDto>();
        CreateMap<CreateUpdateRoomStatusDto, RoomStatus>();


        CreateMap<ServiceType, ServiceTypesDto>()
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status));
        CreateMap<CreateUpdateServiceTypesDto, ServiceType>();

        CreateMap<Service, ServicesDto>()
            .ForMember(dest => dest.ServiceTypeName,
                      opt => opt.MapFrom(src => src.ServiceType != null ? src.ServiceType.Name : string.Empty))
            .ForMember(dest => dest.ExtraProperties, opt => opt.MapFrom(src => src.ExtraProperties));
        CreateMap<CreateUpdateServicesDto, Service>()
            .ForMember(dest => dest.ExtraProperties, opt => opt.Condition(src => src.ExtraProperties != null))
            .ForMember(dest => dest.ExtraProperties, opt => opt.MapFrom(src => src.ExtraProperties));

        CreateMap<Guest, GuestDto>()
        .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status));
        CreateMap<CreateUpdateGuestDto, Guest>();

        CreateMap<ReservationType, ReservationTypesDto>();
        CreateMap<CreateUpdateReservationTypesDto, ReservationType>();

        // Disable direct mapping from UpdateReservationGuestDto to Entities.Guest to prevent duplicate key issues
        // We're handling this manually in the UpdateReservationDetailAsync method
        // CreateMap<UpdateReservationGuestDto, Entities.Guest>();

        CreateMap<Reservation, ReservationsDto>()
            /*.ForMember(dest => dest.ReservationTypeName,
                opt => opt.MapFrom(src => src.ReservationTypes != null ? src.ReservationTypes.Name : string.Empty))*/
            .ForMember(dest => dest.ReservationTypes, opt => opt.MapFrom(src => src.ReservationType))
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status))
            .ForMember(dest => dest.Company, opt => opt.MapFrom(src => src.Company))
            .ForMember(dest => dest.DiningOptions, opt => opt.MapFrom(src => src.DiningOptions))
            .ForMember(dest => dest.PaymentMethod, opt => opt.MapFrom(src => src.PaymentMethod))
            .ForMember(dest => dest.ReservationDetails, opt => opt.MapFrom(src => src.ReservationDetails));

        // Add mapping for ReservationBasicInfoDto to avoid circular references
        CreateMap<Reservation, ReservationBasicInfoDto>()
            .ForMember(dest => dest.ReservationTypes, opt => opt.MapFrom(src => src.ReservationType))
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status))
            .ForMember(dest => dest.Company, opt => opt.MapFrom(src => src.Company));

        CreateMap<CreateUpdateReservationsDto, Reservation>()
            .ForMember(dest => dest.ReservationDetails, opt => opt.Ignore()); // We'll handle this manually in the service

        // Update the mapping for CreateReservationWithDetailsDto to Entities.Reservations
        CreateMap<CreateReservationWithDetailsDto, Reservation>()
            .ForMember(dest => dest.ReservationDetails, opt => opt.Ignore()); // We'll handle this manually in the service

        CreateMap<UpdateReservationWithDetailsDto, Reservation>()
            .ForMember(dest => dest.ReservationDetails, opt => opt.Ignore()); // We'll handle this manually in the service


        CreateMap<ReservationDetail, ReservationDetailsDto>()
            .ForMember(dest => dest.Room, opt => opt.MapFrom(src => src.Room))
            .ForMember(dest => dest.Guest, opt => opt.MapFrom(src => src.Guest))
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status))
            .ForMember(dest => dest.Criteria, opt => opt.MapFrom(src => src.Criteria))
            .ForMember(dest => dest.PaymentStatus, opt => opt.MapFrom(src => src.PaymentStatus))
            .ForMember(dest => dest.ReservationRooms, opt => opt.MapFrom(src => src.ReservationRooms))
            .ForMember(dest => dest.Reservation, opt => opt.MapFrom(src => src.Reservation))
            .ForMember(dest => dest.PaymentDetails, opt => opt.MapFrom(src => src.PaymentDetails))
            .ForMember(dest => dest.ReservationFoodAndBeverages, opt => opt.MapFrom(src => src.ReservationFoodAndBeverages));

        CreateMap<CreateUpdateReservationDetailsDto, ReservationDetail>();
        // Add mapping for CreateReservationDetailDto to Entities.ReservationDetails
        CreateMap<CreateReservationDetailDto, ReservationDetail>()
            .ForMember(dest => dest.Id, opt => opt.Ignore()) // ID will be generated in the service
            .ForMember(dest => dest.ReservationId, opt => opt.Ignore()); // ReservationId will be set in the service

        CreateMap<UpdateReservationDetailDto, ReservationDetail>();


        CreateMap<ReservationRoom, ReservationRoomsDto>()
            .ForMember(dest => dest.PaymentStatus, opt => opt.MapFrom(src => src.PaymentStatus))
            .ForMember(dest => dest.Services, opt => opt.MapFrom(src => src.Services))
            .ForMember(dest => dest.ServiceName,
                opt => opt.MapFrom(src => src.Services != null ? src.Services.Name : string.Empty))
            .ForMember(dest => dest.ServiceTypeName,
                opt => opt.MapFrom(src => src.Services != null && src.Services.ServiceType != null ? src.Services.ServiceType.Name : string.Empty));

        CreateMap<CreateUpdateReservationRoomsDto, ReservationRoom>();


        CreateMap<ReservationFoodAndBeverage, ReservationFoodAndBeveragesDto>()
            .ForMember(dest => dest.PaymentStatus, opt => opt.MapFrom(src => src.PaymentStatus))
            .ForMember(dest => dest.FoodAndBeverage, opt => opt.MapFrom(src => src.FoodAndBeverage))
            .ForMember(dest => dest.FoodAndBeverageName,
                opt => opt.MapFrom(src => src.FoodAndBeverage != null ? src.FoodAndBeverage.Name : string.Empty))
            .ForMember(dest => dest.FoodAndBeverageTypeName,
                opt => opt.MapFrom(src => src.FoodAndBeverage != null && src.FoodAndBeverage.TypeFoodAndBeverage != null ? src.FoodAndBeverage.TypeFoodAndBeverage.Name : string.Empty));

        CreateMap<CreateUpdateReservationFoodAndBeveragesDto, ReservationFoodAndBeverage>();

        //CreateMap<Entities.Payments, PaymentsDTO>();
        CreateMap<Payment, PaymentsDto>()
            .ForMember(dest => dest.ReservationCode,
                opt => opt.MapFrom(src => src.Reservations != null ? src.Reservations.ReservationCode : string.Empty))
            .ForMember(dest => dest.BookerName,
                opt => opt.MapFrom(src => src.Reservations != null ? src.Reservations.BookerName : string.Empty))
            /*.ForMember(dest => dest.GuestName,
                opt => opt.MapFrom(src => src.ReservationDetails != null && src.ReservationDetails.Guest != null ?
                    src.ReservationDetails.Guest.Fullname : string.Empty))*/
            .ForMember(dest => dest.PaymentDetails,
                opt => opt.MapFrom(src => src.PaymentDetails));

        CreateMap<CreateUpdatePaymentsDto, Payment>()
            .ForMember(dest => dest.Id, opt => opt.Ignore()) // ID will be generated in the service
            .ForMember(dest => dest.PaymentDetails, opt => opt.Ignore()); // Ignore PaymentDetails to avoid duplicate entries

        CreateMap<PaymentGuest, PaymentGuestsDto>();
        CreateMap<CreateUpdatePaymentGuestsDto, PaymentGuest>();

        CreateMap<Tax, TaxDto>();
        CreateMap<CreateUpdateTaxDto, Tax>();

        CreateMap<Report, ReportDto>();
        CreateMap<CreateUpdateReportDto, Report>();

        // Map ReservationDetail to ReservationDetailBasicDto to avoid circular references
        CreateMap<ReservationDetail, ReservationDetailBasicDto>()
            .ForMember(dest => dest.Room, opt => opt.MapFrom(src => src.Room))
            .ForMember(dest => dest.Criteria, opt => opt.MapFrom(src => src.Criteria))
            .ForMember(dest => dest.Guest, opt => opt.MapFrom(src => src.Guest))
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status))
            .ForMember(dest => dest.PaymentStatus, opt => opt.MapFrom(src => src.PaymentStatus))
            .ForMember(dest => dest.Reservation, opt => opt.MapFrom(src => src.Reservation));

        CreateMap<PaymentDetail, PaymentDetailsDto>()
            .ForMember(dest => dest.SourceType, opt => opt.MapFrom(src => src.SourceType ?? PaymentSourceType.ReservationRoom))
            .ForMember(dest => dest.ReservationDetails, opt => opt.MapFrom(src => src.ReservationDetails))
            .ForMember(dest => dest.ReservationFoodAndBeverages, opt => opt.MapFrom(src => src.ReservationFoodAndBeverages))
            .ForMember(dest => dest.ReservationRoom, opt => opt.MapFrom(src => src.ReservationRoom));

        // Simple DTO mapping to prevent circular references
        CreateMap<PaymentDetail, PaymentDetailSimpleDto>()
            .ForMember(dest => dest.SourceType, opt => opt.MapFrom(src => src.SourceType ?? PaymentSourceType.ReservationRoom))
            .ForMember(dest => dest.PaymentCode, opt => opt.MapFrom(src => src.Payments != null ? src.Payments.PaymentCode : string.Empty))
            .ForMember(dest => dest.ReservationCode, opt => opt.MapFrom(src => src.ReservationDetails != null && src.ReservationDetails.Reservation != null ? src.ReservationDetails.Reservation.ReservationCode : string.Empty))
            .ForMember(dest => dest.GuestName, opt => opt.MapFrom(src => src.ReservationDetails != null && src.ReservationDetails.Guest != null ? src.ReservationDetails.Guest.Fullname : string.Empty))
            .ForMember(dest => dest.RoomNumber, opt => opt.MapFrom(src => src.ReservationDetails != null && src.ReservationDetails.Room != null ? src.ReservationDetails.Room.RoomNumber : string.Empty))
            .ForMember(dest => dest.StatusName, opt => opt.MapFrom(src => src.ReservationDetails != null && src.ReservationDetails.Status != null ? src.ReservationDetails.Status.Name : string.Empty));

        CreateMap<CreateUpdatePaymentDetailsDto, PaymentDetail>();

        CreateMap<RoomStatusLog, RoomStatusLogsDto>();
        CreateMap<CreateUpdateRoomStatusLogsDto, RoomStatusLog>();

        // Entity Change Log mappings
        CreateMap<EntityChangeLog, EntityChangeLogDto>()
            .ForMember(dest => dest.UserName, opt => opt.Ignore()); // This will be set in the service
        CreateMap<EntityPropertyChangeLog, EntityPropertyChangeLogDto>();
        CreateMap<EntityChangeType, EntityChangeTypeDto>();

        // ABP Audit Log mappings
        CreateMap<EntityChange, EntityChangeLogDto>()
            .ForMember(dest => dest.EntityDisplayName, opt => opt.MapFrom(src => GetEntityDisplayName(src.EntityTypeFullName)))
            .ForMember(dest => dest.EntityDisplayValue, opt => opt.MapFrom(src => src.EntityId))
            .ForMember(dest => dest.UserName, opt => opt.Ignore())
            .ForMember(dest => dest.AuditLogId, opt => opt.MapFrom(src => src.AuditLogId))
            .ForMember(dest => dest.AuditLog, opt => opt.Ignore()) // Will be set in service
            .ForMember(dest => dest.AuditLogActions, opt => opt.Ignore()); // Will be set in service

        CreateMap<EntityPropertyChange, EntityPropertyChangeLogDto>()
            .ForMember(dest => dest.EntityChangeLogId, opt => opt.MapFrom(src => src.EntityChangeId))
            .ForMember(dest => dest.PropertyDisplayName, opt => opt.MapFrom(src => src.PropertyName));

        // Audit Log with Details mappings
        CreateMap<AuditLog, AuditLogWithDetailsDto>()
            .ForMember(dest => dest.Actions, opt => opt.MapFrom(src => src.Actions))
            .ForMember(dest => dest.EntityChanges, opt => opt.MapFrom(src => src.EntityChanges));

        CreateMap<AuditLogAction, AuditLogActionDto>();

        // New mappings for enhanced EntityChangeLogDto with audit log information
        CreateMap<AuditLog, AuditLogInfoDto>();
        CreateMap<AuditLogAction, AuditLogActionInfoDto>();
    }

    /// <summary>
    /// Gets a user-friendly display name for an entity type
    /// </summary>
    private static string GetEntityDisplayName(string entityTypeFullName)
    {
        if (string.IsNullOrEmpty(entityTypeFullName))
        {
            return "Unknown";
        }

        // Extract the class name from the full type name
        var lastDotIndex = entityTypeFullName.LastIndexOf('.');
        var className = lastDotIndex >= 0 && lastDotIndex < entityTypeFullName.Length - 1
            ? entityTypeFullName.Substring(lastDotIndex + 1)
            : entityTypeFullName;

        // Add spaces before capital letters
        var result = string.Empty;
        for (var i = 0; i < className.Length; i++)
        {
            var c = className[i];
            if (i > 0 && char.IsUpper(c))
            {
                result += " ";
            }
            result += c;
        }

        return result;
    }
}
