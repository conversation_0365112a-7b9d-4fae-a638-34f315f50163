using System.Threading.Tasks;
using Imip.HotelFrontOffice.Permissions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp;
using Volo.Abp.AspNetCore.Mvc;

namespace Imip.HotelFrontOffice.Settings;

[RemoteService]
[Route("api/status-settings")]
[Authorize(WismaAppPermissions.PolicySettings.Default)]
public class StatusSettingsController : AbpControllerBase
{
    private readonly IStatusSettingsAppService _statusSettingsAppService;

    public StatusSettingsController(IStatusSettingsAppService statusSettingsAppService)
    {
        _statusSettingsAppService = statusSettingsAppService;
    }

    /// <summary>
    /// Gets all status settings
    /// </summary>
    /// <returns>The status settings</returns>
    [HttpGet]
    [Authorize(WismaAppPermissions.PolicySettings.View)]
    public async Task<StatusSettingsDto> GetAsync()
    {
        return await _statusSettingsAppService.GetAsync();
    }

    /// <summary>
    /// Updates all status settings
    /// </summary>
    /// <param name="input">The status settings to update</param>
    /// <returns>The updated status settings</returns>
    [HttpPut]
    [Authorize(WismaAppPermissions.PolicySettings.Edit)]
    public async Task<StatusSettingsDto> UpdateAsync(StatusSettingsDto input)
    {
        return await _statusSettingsAppService.UpdateAsync(input);
    }

    /// <summary>
    /// Updates a single setting
    /// </summary>
    /// <param name="input">The setting to update</param>
    /// <returns>True if the setting was updated successfully</returns>
    [HttpPatch]
    [Authorize(WismaAppPermissions.PolicySettings.Edit)]
    public async Task<bool> UpdateSettingAsync(UpdateSettingDto input)
    {
        return await _statusSettingsAppService.UpdateSettingAsync(input);
    }
}
