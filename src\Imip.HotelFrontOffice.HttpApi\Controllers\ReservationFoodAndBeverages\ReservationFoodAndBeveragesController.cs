using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Models;
using Imip.HotelFrontOffice.Permissions;
using Imip.HotelFrontOffice.ReservationFoodAndBeverages;
using Imip.HotelFrontOffice.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.Controllers.ReservationFoodAndBeverages;

[Route("api/app/reservation-food-and-beverages")]
[RemoteService]
public class ReservationFoodAndBeveragesController : HotelFrontOfficeController
{
    private readonly IReservationFoodAndBeveragesAppService _reservationFoodAndBeveragesAppService;
    private readonly IRepository<ReservationFoodAndBeverage> _repository;
    private readonly ILogger<ReservationFoodAndBeveragesController> _logger;

    public ReservationFoodAndBeveragesController(
        IReservationFoodAndBeveragesAppService reservationFoodAndBeveragesAppService,
        IRepository<ReservationFoodAndBeverage> repository,
        ILogger<ReservationFoodAndBeveragesController> logger)
    {
        _reservationFoodAndBeveragesAppService = reservationFoodAndBeveragesAppService;
        _repository = repository;
        _logger = logger;
    }

    /// <summary>
    /// Get a paged list of reports with dynamic filtering and sorting
    /// </summary>
    /// <param name="input">Query parameters including filtering and sorting</param>
    /// <returns>Paged list of reports in standard ABP format</returns>
    [HttpPost("list")]
    [Authorize(WismaAppPermissions.PolicyReservationFoodAndBeverages.View)]
    [ProducesResponseType(typeof(PagedResultDto<ReservationFoodAndBeveragesDto>), StatusCodes.Status200OK)]
    public virtual async Task<PagedResultDto<ReservationFoodAndBeveragesDto>> GetPagedListAsync(QueryParameters input)
    {
        try
        {
            // Get the base query with includes
            var query = await ExecuteDynamicQueryAsync(input);

            // Get total count before paging
            var totalCount = await query.CountAsync();

            var items = await query
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount)
                .ToListAsync();

            // Map to DTOs
            var dtos = ObjectMapper.Map<List<ReservationFoodAndBeverage>, List<ReservationFoodAndBeveragesDto>>(items);

            // Return a standard ABP paged result
            return new PagedResultDto<ReservationFoodAndBeveragesDto>
            {
                TotalCount = totalCount,
                Items = dtos
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting paged list of reports: {Message}", ex.Message);
            throw new UserFriendlyException(
                "Could not retrieve reports list",
                "Error.ReportsList.Failed",
                ex.Message);
        }
    }

    /// <summary>
    /// Execute dynamic query with filtering and sorting
    /// </summary>
    protected virtual async Task<IQueryable<ReservationFoodAndBeverage>> ExecuteDynamicQueryAsync(QueryParameters parameters)
    {
        // Get base query with includes
        var query = await _repository.GetQueryableAsync();

        // Add includes for related entities if needed
        query = query.AsNoTracking()
            // .Include(x => x.ReservationDetails)
            .Include(x => x.FoodAndBeverage!)
                .ThenInclude(x => x.TypeFoodAndBeverage!)
            .Include(x => x.PaymentStatus);

        // Check if we need to include deeper relationships based on filter or sort fields
        var fieldsToCheck = new List<string>();

        // Add filter fields
        if (parameters.FilterGroup?.Conditions != null)
        {
            fieldsToCheck.AddRange(parameters.FilterGroup.Conditions
                .Where(c => c.FieldName.Contains('.'))
                .Select(c => c.FieldName));
        }

        // Add sort fields
        if (parameters.Sort?.Count > 0)
        {
            fieldsToCheck.AddRange(parameters.Sort
                .Where(s => s.Field.Contains('.'))
                .Select(s => s.Field));
        }

        // Apply filters
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<ReservationFoodAndBeverage>.ApplyFilters(query, parameters.FilterGroup);
        }

        // Apply sorting
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<ReservationFoodAndBeverage>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            // Use the default sorting if provided
            var sortDesc = parameters.Sorting.StartsWith('-');
            var sortField = sortDesc ? parameters.Sorting[1..] : parameters.Sorting;
            query = DynamicQueryBuilder<ReservationFoodAndBeverage>.ApplySorting(query, sortField, sortDesc);
        }
        else
        {
            // Default sorting by name
            query = query.OrderBy(x => x.Id);
        }

        return query;
    }
}
