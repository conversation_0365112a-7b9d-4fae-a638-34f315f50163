﻿using System;
using System.Collections.Generic;
using Imip.HotelFrontOffice.TypeFoodAndBeverages;
using Volo.Abp.Application.Dtos;

namespace Imip.HotelFrontOffice.FoodAndBeverages;

public class FoodAndBeverageDto : AuditedEntityDto<Guid>
{
    public string Name { get; set; } = default!;
    public decimal Price { get; set; } = 0!;
    public string Information { get; set; } = default!;
    public Guid TypeFoodAndBeverageId { get; set; } = default!;
    public string TypeFoodAndBeverageName { get; set; } = default!;

    public TypeFoodAndBeverageDto? TypeFoodAndBeverage { get; set; } = default!;

    // Dictionary for extra properties
    public Dictionary<string, object> ExtraProperties { get; set; } = new Dictionary<string, object>();
}